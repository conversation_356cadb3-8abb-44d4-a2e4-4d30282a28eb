<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="stock.ReceptionReportTable">
        <thead>
            <tr class="bg-light">
                <th>
                    <i t-if="props.source[0].priority == '1'" class="o_priority o_priority_star fa fa-star"/>
                    <a href="#" t-on-click.prevent="() => this.onClickLink(props.source[0].model, props.source[0].id, 'form')" t-out="props.source[0].name"/>
                    <span t-if="props.source.length > 1">
                        (<a href="#" t-on-click.prevent="() => this.onClickLink(props.source[1].model, props.source[1].id, 'form')" t-out="props.source[1].name"/>)
                    </span>
                    <span t-if="props.source[0].model == 'stock.picking' and props.source[0].partner_id">:
                        <a href="#" t-on-click.prevent="() => this.onClickLink('res.partner', props.source[0].partner_id, 'form')" t-out="props.source[0].partner_name"/>
                    </span>
                </th>
                <th>Expected Delivery: <t t-esc="props.scheduledDate"/></th>
                <th t-if="hasMovesIn">
                    <button t-if="hasAssignAllButton" t-on-click="onClickAssignAll" class="btn btn-sm btn-primary" t-att-disabled="isAssignAllDisabled" name="assign_source_link">
                        Assign All
                    </button>
                </th>
                <th>
                    <button t-if="hasMovesIn" t-on-click="onClickPrintLabels" class="btn btn-sm btn-primary" t-att-disabled="isPrintLabelDisabled" name="print_labels">
                        <span class="d-none d-sm-block">Print Labels</span>
                        <span class="d-block d-sm-none fa fa-print"/>
                    </button>
                </th>
            </tr>
        </thead>
        <tbody>
            <t t-foreach="props.lines" t-as="line" t-key="line.index">
                <ReceptionReportLine
                    data="line"
                    labelReport="props.labelReport"
                    parentIndex="props.index"
                    showUom="props.showUom"
                    precision="props.precision"/>
            </t>
        </tbody>
    </t>

</templates>
