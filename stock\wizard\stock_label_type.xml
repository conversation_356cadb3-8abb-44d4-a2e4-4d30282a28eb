<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="picking_label_type_form" model="ir.ui.view">
        <field name="name">picking.label.type.form</field>
        <field name="model">picking.label.type</field>
        <field name="mode">primary</field>
        <field name="priority">25</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="label_type" widget="radio"/>
                </group>
                <footer>
                    <button name="process" string="Confirm" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="x"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
