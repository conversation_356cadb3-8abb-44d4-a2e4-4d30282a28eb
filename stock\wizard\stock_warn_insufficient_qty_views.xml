<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_warn_insufficient_qty_form_view" model="ir.ui.view">
        <field name="name">stock.warn.insufficient.qty</field>
        <field name="model">stock.warn.insufficient.qty</field>
        <field name="arch" type="xml">
            <form>
                <!-- Invisible field to trigger compute -->
                <field name="product_id" invisible="True"/>
                <div>
                    The product is not available in sufficient quantity
                    <span class="oe_inline" groups="stock.group_stock_multi_locations"> in
                        <strong><field name="location_id" readonly="True"/></strong>.
                    </span>
                </div>
                <div invisible="not quant_ids">
                    <br/>
                    <strong>Current Inventory: </strong>
                    <field name="quant_ids" style="margin-top:10px;">
                        <list>
                            <field name="location_id" options="{'no_create': True}"/>
                            <field name="lot_id" groups="stock.group_production_lot"/>
                            <field name="quantity"/>
                        </list>
                    </field>
                </div>
                <div name="description">
                </div>
                <footer>
                    <button name="cancel_button" string="Discard" class="btn-primary" special="cancel" data-hotkey="x"/>
                    <button string="Confirm" name="action_done" type="object" class="btn-secondary" data-hotkey="q"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="stock_warn_insufficient_qty_scrap_form_view" model="ir.ui.view">
        <field name="name">stock.warn.insufficient.qty.scrap</field>
        <field name="model">stock.warn.insufficient.qty.scrap</field>
        <field name="inherit_id" ref="stock.stock_warn_insufficient_qty_form_view"/>
         <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='description']" position="inside">
                Do you confirm you want to scrap <strong><field name="quantity" readonly="True"/></strong><field name="product_uom_name" readonly="True" class="mx-1"/>from location <strong><field name="location_id" readonly="True"/></strong>? This may lead to inconsistencies in your inventory.
            </xpath>
            <xpath expr="//button[@name='cancel_button']" position="replace">
                <button string="Discard" name="action_cancel" type="object" class="btn-primary"/>
            </xpath>
        </field>
    </record>
</odoo>
