<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="exception_on_mo">
        <div class="alert alert-warning" role="alert">
            Exception(s) occurred on the manufacturing order(s):
            <a href="#" data-oe-model="mrp.production" t-att-data-oe-id="production_order.id"><t t-esc="production_order.name"/></a>.
            Manual actions may be needed.
            <div class="mt16">
                <p>Exception(s):</p>
                <ul t-foreach="order_exceptions.items()" t-as="order_exception">
                    <li>
                        <t t-set="move_raw_id" t-value="order_exception[0]"/>
                        <t t-set="exception" t-value="order_exception[1]"/>
                        <t t-set="order" t-value="exception[0]"/>
                        <t t-set="new_qty" t-value="exception[1][0]"/>
                        <t t-set="old_qty" t-value="exception[1][1]"/>
                            <a href="#" data-oe-model="mrp.production" t-att-data-oe-id="production_order.id"><t t-esc="production_order.name"/></a>:
                            <t t-esc="new_qty"/> <t t-esc="move_raw_id.product_uom.name"/> of <t t-esc="move_raw_id.product_id.name"/>
                            <t t-if="cancel">
                                cancelled
                            </t>
                            <t t-if="not cancel">
                                ordered instead of <t t-esc="old_qty"/> <t t-esc="move_raw_id.product_uom.name"/>
                            </t>
                      </li>
                </ul>
            </div>
            <div class="mt16" t-if="not cancel and impacted_pickings">
                <p>Impacted Transfer(s):</p>
                <ul t-foreach="impacted_pickings" t-as="picking">
                    <li><a href="#" data-oe-model="stock.picking" t-att-data-oe-id="picking.id"><t t-esc="picking.name"/></a></li>
                </ul>
            </div>
        </div>
    </template>

    <template id="production_message">
        <t t-if="move.move_id.raw_material_production_id">
            <t t-set="message">Consumed</t>
        </t>
        <t t-if="move.move_id.production_id">
            <t t-set="message">Produced</t>
        </t>
        <strong><t t-esc="message"/> quantity has been updated.</strong>
    </template>

    <template id="track_production_move_template">
        <div>
            <t t-call="mrp.production_message"/>
            <t t-call="stock.message_body"/>
        </div>
    </template>
</odoo>
