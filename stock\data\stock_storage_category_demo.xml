<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="stock_storage_category_high_small" model="stock.storage.category">
            <field name="name">High frequency - Small</field>
            <field name="max_weight">200</field>
        </record>
        <record id="stock_storage_category_high_big" model="stock.storage.category">
            <field name="name">High frequency - Big</field>
            <field name="max_weight">3000</field>
        </record>
        <record id="stock_storage_category_medium_small" model="stock.storage.category">
            <field name="name">Medium frequency - Small</field>
            <field name="max_weight">200</field>
        </record>
        <record id="stock_storage_category_medium_big" model="stock.storage.category">
            <field name="name">Medium frequency - Big</field>
            <field name="max_weight">3000</field>
        </record>
        <record id="stock_storage_category_low_small" model="stock.storage.category">
            <field name="name">Low frequency - Small</field>
            <field name="max_weight">200</field>
        </record>
        <record id="stock_storage_category_low_big" model="stock.storage.category">
            <field name="name">Low frequency - Big</field>
            <field name="max_weight">3000</field>
        </record>
    </data>
</odoo>
