from odoo import api, fields, models


class MrpConsumptionWarning(models.TransientModel):
    _inherit = "mrp.consumption.warning"

    def action_confirm(self):
        for rec in self:
            for line in rec.mrp_consumption_warning_line_ids.filtered(
                lambda warning_line: warning_line.end_lot
            ):
                expected = line.product_expected_qty_uom or 0.0
                consumed = line.product_consumed_qty_uom or 0.0
                diff_qty = expected - consumed

                if diff_qty <= 0:
                    continue

                production = line.mrp_production_id
                if not production:
                    continue

                if line.lot_id:
                    lot_id = line.lot_id.id
                else:
                    lot_id = False

                scrap_vals = {
                    "product_id": line.product_id.id,
                    "product_uom_id": line.product_uom_id.id,
                    "scrap_qty": diff_qty,
                    "company_id": production.company_id.id,
                    "location_id": production.location_src_id.id,
                    "production_id": production.id,
                }
                if lot_id:
                    scrap_vals["lot_id"] = lot_id

                scrap = self.env["stock.scrap"].create(scrap_vals)
                scrap.action_validate()

        return super().action_confirm()


class MrpConsumptionWarningLine(models.TransientModel):
    _inherit = "mrp.consumption.warning.line"

    end_lot = fields.Boolean(string="End of Lot")
    lot_id = fields.Many2one(
        "stock.lot",
        string="Lot",
        compute="_compute_available_lot_ids",
        readonly=False,
        store=False,
        domain="[('id', 'in', available_lot_ids)]",
    )

    available_lot_ids = fields.Many2many(
        "stock.lot",
        compute="_compute_available_lot_ids",
        store=False,
    )

    is_lot_tracked = fields.Boolean(
        compute="_compute_is_lot_tracked",
        store=False,
    )

    @api.depends("product_id")
    def _compute_is_lot_tracked(self):
        for line in self:
            line.is_lot_tracked = line.product_id and line.product_id.tracking == "lot"

    @api.depends("mrp_production_id", "product_id")
    def _compute_available_lot_ids(self):
        for line in self:
            lots = self.env["stock.lot"]
            if line.mrp_production_id and line.product_id:
                move_raw = line.mrp_production_id.move_raw_ids.filtered(
                    lambda move: move.product_id.id == line.product_id.id  # noqa: B023
                )
                if move_raw:
                    lots = move_raw.move_line_ids.mapped("lot_id")
            line.available_lot_ids = lots
            line.lot_id = lots[:1] if line.available_lot_ids else False
