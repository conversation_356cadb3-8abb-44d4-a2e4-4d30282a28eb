<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data>
        <record id="digest_tip_stock_0" model="digest.tip">
            <field name="name">Tip: Speed up inventory operations with barcodes</field>
            <field name="sequence">1000</field>
            <field name="group_id" ref="stock.group_stock_user" />
            <field name="tip_description" type="html">
<div>
    <p class="tip_title">Tip: Speed up inventory operations with barcodes</p>
    <table class="tip_twocol">
        <tr>
            <td class="tip_twocol_left" style="width: 45%;">
                <img src="https://download.odoocdn.com/digests/stock/static/src/img/barcode.gif" class="tip_twocol_img" width="240"/>
            </td>
            <td style="width: 55%;">
                <p class="tip_content" style="margin: 0;">Enjoy a quick-paced experience with the Odoo barcode app. It is blazing fast and works even without a stable internet connection. It supports all flows: inventory adjustments, batch picking, moving lots or pallets, low inventory checks, etc. Go to the "Apps" menu to activate the barcode interface.</p>
            </td>
        </tr>
    </table>
    <div style="clear: both;" />
</div>
            </field>
        </record>
        <record id="digest_tip_stock_1" model="digest.tip">
            <field name="name">Tip: Monitor Lot details</field>
            <field name="sequence">4000</field>
            <field name="group_id" ref="stock.group_stock_user"/>
            <field name="tip_description" type="html">
<div>
    <p class="tip_title">Tip: Monitor Lot details</p>
    <p class="tip_content">Store and retrieve information regarding every Lot/Serial Number (condition, product info, ...).</p>
    <img src="https://download.odoocdn.com/digests/stock/static/src/img/18-stock-property-fields.gif" width="540" class="illustration_border"/>
</div>
            </field>
        </record>
    </data>
</odoo>
