<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="stock_picking_view_activity" model="ir.ui.view">
            <field name="name">stock.picking.view.activity</field>
            <field name="model">stock.picking</field>
            <field name="arch" type="xml">
                <activity string="Activity view">
                    <templates>
                        <div t-name="activity-box" class="d-block">
                            <field name="name" display="full" class="o_text_block o_text_bold"/>
                            <field name="scheduled_date"/>
                        </div>
                    </templates>
                </activity>
            </field>
        </record>

        <record model="ir.ui.view" id="stock_picking_calendar">
            <field name="name">stock.picking.calendar</field>
            <field name="model">stock.picking</field>
            <field name="priority" eval="2"/>
            <field name="arch" type="xml">
                <calendar string="Calendar View" date_start="scheduled_date" color="partner_id" event_limit="5" quick_create="0">
                    <field name="partner_id" filters="1"/>
                    <field name="origin"/>
                    <field name="picking_type_id"/>
                    <field name="state"/>
                    <field name="picking_properties"/>
                </calendar>
            </field>
        </record>

        <record model="ir.ui.view" id="stock_picking_kanban">
            <field name="name">stock.picking.kanban</field>
            <field name="model">stock.picking</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" sample="1">
                    <field name="scheduled_date"/>
                    <field name="picking_type_id"/>
                    <progressbar field="activity_state" colors='{"planned": "success", "today": "warning", "overdue": "danger"}'/>
                    <templates>
                        <t t-name="card">
                            <div class="d-flex mb-1">
                                <field name="priority" widget="priority" class="pt-1"/>
                                <field name="name" class="fw-bold fs-5 ms-1"/>
                                <field name="state" widget="label_selection" options="{'classes': {'draft': 'default', 'cancel': 'danger', 'waiting': 'warning', 'confirmed': 'warning', 'done': 'success'}}" class="ms-auto"/>
                            </div>
                            <field name="picking_properties" widget="properties"/>
                            <footer class="pt-0">
                                <div class="d-flex">
                                    <field name="partner_id"/>
                                    <field name="activity_ids" widget="kanban_activity"/>
                                    <field name="json_popover" nolabel="1" widget="stock_rescheduling_popover" invisible="not json_popover"/>
                                </div>
                                <div class="d-flex ms-auto mt-1 align-items-center">
                                    <field name="scheduled_date" options="{'show_time': false}"/>
                                    <field name="user_id" widget="many2one_avatar_user" invisible="not user_id" readonly="state in ['cancel', 'done']" class="ms-1"/>
                                </div>
                            </footer>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="vpicktree" model="ir.ui.view">
            <field name="name">stock.picking.list</field>
            <field name="model">stock.picking</field>
            <field name="arch" type="xml">
                <list string="Picking list" multi_edit="1" sample="1" js_class="stock_list_view">
                    <header>
                        <button name="do_unreserve" type="object" string="Unreserve"/>
                        <button name="action_assign" type="object" string="Check Availability"/>
                    </header>
                    <field name="company_id" column_invisible="True"/>
                    <field name="priority" optional="show" widget="priority" nolabel="1"/>
                    <field name="name" decoration-bf="1"/>
                    <field name="location_id" options="{'no_create': True}" string="From" groups="stock.group_stock_multi_locations" optional="show" readonly="state == 'done'"/>
                    <field name="location_dest_id" options="{'no_create': True}" string="To" groups="stock.group_stock_multi_locations" optional="show" readonly="state == 'done'"/>
                    <field name="partner_id" optional="show" readonly="state in ['cancel', 'done']"/>
                    <field name="is_signed" string="Signed" optional="hide" groups="stock.group_stock_sign_delivery"/>
                    <field name="user_id" optional="hide" widget="many2one_avatar_user" readonly="state in ['cancel', 'done']"/>
                    <field name="scheduled_date" optional="show" widget="remaining_days" invisible="state in ('done', 'cancel')" readonly="state in ['cancel', 'done']"/>
                    <field name="picking_type_code" column_invisible="True"/>
                    <field name="products_availability_state" column_invisible="True" options='{"lazy": true}'/>
                    <field name="products_availability" options='{"lazy": true}'
                        optional="hide"
                        invisible="picking_type_code != 'outgoing' or state not in ['confirmed', 'waiting', 'assigned']"
                        decoration-success="state == 'assigned' or products_availability_state == 'available'"
                        decoration-warning="state != 'assigned' and products_availability_state in ('expected', 'available')"
                        decoration-danger="state != 'assigned' and products_availability_state == 'late'"/>
                    <field name="date_deadline" optional="hide" widget="remaining_days" invisible="state in ('done', 'cancel')"/>
                    <field name="date_done" string="Effective Date" optional="hide"/>
                    <field name="origin" optional="show" readonly="state in ['cancel', 'done']"/>
                    <field name="backorder_id" optional="hide"/>
                    <field name="picking_type_id" optional="hide"/>
                    <field name="company_id" groups="base.group_multi_company" optional="show"/>
                    <field name="state" optional="show" widget="badge"
                           decoration-danger="state=='cancel'"
                           decoration-info="state== 'assigned'"
                           decoration-muted="state == 'draft'"
                           decoration-success="state == 'done'"
                           decoration-warning="state not in ('draft','cancel','done','assigned')"/>
                    <field name="activity_exception_decoration" widget="activity_exception"/>
                    <field name="json_popover" widget="stock_rescheduling_popover" nolabel="1" invisible="not json_popover"/>
                </list>
            </field>
        </record>

        <record id="view_picking_form" model="ir.ui.view">
            <field name="name">stock.picking.form</field>
            <field name="model">stock.picking</field>
            <field eval="12" name="priority"/>
            <field name="arch" type="xml">
                <form string="Transfer">
                <header>
                    <button name="action_confirm" invisible="state != 'draft'" string="Mark as Todo" type="object" class="oe_highlight" groups="base.group_user" data-hotkey="q"/>
                    <button name="action_assign" invisible="not show_check_availability" string="Check Availability" type="object" class="oe_highlight" groups="base.group_user" data-hotkey="w"/>
                    <button name="button_validate" invisible="state in ('draft', 'confirmed', 'done', 'cancel')" string="Validate" type="object" class="oe_highlight" groups="stock.group_stock_user" data-hotkey="v"/>
                    <button name="button_validate" invisible="state in ('waiting', 'assigned', 'done', 'cancel')" string="Validate" type="object" groups="stock.group_stock_user" class="o_btn_validate" data-hotkey="v"/>
                    <widget name="signature" string="Sign" highlight="1"
                            invisible="not id or picking_type_code != 'outgoing' or state != 'done' or is_signed"
                            full_name="partner_id" groups="stock.group_stock_sign_delivery"/>
                    <widget name="signature" string="Sign"
                            invisible="not id or picking_type_code != 'outgoing' or state == 'done' or is_signed"
                            full_name="partner_id" groups="stock.group_stock_sign_delivery"/>
                    <button name="do_print_picking" string="Print" groups="stock.group_stock_user" type="object" invisible="state != 'assigned'" data-hotkey="o"/>
                    <button name="%(action_report_delivery)d" string="Print" invisible="state != 'done'" type="action" groups="base.group_user" data-hotkey="o"/>
                    <button name="%(act_stock_return_picking)d" string="Return" invisible="state != 'done'" type="action" groups="base.group_user" data-hotkey="k"/>
                    <field name="state" widget="statusbar" invisible="picking_type_code != 'incoming'" statusbar_visible="draft,assigned,done"/>
                    <field name="state" widget="statusbar" invisible="picking_type_code == 'incoming'" statusbar_visible="draft,confirmed,assigned,done"/>
                    <button name="action_cancel" invisible="state not in ('assigned', 'confirmed', 'draft', 'waiting')" string="Cancel" groups="base.group_user" type="object" confirm="Are you sure you want to cancel this transfer?" data-hotkey="x"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_see_returns" type="object"
                            class="oe_stat_button" icon="fa-rotate-left"
                            invisible="return_count == 0">
                            <field string="Returns" name="return_count" widget="statinfo"/>
                        </button>
                        <button name="action_see_move_scrap" string="Scraps" type="object"
                            class="oe_stat_button" icon="oi-arrows-v"
                            invisible="not has_scrap_move"/>
                        <button name="action_see_packages" string="Packages" type="object"
                            class="oe_stat_button" icon="fa-cubes"
                            invisible="not has_packages"/>
                        <button name="%(action_stock_report)d" icon="oi-arrow-up" class="oe_stat_button" type="action" invisible="state != 'done' or not has_tracking" groups="stock.group_production_lot">
                            <div class="o_stat_info">
                                <span class="o_stat_text">Traceability</span>
                            </div>
                        </button>
                        <button name="action_view_reception_report" type="object"
                            context="{'default_picking_ids': [id]}"
                            class="oe_stat_button" icon="fa-list"
                            invisible="not show_allocation"
                            groups="stock.group_reception_report">
                            <div class="o_stat_info">
                                <span class="o_stat_text">Allocation</span>
                            </div>
                        </button>
                        <!-- Use the following button to avoid onchange on one2many -->
                        <button name="action_picking_move_tree"
                            class="oe_stat_button"
                            icon="oi-arrows-v"
                            type="object"
                            help="List view of operations"
                            groups="base.group_no_one"
                            invisible="(is_locked or state == 'done') or state == 'done' and is_locked"
                            context="{'picking_type_code': picking_type_code, 'default_picking_id': id, 'form_view_ref':'stock.view_move_form', 'address_in_id': partner_id, 'default_picking_type_id': picking_type_id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id}">
                            <div class="o_form_field o_stat_info">
                                <span class="o_stat_text">Operations</span>
                            </div>
                        </button>
                        <button name="action_detailed_operations"
                            class="oe_stat_button"
                            icon="fa-bars"
                            type="object"
                            help="List view of detailed operations">
                            <div class="o_form_field o_stat_info">
                                <span class="o_stat_text">Moves</span>
                            </div>
                        </button>
                        <button name="action_next_transfer"
                            class="oe_stat_button"
                            icon="fa-exchange"
                            type="object"
                            help="List view of next transfers"
                            invisible="not show_next_pickings">
                            <div class="o_form_field o_stat_info">
                                <span class="o_stat_text">Next Transfer</span>
                            </div>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1 class="d-flex">
                            <field name="priority" widget="priority" class="me-3"/>
                            <div invisible="id">New <field name="picking_type_code"/></div>
                            <field name="name" invisible="not id"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <div class="o_td_label">
                                <label for="partner_id" string="Delivery Address" style="font-weight:bold;"
                                       invisible="picking_type_code != 'outgoing'"/>
                                <label for="partner_id" string="Receive From" style="font-weight:bold;"
                                       invisible="picking_type_code != 'incoming'"/>
                                <label for="partner_id" string="Contact" style="font-weight:bold;"
                                       invisible="picking_type_code in ['incoming', 'outgoing']"/>
                            </div>
                            <field name="partner_id" nolabel="1" readonly="state in ['cancel', 'done']" placeholder="e.g. Lumber Inc"/>
                            <field name="picking_type_id" options="{'no_open': True}"
                                   readonly="state in ('done', 'cancel')"
                                   domain="[('code', 'in', ('incoming', 'outgoing', 'internal'))]" />
                            <field name="location_id" groups="!stock.group_stock_multi_locations" invisible="1" readonly="state == 'done'"/>
                            <field name="location_dest_id" groups="!stock.group_stock_multi_locations" invisible="1" readonly="state == 'done'"/>
                            <field name="location_id" options="{'no_create': True}" groups="stock.group_stock_multi_locations" invisible="picking_type_code == 'incoming'" readonly="state == 'done'"/>
                            <field name="location_dest_id" options="{'no_create': True}" groups="stock.group_stock_multi_locations" invisible="picking_type_code == 'outgoing'" readonly="state == 'done'"/>
                            <field name="backorder_id" invisible="not backorder_id"/>
                            <field name="use_create_lots" invisible="1"/> <!-- used by embedded stock.move list view below -->
                        </group>
                        <group>
                            <label for="scheduled_date"/>
                            <div class="o_row">
                                <field name="scheduled_date" readonly="state in ['cancel', 'done']" required="id"
                                    decoration-warning="state not in ('done', 'cancel') and scheduled_date &lt; now"
                                    decoration-danger="state not in ('done', 'cancel') and scheduled_date &lt; current_date"
                                    decoration-bf="state not in ('done', 'cancel') and (scheduled_date &lt; current_date or scheduled_date &lt; now)"/>
                                <field name="json_popover" nolabel="1" widget="stock_rescheduling_popover" invisible="not json_popover"/>
                            </div>
                            <field name="date_deadline"
                                invisible="state in ('done', 'cancel') or not date_deadline"
                                decoration-danger="date_deadline and date_deadline &lt; current_date"
                                decoration-bf="date_deadline and date_deadline &lt; current_date"/>
                            <field name="products_availability_state" invisible="1"/>
                            <field name="products_availability"
                                invisible="picking_type_code != 'outgoing' or state not in ['confirmed', 'waiting', 'assigned']"
                                decoration-success="state == 'assigned' or products_availability_state == 'available'"
                                decoration-warning="state != 'assigned' and products_availability_state in ('expected', 'available')"
                                decoration-danger="state != 'assigned' and products_availability_state == 'late'"/>
                            <field name="date_done" string="Effective Date" invisible="state != 'done'"/>
                            <field name="origin" placeholder="e.g. PO0032" readonly="state in ['cancel', 'done']"/>
                            <field name="owner_id" groups="stock.group_tracking_owner"
                                   invisible="picking_type_code != 'incoming'"
                                   readonly="state in ['cancel', 'done']"/>
                        </group>
                    </group>
                    <field name="picking_properties" columns="2"/>
                    <notebook>
                        <page string="Operations" name="operations">
                            <field name="move_ids_without_package" mode="list,kanban"
                                widget="stock_move_one2many"
                                readonly="state == 'done' and is_locked"
                                context="{'default_company_id': company_id, 'default_date': scheduled_date, 'default_date_deadline': date_deadline, 'picking_type_code': picking_type_code, 'default_picking_id': id, 'form_view_ref': 'stock.view_stock_move_operations', 'address_in_id': partner_id, 'default_picking_type_id': picking_type_id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_partner_id': partner_id}"
                                add-label="Add a Product">
                                <list decoration-muted="scrapped == True or state == 'cancel' or (state == 'done' and is_locked == True)" string="Stock Moves" editable="bottom">
                                    <field name="company_id" column_invisible="True"/>
                                    <field name="picking_id" column_invisible="True"/>
                                    <field name="name" column_invisible="True"/>
                                    <field name="state" readonly="0" column_invisible="True"/>
                                    <field name="picking_type_id" column_invisible="True"/>
                                    <field name="move_line_ids" column_invisible="True"/>
                                    <field name="location_id" column_invisible="True"/>
                                    <field name="location_dest_id" column_invisible="True"/>
                                    <field name="partner_id" column_invisible="True" readonly="state == 'done'"/>
                                    <field name="scrapped" column_invisible="True"/>
                                    <field name="picking_code" column_invisible="True"/>
                                    <field name="show_details_visible" column_invisible="True"/>
                                    <field name="additional" column_invisible="True"/>
                                    <field name="move_lines_count" column_invisible="True"/>
                                    <field name="is_locked" column_invisible="True"/>
                                    <field name="product_uom_category_id" column_invisible="True"/>
                                    <field name="is_storable" column_invisible="True"/>
                                    <field name="has_tracking" column_invisible="True"/>
                                    <field name="product_id" context="{'default_is_storable': True}" required="1" readonly="(state != 'draft' and not additional) or move_lines_count &gt; 0" force_save="1"/>
                                    <field name="location_final_id" optional="hide" groups="stock.group_stock_multi_locations"/>
                                    <field name="description_picking" string="Description" optional="hide"/>
                                    <field name="date" optional="hide"/>
                                    <field name="date_deadline" optional="hide"/>
                                    <field name="is_quantity_done_editable" column_invisible="True"/>
                                    <field name="show_quant" column_invisible="True"/>
                                    <field name="show_lots_text" column_invisible="True"/>
                                    <field name="show_lots_m2o" column_invisible="True"/>
                                    <field name="is_initial_demand_editable" column_invisible="True"/>
                                    <field name="display_import_lot" column_invisible="True"/>
                                    <field name="picking_type_entire_packs" column_invisible="True"/>
                                    <field name="product_packaging_id" groups="product.group_stock_packaging"
                                        context="{'default_product_id': product_id}"
                                        readonly="not product_id"/>
                                    <field name="product_uom_qty" string="Demand" readonly="not is_initial_demand_editable"/>
                                    <button type="object" name="action_product_forecast_report" title="Forecast Report" icon="fa-area-chart"
                                        invisible="not product_id or product_uom_qty == 0 or quantity == 0 and forecast_availability &lt;= 0 or (parent.picking_type_code == 'outgoing' and state != 'draft')"/>
                                    <button type="object" name="action_product_forecast_report" title="Forecast Report" icon="fa-area-chart text-danger"
                                        invisible="not product_id or product_uom_qty == 0 or quantity &gt; 0 or forecast_availability &gt; 0 or (parent.picking_type_code == 'outgoing' and state != 'draft')"/>
                                    <field name="forecast_expected_date" column_invisible="True"/>
                                    <field name="forecast_availability" string="Forecast" optional="hide"
                                        column_invisible="parent.state in ('draft', 'done') or parent.picking_type_code != 'outgoing'" widget="forecast_widget"/>
                                    <field name="product_qty" readonly="1" column_invisible="True"/>
                                    <field name="quantity" string="Quantity" readonly="not is_quantity_done_editable" column_invisible="parent.state=='draft'" decoration-danger="product_uom_qty and quantity > product_uom_qty and parent.state not in ['done', 'cancel']"/>
                                    <field name="product_uom" readonly="state != 'draft' and not additional" options="{'no_open': True, 'no_create': True}" string="Unit" groups="uom.group_uom"/>
                                    <field name="product_uom" groups="!uom.group_uom" column_invisible="True"/>
                                    <field name="picked" optional="hide" column_invisible="parent.state=='draft'"/>
                                    <field name="lot_ids" widget="many2many_tags"
                                        column_invisible="parent.state == 'draft'"
                                        groups="stock.group_production_lot"
                                        invisible="not show_details_visible or has_tracking == 'none'"
                                        optional="hide"
                                        options="{'create': [('parent.use_create_lots', '=', True)]}"
                                        context="{'default_company_id': company_id, 'default_product_id': product_id, 'active_picking_id': parent.id}"
                                        domain="[('product_id','=',product_id)]"
                                    />
                                </list>
                            </field>
                            <field name="id" invisible="1"/>
                            <field name="package_level_ids" context="{'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_company_id': company_id}" invisible="not picking_type_entire_packs" readonly="state == 'done'" />
                            <button class="btn-secondary float-end" name="action_put_in_pack" type="object" string="Put in Pack" invisible="state in ('draft', 'done', 'cancel')" groups="stock.group_tracking_lot" data-hotkey="shift+g"/>
                        </page>
                        <page string="Additional Info" name="extra">
                            <group>
                                <group string="Other Information" name="other_infos">
                                    <field name="picking_type_code" invisible="1"/>
                                    <field name="move_type" invisible="picking_type_code == 'incoming'" readonly="state in ['cancel', 'done']"/>
                                    <field name="user_id" widget="many2one_avatar_user" domain="[('share', '=', False)]" readonly="state in ['cancel', 'done']"/>
                                    <field name="group_id" groups="base.group_no_one"/>
                                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" force_save="1"/>
                                </group>
                            </group>
                        </page>
                        <page string="Note" name="note">
                            <field name="note" string="Note" placeholder="Add an internal note that will be printed on the Picking Operations sheet"/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
                </form>
            </field>
        </record>

        <record id="view_picking_internal_search" model="ir.ui.view">
            <field name="name">stock.picking.internal.search</field>
            <field name="model">stock.picking</field>
            <field name="arch" type="xml">
                <search string="Picking Lists">
                    <field name="name" string="Transfer" filter_domain="['|', ('name', 'ilike', self), ('origin', 'ilike', self)]"/>
                    <field name="partner_id" filter_domain="[('partner_id', 'child_of', self)]"/>
                    <field name="origin"/>
                    <field name="product_id"/>
                    <field name="picking_type_id"/>
                    <field name="move_line_ids"
                        string="Package"
                        filter_domain="['|', ('move_line_ids.package_id.name', 'ilike', self), ('move_line_ids.result_package_id.name', 'ilike', self)]"
                        groups="stock.group_tracking_lot"/>
                    <field name="lot_id" groups="stock.group_production_lot"/>
                    <filter name="to_do_transfers" string="To Do" domain="['&amp;',('user_id', 'in', [uid, False]),('state','not in',['done','cancel'])]"/>
                    <filter name="my_transfers" string="My Transfers" domain="[('user_id', '=', uid)]"/>
                    <filter string="Starred" name="starred" domain="[('priority', '=', '1')]"/>
                    <separator/>
                    <filter name="draft" string="Draft" domain="[('state', '=', 'draft')]" help="Draft Moves"/>
                    <filter name="waiting" string="Waiting" domain="[('state', 'in', ('confirmed', 'waiting'))]" help="Waiting Moves"/>
                    <filter name="available" string="Ready" domain="[('state', '=', 'assigned')]" help="Assigned Moves"/>
                    <separator/>
                    <filter name="reception" string="Receipts" domain="[('picking_type_code', '=', 'incoming')]"/>
                    <filter name="delivery" string="Deliveries" domain="[('picking_type_code', '=', 'outgoing')]"/>
                    <filter name="internal" string="Internal" domain="[('picking_type_code', '=', 'internal')]"/>
                    <separator/>
                    <filter invisible="1" name="before" string="Before" domain="[('search_date_category', '=', 'before')]"/>
                    <filter invisible="1" name="yesterday" string="Yesterday" domain="[('search_date_category', '=', 'yesterday')]"/>
                    <filter invisible="1" name="today" string="Today" domain="[('search_date_category', '=', 'today')]"/>
                    <filter invisible="1" name="day_1" string="Tomorrow" domain="[('search_date_category', '=', 'day_1')]"/>
                    <filter invisible="1" name="day_2" string="The day after tomorrow" domain="[('search_date_category', '=', 'day_2')]"/>
                    <filter invisible="1" name="after" string="After" domain="[('search_date_category', '=', 'after')]"/>
                    <filter name="late" string="Late" help="Deadline exceed or/and by the scheduled"
                        domain="[('state', 'in', ('assigned', 'waiting', 'confirmed')), '|', '|', ('has_deadline_issue', '=', True), ('date_deadline', '&lt;', current_date), ('scheduled_date', '&lt;', current_date)]"/>
                    <filter string="Planning Issues" name="planning_issues" help="Transfers that are late on scheduled time or one of pickings will be late"
                        domain="['|', ('delay_alert_date', '!=', False), '&amp;', ('scheduled_date','&lt;', time.strftime('%Y-%m-%d %H:%M:%S')), ('state', 'in', ('assigned', 'waiting', 'confirmed'))]"/>
                    <filter string="Late Availability" name="late_availability" domain="[('products_availability_state', '=', 'late')]"/>
                    <separator/>
                    <filter name="backorder" string="Backorders" domain="[('backorder_id', '!=', False), ('state', 'in', ('assigned', 'waiting', 'confirmed'))]" help="Remaining parts of picking partially processed"/>
                    <separator/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which has next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <separator/>
                    <filter string="Warnings" name="activities_exception"
                        domain="[('activity_exception_decoration', '!=', False)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Status" name="status" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Scheduled Date" name="expected_date" domain="[]" context="{'group_by': 'scheduled_date'}"/>
                        <filter string="Source Document" name="origin" domain="[]" context="{'group_by': 'origin'}"/>
                        <filter string="Operation Type" name="picking_type" domain="[]" context="{'group_by': 'picking_type_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="action_picking_tree_all" model="ir.actions.act_window">
            <field name="name">Transfers</field>
            <field name="res_model">stock.picking</field>
            <field name="view_mode">list,kanban,form,calendar</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'default_company_id': allowed_company_ids[0]}</field>
            <field name="search_view_id" ref="view_picking_internal_search"/>
        </record>

        <record id="action_picking_tree_incoming" model="ir.actions.act_window">
            <field name="name">Receipts</field>
            <field name="res_model">stock.picking</field>
            <field name="path">receipts</field>
            <field name="view_mode">list,kanban,form,calendar,activity</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'restricted_picking_type_code': 'incoming', 'search_default_reception': 1}</field>
            <field name="search_view_id" ref="view_picking_internal_search"/>
        </record>

        <record id="action_picking_tree_outgoing" model="ir.actions.act_window">
            <field name="name">Deliveries</field>
            <field name="res_model">stock.picking</field>
            <field name="path">deliveries</field>
            <field name="view_mode">list,kanban,form,calendar,activity</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'restricted_picking_type_code': 'outgoing', 'search_default_delivery': 1}</field>
            <field name="search_view_id" ref="view_picking_internal_search"/>
        </record>

        <record id="action_picking_tree_internal" model="ir.actions.act_window">
            <field name="name">Internal Transfers</field>
            <field name="res_model">stock.picking</field>
            <field name="view_mode">list,kanban,form,calendar</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'restricted_picking_type_code': 'internal', 'search_default_internal': 1}</field>
            <field name="search_view_id" ref="view_picking_internal_search"/>
        </record>

        <record id="action_validate_picking" model="ir.actions.server">
            <field name="name">Validate</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
            if records:
                res = records.button_validate()
                if isinstance(res, dict):
                    action = res
            </field>
        </record>

        <record id="action_unreserve_picking" model="ir.actions.server">
            <field name="name">Unreserve</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_view_types">list,form</field>
            <field name="state">code</field>
            <field name="code">
            if records:
                records.do_unreserve()
            </field>
        </record>

        <record id="action_print_labels" model="ir.actions.server">
            <field name="name">Labels</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_view_types">list,form</field>
            <field name="binding_type">report</field>
            <field name="state">code</field>
            <field name="code">
            if records:
                action = records.action_open_label_type()
            </field>
        </record>

        <record id="action_toggle_is_locked" model="ir.actions.server">
            <field name="name">Lock/Unlock</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_view_types">form</field>
            <field name="state">code</field>
            <field name="groups_id" eval="[Command.link(ref('stock.group_stock_manager'))]"/>
            <field name="code">
            if records:
                records.action_toggle_is_locked()</field>
        </record>

        <record id="action_scrap" model="ir.actions.server">
            <field name="name">Scrap</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_view_types">form</field>
            <field name="state">code</field>
            <field name="code">
            if records:
                action = records.button_scrap()</field>
        </record>

        <record id="action_lead_mass_mail" model="ir.actions.act_window">
            <field name="name">Send email</field>
            <field name="res_model">mail.compose.message</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="context">{
                'default_composition_mode': 'mass_mail',
            }</field>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_view_types">list</field>
        </record>

        <record id="click_dashboard_graph" model="ir.actions.server">
            <field name="name">stock.click_dashboard_graph</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="state">code</field>
            <field name="code">action = model.get_action_click_graph()</field>
        </record>

        <record id="method_action_picking_tree_incoming" model="ir.actions.server">
            <field name="name">stock.method_action_picking_tree_incoming</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="state">code</field>
            <field name="code">action = model.get_action_picking_tree_incoming()</field>
        </record>

        <record id="method_action_picking_tree_outgoing" model="ir.actions.server">
            <field name="name">stock.method_action_picking_tree_outgoing</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="state">code</field>
            <field name="code">action = model.get_action_picking_tree_outgoing()</field>
        </record>

        <record id="method_action_picking_tree_internal" model="ir.actions.server">
            <field name="name">stock.method_action_picking_tree_internal</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="state">code</field>
            <field name="code">action = model.get_action_picking_tree_internal()</field>
        </record>

        <menuitem id="in_picking" name="Receipts" parent="menu_stock_transfers"
                  action="stock.method_action_picking_tree_incoming" sequence="20"
                  groups="stock.group_stock_manager,stock.group_stock_user"/>
        <menuitem id="out_picking" name="Deliveries" parent="menu_stock_transfers"
                  action="stock.method_action_picking_tree_outgoing" sequence="21"
                  groups="stock.group_stock_manager,stock.group_stock_user"/>
        <menuitem id="int_picking" name="Internal" parent="menu_stock_transfers"
                  action="stock.method_action_picking_tree_internal" sequence="22"
                  groups="stock.group_stock_multi_locations"/>

        <record id="stock_picking_action_picking_type" model="ir.actions.act_window">
            <field name="name">All Transfers</field>
            <field name="res_model">stock.picking</field>
            <field name="context">{'contact_display': 'partner_address'}</field>
        </record>

        <record id="action_picking_tree_ready" model="ir.actions.act_window">
            <field name="name">To Do</field>
            <field name="res_model">stock.picking</field>
            <field name="view_mode">list,kanban,form,calendar</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'search_default_available': 1}</field>
       </record>

        <record id="action_picking_tree_graph" model="ir.actions.act_window">
            <field name="name">To Do</field>
            <field name="res_model">stock.picking</field>
            <field name="view_mode">list,kanban,form,calendar</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'search_default_available': 1, 'search_default_waiting': 1}</field>
       </record>

        <record id="action_picking_tree_waiting" model="ir.actions.act_window">
            <field name="name">Waiting Transfers</field>
            <field name="res_model">stock.picking</field>
            <field name="view_mode">list,kanban,form,calendar</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'search_default_waiting': 1}</field>
        </record>

        <record id="action_picking_tree_late" model="ir.actions.act_window">
            <field name="name">Late Transfers</field>
            <field name="res_model">stock.picking</field>
            <field name="view_mode">list,kanban,form,calendar</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'search_default_late': 1}</field>
        </record>

        <record id="action_picking_tree_backorder" model="ir.actions.act_window">
            <field name="name">Backorders</field>
            <field name="res_model">stock.picking</field>
            <field name="view_mode">list,kanban,form,calendar</field>
            <field name="domain"></field>
            <field name="context">{'contact_display': 'partner_address', 'search_default_backorder': 1}</field>
        </record>

        <record id="action_get_picking_type_ready_moves" model="ir.actions.act_window">
            <field name="name">Ready Moves</field>
            <field name="res_model">stock.move</field>
            <field name="view_id" ref="view_move_tree"/>
            <field name="search_view_id" ref="view_move_search"/>
            <field name="domain">[('picking_type_id', '=', active_id)]</field>
            <field name="context">{'search_default_ready': 1}</field>
        </record>

        <template id="help_message_template" name="Help Message" groups="base.group_user">
            <p name="top_help_message" class="o_view_nocontent_smiling_face o_view_nocontent_stock">
                <t name="receipt_message">
                    <t t-if="picking_type_code == 'incoming'">
                        No receipt yet! Create a new one.
                    </t>
                </t>
                <t name="delivery_message">
                    <t t-if="picking_type_code == 'outgoing'">
                        No delivery to do!
                    </t>
                </t>
                <t name="transfer_message">
                    <t t-if="picking_type_code not in ['incoming', 'outgoing']">
                        No transfer found. Let's create one!
                    </t>
                </t>
            </p>
            <p name="bottom_help_message" class="text-muted">
                <t name="receipt_message">
                    <t t-if="picking_type_code == 'incoming'">
                        Reduce stockouts with alerts, barcode app, replenishment propositions,
                        locations management traceability, quality control, etc.
                    </t>
                </t>
                <t name="delivery_message">
                    <t t-if="picking_type_code == 'outgoing'"></t>
                </t>
                <t name="transfer_message">
                    <t t-if="picking_type_code not in ['incoming', 'outgoing']">
                        Transfers allow you to move products from one location to another.
                    </t>
                </t>
            </p>
            <p name="footer_help_message" class="text-muted">
                <br/>
                Want to speed up operations? <a class="btn-link" name="stock.action_install_barcode" type="action">Install</a> the barcode app
            </p>
        </template>

        <record id="action_install_barcode" model="ir.actions.server">
            <field name="name">Install Barcode</field>
            <field name="model_id" ref="stock.model_stock_picking_type"/>
            <field name="binding_model_id" ref="stock.model_stock_picking_type"/>
            <field name="state">code</field>
            <field name="code">
                action = model.action_redirect_to_barcode_installation()
            </field>
        </record>

        <record id="action_get_picking_type_operations" model="ir.actions.act_window">
            <field name="name">Operations</field>
            <field name="res_model">stock.move.line</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'list', 'view_id': ref('stock.view_move_line_tree_detailed')})]"/>
            <field name="domain">[('picking_type_id', '=', active_id), ('picking_id', '!=', False)]</field>
            <field name="context">{
                'search_default_todo': '1',
            }</field>
            <field name="search_view_id" ref="stock_move_line_view_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No operations found. Let's create a transfer!
                </p>
                <p>
                    Transfers allow you to move products from one location to another.
                </p>
            </field>
        </record>

        <record id="action_picking_form" model="ir.actions.act_window">
            <field name="name">New Transfer</field>
            <field name="res_model">stock.picking</field>
            <field name="view_mode">form</field>
            <field name="domain"></field>
            <field name="context">{
                    'search_default_picking_type_id': [active_id],
                    'default_picking_type_id': active_id,
                    'contact_display': 'partner_address',
            }
            </field>
            <field name="search_view_id" ref="view_picking_internal_search"/>
        </record>
        <record id="do_view_pickings" model="ir.actions.act_window">
            <field name="name">Transfers for Groups</field>
            <field name="res_model">stock.picking</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('group_id','=',active_id)]</field>
        </record>
        <record id="stock_split_picking" model="ir.actions.server">
            <field name="name">Split</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_view_types">form</field>
            <field name="state">code</field>
            <field name="code">action = records.action_split_transfer()</field>
        </record>
</odoo>
