from odoo import _, models
from odoo.exceptions import UserError
from odoo.tools import float_compare, float_is_zero


class MrpProduction(models.Model):
    _inherit = "mrp.production"

    # -------------------------------------------------------------------------
    # COMPUTE METHODS
    # -------------------------------------------------------------------------

    def _compute_state(self):
        """
        Compute the state of the manufacturing order.

        This method determines and sets the `state` field of each production order
        based on the status of related moves, work orders, and production quantities.

        Customization Summary:
        Prevents MO from moving to 'progress' if BoM has a product label defined.
        """
        for production in self:
            if (
                not production.state
                or not production.product_uom_id
                or not (production.id or production._origin.id)
            ):
                production.state = "draft"
            elif production.state == "cancel" or (
                production.move_finished_ids
                and all(move.state == "cancel" for move in production.move_finished_ids)
            ):
                production.state = "cancel"
            elif (
                production.state == "done"
                or (
                    production.move_raw_ids
                    and all(
                        move.state in ("cancel", "done")
                        for move in production.move_raw_ids
                    )
                )
                and all(
                    move.state in ("cancel", "done")
                    for move in production.move_finished_ids
                )
            ):
                production.state = "done"
            elif production.workorder_ids and all(
                wo_state in ("done", "cancel")
                for wo_state in production.workorder_ids.mapped("state")
            ):
                production.state = "to_close"
            elif (
                not production.workorder_ids
                and float_compare(
                    production.qty_producing,
                    production.product_qty,
                    precision_rounding=production.product_uom_id.rounding,
                )
                >= 0
            ):
                production.state = "to_close"
            elif any(
                wo_state in ("progress", "done")
                for wo_state in production.workorder_ids.mapped("state")
            ):
                production.state = "progress"
            elif (
                production.product_uom_id
                and not float_is_zero(
                    production.qty_producing,
                    precision_rounding=production.product_uom_id.rounding,
                )
                and not production.bom_id.product_label_id
            ):
                production.state = "progress"
            elif (
                any(production.move_raw_ids.mapped("picked"))
                and not production.bom_id.product_label_id
            ):
                production.state = "progress"

    # -------------------------------------------------------------------------
    # ACTION METHODS
    # -------------------------------------------------------------------------

    def action_start(self):
        for production in self:
            bom = production.bom_id
            product_label = bom.product_label_id
            if product_label:
                label_moves = production.move_raw_ids.filtered(
                    lambda m: m.product_id == product_label  # noqa: B023
                )
                if self.env.context.get("from_input_control"):
                    return super().action_start()

                if label_moves and not label_moves.mapped("move_line_ids.lot_id.name"):
                    raise UserError(
                        _(
                            "The batch of the labeled product must be consumed before entering the code."  # noqa: E501
                        )
                    )
                if product_label.tracking == "lot":
                    return {
                        "name": _("Input control"),
                        "type": "ir.actions.act_window",
                        "res_model": "mrp.input.control",
                        "view_mode": "form",
                        "target": "new",
                        "context": {
                            "default_production_id": production.id,
                        },
                    }
