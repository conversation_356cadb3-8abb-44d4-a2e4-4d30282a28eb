<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">
    <div t-name="stock.Forecasted" class="o_action">
        <ControlPanel>
            <t t-set-slot="layout-buttons">
                <ForecastedButtons action="props.action" reloadReport.bind="reloadReport" resModel="resModel"/>
            </t>
            <t t-set-slot="layout-actions">
                <div class="btn-group o_search_options position-static" role="search">
                    <ForecastedWarehouseFilter action="props.action" warehouses="warehouses" setWarehouseInContext.bind="updateWarehouse"/>
                </div>
            </t>
        </ControlPanel>
        <div class="o-content pt-3 container-fluid overflow-auto o_stock_forecasted_page">
            <ForecastedHeader docs="docs" openView.bind="openView"/>
            <t t-if="context.warehouse_id">
                <View type="'graph'"
                viewId="stock_report_view_graph"
                resModel="'report.stock.quantity'"
                domain="graphDomain"
                display="{controlPanel: false}"
                context="{fill_temporal: false}"
                info="graphInfo"
                useSampleModel="true"
                />
            </t>
            <ForecastedDetails docs="docs" openView.bind="openView" reloadReport.bind="reloadReport"/>
        </div>
    </div>
</templates>
