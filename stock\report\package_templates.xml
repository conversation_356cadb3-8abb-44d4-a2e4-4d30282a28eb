<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <template id="label_package_template_view">
            <t t-foreach="docs" t-as="package">
                <t t-translation="off">
^XA
                    <t t-if="package.valid_sscc">
                        <t t-set="barcode" t-value="'00' + package.name"/>
                        <t t-if="package.pack_date" name="datamatrix_pack_date" t-set="barcode" t-value="barcode + '13' + package.pack_date.strftime('%y%m%d')"/>
^FO310,50
^A0N,44,33^FDSSCC: <t t-out="package.name"/>^FS
^FO310,100^BY3
<t t-if="package.pack_date">^A0N,44,33^FDPack Date: <t t-out="package.pack_date" t-options='{"widget": "date"}'/>^FS</t>
^FO310,150^BY3
<t t-if="package.package_type_id">^A0N,44,33^FDPackage Type: <t t-out="package.package_type_id.name"/>^FS</t>
^FO100,50^BY3
^BXN,10,200
^FD<t t-out="barcode"/>^FS
                    </t>
                    <t t-else="">
^FO100,50
^A0N,44,33^FD<t t-out="package.name"/>^FS
^FO100,100^BY3
^BCN,100,Y,N,N
^FD<t t-out="package.name"/>^FS
                    </t>
^XZ
                </t>
            </t>
        </template>
    </data>
</odoo>
