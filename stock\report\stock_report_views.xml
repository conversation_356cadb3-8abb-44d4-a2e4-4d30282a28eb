<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="action_report_picking" model="ir.actions.report">
            <field name="name">Picking Operations</field>
            <field name="model">stock.picking</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">stock.report_picking</field>
            <field name="report_file">stock.report_picking_operations</field>
            <field name="print_report_name">'Picking Operations - %s - %s' % (object.partner_id.name or '', object.name)</field>
            <field name="binding_model_id" ref="model_stock_picking"/>
            <field name="binding_type">report</field>
        </record>
        <record id="action_report_delivery" model="ir.actions.report">
            <field name="name">Delivery Slip</field>
            <field name="model">stock.picking</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">stock.report_deliveryslip</field>
            <field name="report_file">stock.report_deliveryslip</field>
            <field name="print_report_name">'Delivery Slip - %s - %s' % (object.partner_id.name or '', object.name)</field>
            <field name="binding_model_id" ref="model_stock_picking"/>
            <field name="binding_type">report</field>
        </record>
        <record id="action_report_picking_packages" model="ir.actions.report">
            <field name="name">Packages</field>
            <field name="model">stock.picking</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">stock.report_picking_packages</field>
            <field name="report_file">stock.report_picking_packages</field>
            <field name="print_report_name">'Packages - %s' % (object.name)</field>
            <field name="binding_model_id" ref="model_stock_picking"/>
            <field name="binding_type">report</field>
            <field name="groups_id" eval="[Command.link(ref('stock.group_tracking_lot'))]"/>
        </record>
        <record id="action_report_inventory" model="ir.actions.report">
            <field name="name">Count Sheet</field>
            <field name="model">stock.quant</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">stock.report_inventory</field>
            <field name="report_file">stock.report_inventory</field>
            <field name="print_report_name">'Count Sheet'</field>
            <field name="binding_model_id" ref="model_stock_quant"/>
            <field name="binding_type">report</field>
        </record>
        <record id="action_report_quant_package_barcode" model="ir.actions.report">
            <field name="name">Package Barcode with Contents</field>
            <field name="model">stock.quant.package</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">stock.report_package_barcode</field>
            <field name="report_file">stock.report_package_barcode</field>
            <field name="binding_model_id" ref="model_stock_quant_package"/>
            <field name="binding_type">report</field>
        </record>
        <record id="action_report_quant_package_barcode_small" model="ir.actions.report">
            <field name="name">Package Barcode (PDF)</field>
            <field name="model">stock.quant.package</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">stock.report_package_barcode_small</field>
            <field name="report_file">stock.report_package_barcode</field>
            <field name="binding_model_id" ref="model_stock_quant_package"/>
            <field name="binding_type">report</field>
        </record>
        <record id="action_report_location_barcode" model="ir.actions.report">
            <field name="name">Location Barcode</field>
            <field name="model">stock.location</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">stock.report_location_barcode</field>
            <field name="report_file">stock.report_location_barcode</field>
            <field name="paperformat_id" ref="product.paperformat_label_sheet"/>
            <field name="print_report_name">'Location - %s' % object.name</field>
            <field name="binding_model_id" ref="model_stock_location"/>
            <field name="binding_type">report</field>
        </record>
        <record id="action_report_lot_label" model="ir.actions.report">
            <field name="name">Lot/Serial Number (PDF)</field>
            <field name="model">stock.lot</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">stock.report_lot_label</field>
            <field name="report_file">stock.report_lot_label</field>
            <field name="paperformat_id" ref="product.paperformat_label_sheet"/>
            <field name="print_report_name">'Lot-Serial - %s' % object.name</field>
            <field name="binding_model_id" ref="model_stock_lot"/>
            <field name="binding_type">report</field>
        </record>
        <record id="action_report_picking_type_label" model="ir.actions.report">
            <field name="name">Operation type (PDF)</field>
            <field name="model">stock.picking.type</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">stock.report_picking_type_label</field>
            <field name="report_file">stock.report_picking_type_label</field>
            <field name="paperformat_id" ref="product.paperformat_label_sheet"/>
            <field name="print_report_name">'Operation-type - %s' % object.name</field>
            <field name="binding_model_id" ref="model_stock_picking_type"/>
            <field name="binding_type">report</field>
        </record>
        <record id="action_report_stock_rule" model="ir.actions.report">
            <field name="name">Product Routes Report</field>
            <field name="model">product.template</field>
            <field name="report_type">qweb-html</field>
            <field name="report_name">stock.report_stock_rule</field>
            <field name="report_file">stock.report_stock_rule</field>
        </record>
        <record id="label_product_product" model="ir.actions.report">
            <field name="name">Product Label (ZPL)</field>
            <field name="model">product.product</field>
            <field name="report_type">qweb-text</field>
            <field name="report_name">stock.label_product_product_view</field>
            <field name="report_file">stock.label_product_product_view</field>
            <field name="binding_model_id" eval="False"/>
            <field name="binding_type">report</field>
        </record>
        <record id="label_lot_template" model="ir.actions.report">
            <field name="name">Lot/Serial Number (ZPL)</field>
            <field name="model">stock.lot</field>
            <field name="report_type">qweb-text</field>
            <field name="report_name">stock.label_lot_template_view</field>
            <field name="report_file">stock.label_lot_template_view</field>
            <field name="binding_model_id" ref="model_stock_lot"/>
            <field name="binding_type">report</field>
        </record>
        <record id="label_package_template" model="ir.actions.report">
            <field name="name">Package Barcode (ZPL)</field>
            <field name="model">stock.quant.package</field>
            <field name="report_type">qweb-text</field>
            <field name="report_name">stock.label_package_template_view</field>
            <field name="report_file">stock.label_package_template_view</field>
            <field name="binding_model_id" ref="model_stock_quant_package"/>
            <field name="binding_type">report</field>
        </record>
        <record id="label_product_packaging" model="ir.actions.report">
            <field name="name">Product Packaging (ZPL)</field>
            <field name="model">product.packaging</field>
            <field name="report_type">qweb-text</field>
            <field name="report_name">stock.label_product_packaging_view</field>
            <field name="report_file">stock.label_product_packaging_view</field>
            <field name="binding_model_id" ref="product.model_product_packaging"/>
            <field name="binding_type">report</field>
        </record>
        <record id="label_picking_type" model="ir.actions.report">
            <field name="name">Operation type (ZPL)</field>
            <field name="model">stock.picking.type</field>
            <field name="report_type">qweb-text</field>
            <field name="report_name">stock.label_picking_type_view</field>
            <field name="report_file">stock.label_picking_type_view</field>
            <field name="binding_model_id" ref="model_stock_picking_type"/>
            <field name="binding_type">report</field>
        </record>
        <record id="label_picking" model="ir.actions.report">
            <field name="name">Reception Report Label</field>
            <field name="model">stock.move</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">stock.report_reception_report_label</field>
            <field name="report_file">stock.report_reception_report_label</field>
            <field name="paperformat_id" ref="product.paperformat_label_sheet_dymo"/>
            <field name="binding_type">report</field>
        </record>
    </data>
</odoo>
