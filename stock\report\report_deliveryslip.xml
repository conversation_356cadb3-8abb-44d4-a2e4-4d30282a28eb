<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <template id="report_delivery_document">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="o" t-value="o.with_context(lang=o._get_report_lang())" />
                <t t-set="partner" t-value="o.partner_id or (o.move_ids and o.move_ids[0].partner_id) or False"/>

                <t t-set="address">
                    <div name="div_outgoing_address">
                        <div name="outgoing_delivery_address"
                            t-if="o.should_print_delivery_address()">
                            <span><strong>Delivery Address:</strong></span>
                            <div t-out="o.move_ids[0].partner_id or o.partner_id"
                                t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                        </div>
                        <div name="outgoing_warehouse_address"
                             t-elif="o.picking_type_id.code != 'internal' and o.picking_type_id.warehouse_id.partner_id">
                            <strong>Warehouse Address</strong>
                            <div t-field="o.picking_type_id.warehouse_id.partner_id"
                                t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                        </div>
                    </div>
                </t>
                <t t-set="information_block">
                    <div class="row">
                        <div class="col-7" name="div_incoming_address">
                            <t t-set="show_partner" t-value="False" />
                            <div name="vendor_address" t-if="o.picking_type_id.code=='incoming' and partner">
                                <strong>Vendor Address</strong>
                                <t t-set="show_partner" t-value="True" />
                            </div>
                            <div name="customer_address" t-if="o.picking_type_id.code=='outgoing' and partner and partner != partner.commercial_partner_id">
                                <strong>Customer Address</strong>
                                <t t-set="show_partner" t-value="True" />
                            </div>
                            <div t-if="show_partner" name="partner_header">
                                <div t-field="partner.commercial_partner_id"
                                     t-options='{"widget": "contact", "fields": ["address", "name", "phone", "vat"], "no_marker": True, "phone_icons": True}'/>
                            </div>
                        </div>
                    </div>
                </t>
                <div class="page">
                    <h2>
                        <span t-field="o.name">WH/OUT/0001</span>
                    </h2>
                    <div class="oe_structure"></div>
                    <div id="informations" class="report-wrapping-flexbox clearfix row mb-4">
                        <div t-if="o.origin" class="col col-3 mw-100 mb-2" name="div_origin">
                            <strong>Order</strong>
                            <div t-field="o.origin" class="m-0">S0001</div>
                        </div>
                        <div t-if="o.state" class="col col-3 mw-100 mb-2" name="div_sched_date">
                            <strong>Shipping Date</strong>
                            <div t-if="o.state == 'done'" t-field="o.date_done" class="m-0"/>
                            <div t-else="" t-field="o.scheduled_date" class="m-0"/>
                        </div>
                    </div>
                    <div class="oe_structure"></div>
                    <table class="table table-borderless" t-if="o.state!='done'" name="stock_move_table">
                        <thead>
                            <tr>
                                <th name="th_sm_product"><strong>Product</strong></th>
                                <th name="th_sm_ordered" class="text-end"><strong>Ordered</strong></th>
                                <th name="th_sm_quantity" class="text-end"><strong>Delivered</strong></th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-set="lines" t-value="o.move_ids.filtered(lambda x: x.product_uom_qty)"/>
                            <tr t-foreach="lines" t-as="move">
                                <td>
                                    <span t-field="move.product_id">Customizable Desk</span>
                                    <p t-if="move.description_picking and move.description_picking != move.product_id.name and move.description_picking != move.product_id.display_name">
                                        <span t-field="move.description_picking">Description on transfer</span>
                                    </p>
                                </td>
                                <td class="o_td_quantity text-end">
                                    <span t-field="move.product_uom_qty" class="text-nowrap">3.00</span>
                                    <span t-field="move.product_uom" groups="uom.group_uom">units</span>
                                    <span t-if="move.product_packaging_id">
                                        (<span t-field="move.product_packaging_qty" t-options='{"widget": "integer"}'/> <span t-field="move.product_packaging_id"/>)
                                    </span>
                                </td>
                                <td class="o_td_quantity text-end">
                                    <span t-field="move.quantity" class="text-nowrap">3.00</span>
                                    <span t-field="move.product_uom" groups="uom.group_uom">units</span>
                                    <span t-if="move.product_packaging_id">
                                        (<span t-field="move.product_packaging_quantity" t-options='{"widget": "integer"}'/> <span t-field="move.product_packaging_id"/>)
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="table table-borderless mt48" t-elif="o.move_line_ids and o.state=='done'" name="stock_move_line_table">
                        <t t-set="has_serial_number" t-value="False"/>
                        <t t-set="has_serial_number" t-value="o.move_line_ids.mapped('lot_id')" groups="stock.group_lot_on_delivery_slip"/>
                        <thead>
                            <tr>
                                <th name="th_sml_product">Product</th>
                                <th name="th_sml_qty_ordered" class="text-center" t-if="not has_serial_number">Ordered</th>
                                <th name="lot_serial" t-else="">Lot/Serial Number</th>
                                <th name="th_sml_quantity" class="text-center">Delivered</th>
                            </tr>
                        </thead>
                        <tbody>
                        <!-- This part gets complicated with different use cases (additional use cases in extensions of this report):
                                1. If serial numbers are used and set to print on delivery slip => print lines as is, otherwise group them by overlapping
                                    product + description + uom combinations
                                2. If any packages are assigned => split products up by package (or non-package) and then apply use case 1 -->
                            <!-- If has destination packages => create sections of corresponding products -->
                            <t t-if="o.has_packages" name="has_packages">
                                <t t-set="packages" t-value="o.move_line_ids.mapped('result_package_id')"/>
                                <t t-foreach="packages" t-as="package">
                                    <t t-call="stock.stock_report_delivery_package_section_line"/>
                                    <t t-set="package_move_lines" t-value="o.move_line_ids.filtered(lambda l: l.result_package_id == package)"/>
                                    <!-- If printing lots/serial numbers => keep products in original lines -->
                                    <t t-if="has_serial_number">
                                        <tr t-foreach="package_move_lines" t-as="move_line">
                                            <t t-call="stock.stock_report_delivery_has_serial_move_line"/>
                                        </tr>
                                    </t>
                                    <!-- If not printing lots/serial numbers => merge lines with same product+description+uom -->
                                    <t t-else="">
                                        <t t-set="aggregated_lines" t-value="package_move_lines._get_aggregated_product_quantities(strict=True)"/>
                                        <t t-call="stock.stock_report_delivery_aggregated_move_lines"/>
                                    </t>
                                </t>
                                <!-- Make sure we do another section for package-less products if they exist -->
                                <t t-set="move_lines" t-value="o.move_line_ids.filtered(lambda l: not l.result_package_id)"/>
                                <t t-set="aggregated_lines" t-value="o.move_line_ids._get_aggregated_product_quantities(except_package=True)"/>
                                <t t-if="move_lines or aggregated_lines" name="no_package_move_lines">
                                    <t t-call="stock.stock_report_delivery_no_package_section_line" name="no_package_section"/>
                                    <t t-if="has_serial_number">
                                        <tr t-foreach="move_lines" t-as="move_line">
                                            <t t-call="stock.stock_report_delivery_has_serial_move_line"/>
                                        </tr>
                                    </t>
                                    <t t-elif="aggregated_lines">
                                        <t t-call="stock.stock_report_delivery_aggregated_move_lines"/>
                                    </t>
                                </t>
                            </t>
                            <!-- No destination packages -->
                            <t t-else="">
                                <!-- If printing lots/serial numbers => keep products in original lines -->
                                <t t-if="has_serial_number">
                                    <tr t-foreach="o.move_line_ids" t-as="move_line">
                                        <t t-call="stock.stock_report_delivery_has_serial_move_line"/>
                                    </tr>
                                </t>
                                <!-- If not printing lots/serial numbers => merge lines with same product -->
                                <t t-else="" name="aggregated_move_lines">
                                    <t t-set="aggregated_lines" t-value="o.move_line_ids._get_aggregated_product_quantities()"/>
                                    <t t-call="stock.stock_report_delivery_aggregated_move_lines"/>
                                </t>
                            </t>
                        </tbody>
                    </table>
                    <div class="oe_structure"></div>
                    <t t-set="backorders" t-value="o.backorder_ids.filtered(lambda x: x.state not in ('done', 'cancel'))"/>
                    <div t-if="not (o.backorder_ids and backorders)" class="oe_structure"></div>
                    <div t-else="">
                        <p class="mt-5">
                            <span>Remaining quantities not yet delivered:</span>
                        </p>
                        <table class="table table-borderless" name="stock_backorder_table">
                            <thead>
                                <tr>
                                    <th name="th_sb_product"><strong>Product</strong></th>
                                    <th/>
                                    <th name="th_sb_quantity" class="text-center"><strong>Quantity</strong></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="backorders.mapped('move_ids').filtered(lambda x: x.product_uom_qty)" t-as="bo_line">
                                    <td class="w-auto">
                                        <span t-field="bo_line.product_id">Office Chair</span>
                                        <p t-if="bo_line.description_picking and bo_line.description_picking != bo_line.product_id.name and bo_line.description_picking != bo_line.product_id.display_name">
                                            <span t-field="bo_line.description_picking">Description on transfer</span>
                                        </p>
                                    </td>
                                    <td/>
                                    <td class="text-center w-auto">
                                        <span t-field="bo_line.product_uom_qty">3.00</span>
                                        <span t-field="bo_line.product_uom" groups="uom.group_uom">units</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div t-if="not o.signature" class="oe_structure"></div>
                    <div t-else="" class="mt32 ml64 mr4" name="signature">
                        <div class="offset-8">
                            <strong>Signature</strong>
                        </div>
                        <div class="offset-8">
                            <img t-att-src="image_data_uri(o.signature)" style="max-height: 4cm; max-width: 8cm;"/>
                        </div>
                        <div t-if="o.partner_id.name" class="offset-8 text-center">
                            <p t-field="o.partner_id.name">John Doe</p>
                        </div>
                    </div>
                </div>
            </t>
         </t>
    </template>

    <!-- templates for easier extension + cut back on repeat code due to multiple conditionals -->
    <!-- move line(s) printing for tables -->
    <template id="stock_report_delivery_has_serial_move_line">
        <td>
            <span t-field="move_line.product_id"/>
            <!-- this is an annoying workaround for the multiple types of descriptions (often auto-filled) that we do not want to print -->
            <!-- this makes it so we can pre-filter the descriptions in inherited templates since we cannot extend the standard "if" condition -->
            <!-- let's agree that pre-filtered descriptions will be set to "" -->
            <t t-if="not description and description != ''">
                <t t-set="description" t-value="move_line.move_id.description_picking"/>
            </t>
            <p t-if="description != '' and
                     description != move_line.product_id.display_name and
                     description != move_line.product_id.name"
            >
                <span t-esc="description"/>
            </p>
        </td>
        <t t-if="has_serial_number" name="move_line_lot">
            <td><span t-field="move_line.lot_id.name"/></td>
        </t>
        <td class="text-center" name="move_line_lot_quantity">
            <span t-field="move_line.quantity"/>
            <span t-field="move_line.product_uom_id"/>
            <span t-if="move_line.move_id.product_packaging_id" groups="product.group_stock_packaging">
                (<span t-field="move_line.product_packaging_qty" t-options='{"widget": "integer"}'/> <span t-field="move_line.move_id.product_packaging_id.display_name"/>)
            </span>
        </td>
    </template>
    <template id="stock_report_delivery_aggregated_move_lines">
        <tr t-foreach="aggregated_lines" t-as="line">
            <td>
                <span t-esc="aggregated_lines[line]['name']"/>
                <p t-if="aggregated_lines[line]['description']">
                    <span t-esc="aggregated_lines[line]['description']"  t-options="{'widget': 'text'}"/>
                </p>
            </td>
            <td class="text-center" name="move_line_aggregated_qty_ordered">
                <span t-esc="aggregated_lines[line]['qty_ordered']"
                    t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"/>
                <span t-esc="aggregated_lines[line]['product_uom'].name"/>
                <span t-if="aggregated_lines[line]['packaging'].name">
                    (<span t-out="aggregated_lines[line]['packaging_qty']" t-options='{"widget": "integer"}'/> <span t-out="aggregated_lines[line]['packaging'].name"/>)
                </span>
            </td>
            <td class="text-center" name="move_line_aggregated_quantity">
                <t t-if="aggregated_lines[line]['quantity']">
                    <span t-esc="aggregated_lines[line]['quantity']"
                        t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"/>
                    <span t-esc="aggregated_lines[line]['product_uom'].name"/>
                    <span t-if="aggregated_lines[line]['packaging'].name">
                        (<span t-out="aggregated_lines[line]['packaging_quantity']" t-options='{"widget": "integer"}'/> <span t-out="aggregated_lines[line]['packaging'].name"/>)
                    </span>
                </t>
            </td>
        </tr>
    </template>

    <!-- package related "section lines" -->
    <template id="stock_report_delivery_package_section_line">
        <tr t-att-class="'fw-bold o_line_section'">
            <td colspan="99" name="package_info">
                <span t-field="package.name"/>
            </td>
        </tr>
    </template>
    <template id="stock_report_delivery_no_package_section_line">
        <tr t-att-class="'fw-bold o_line_section'">
            <td colspan="99" name="no_package_info">
                <span>Products with no package assigned</span>
            </td>
        </tr>
    </template>

    <template id="report_deliveryslip">
        <t t-foreach="docs" t-as="o">
            <t t-call="stock.report_delivery_document" t-lang="o._get_report_lang()"/>
        </t>
    </template>
</odoo>
