<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_mrp_production_split_multi_form" model="ir.ui.view">
            <field name="name">mrp.production.split.multi.form</field>
            <field name="model">mrp.production.split.multi</field>
            <field name="arch" type="xml">
                <form string="Split Productions">
                    <field name="production_ids">
                        <list create="0" editable="top">
                            <field name="production_id"/>
                            <field name="product_id"/>
                            <field name="product_qty"/>
                            <field name="production_capacity"/>
                            <field name="product_uom_id" groups="uom.group_uom"/>
                            <button name="action_prepare_split" type="object" icon="fa-scissors" title="Split Production"/>
                        </list>
                    </field>
                    <footer>
                        <button string="Discard" class="btn-secondary" special="cancel" data-hotkey="x"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="view_mrp_production_split_form" model="ir.ui.view">
            <field name="name">Split Production</field>
            <field name="model">mrp.production.split</field>
            <field name="arch" type="xml">
                <form string="Split Production">
                    <group>
                        <group>
                            <field name="production_id" readonly="1"/>
                        </group>
                        <group>
                            <field name="product_id"/>
                            <label for="product_qty"/>
                            <div class="o_row">
                                <span><field name="product_qty"/></span>
                                <span><field name="product_uom_id" groups="uom.group_uom"/></span>
                            </div>
                        </group>
                        <group>
                            <field name="counter"/>
                        </group>
                    </group>
                    <field name="production_detailed_vals_ids" invisible="counter == 0">
                        <list editable="top">
                            <field name="date"/>
                            <field name="user_id"/>
                            <field name="quantity"/>
                        </list>
                    </field>
                    <field name="production_split_multi_id" invisible="1"/>
                    <field name="valid_details" invisible="1"/>
                    <div class="alert alert-danger" role="alert" invisible="valid_details or counter == 0">
                        The total should be equal to the quantity to produce.
                    </div>
                    <footer>
                        <button string="Split" class="btn-primary" type="object" name="action_split" data-hotkey="q" invisible="not valid_details"/>
                        <button string="Discard" class="btn-secondary" special="cancel" data-hotkey="x" invisible="production_split_multi_id"/>
                        <button string="Discard" class="btn-secondary" type="object" name="action_return_to_list" data-hotkey="x" invisible="not production_split_multi_id"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_mrp_production_split_multi" model="ir.actions.act_window">
            <field name="name">Split productions</field>
            <field name="res_model">mrp.production.split.multi</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

        <record id="action_mrp_production_split" model="ir.actions.act_window">
            <field name="name">Split production</field>
            <field name="res_model">mrp.production.split</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

    </data>
</odoo>
