# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2025\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__state
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_state
msgid ""
" * Draft: The MO is not confirmed yet.\n"
" * Confirmed: The MO is confirmed, the stock rules and the reordering of the components are trigerred.\n"
" * In Progress: The production has started (on the MO or on the WO).\n"
" * To Close: The production is done, the MO has to be closed.\n"
" * Done: The MO is closed, the stock moves are posted. \n"
" * Cancelled: The MO has been cancelled, can't be confirmed anymore."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid " <br/><br/> The components will be taken from <b>%s</b>."
msgstr " <br/><br/> Komponentlər götürülməlidir <b>%s</b>."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid ""
" <br/><br/> The manufactured products will be moved towards "
"<b>%(destination)s</b>, <br/> as specified from <b>%(operation)s</b> "
"destination."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__all_available
msgid " When all components are available"
msgstr " Bütün komponentlər mövcud olduğu zaman"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_count
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_count
msgid "# Bill of Material"
msgstr "# Məhsul Resepti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__used_in_bom_count
msgid "# BoM Where Used"
msgstr "# İstifadə olunarsa, Məhsul Resepti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_ready_count
msgid "# Ready Work Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_count
msgid "# Work Orders"
msgstr "# İş sifarişləri"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_template__used_in_bom_count
msgid "# of BoM Where is Used"
msgstr "# istifadə olunarsa, Məhsul Reseptinin"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "%(name)s Sequence picking before manufacturing"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "%(name)s Sequence production"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "%(name)s Sequence stock after manufacturing"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "%(producible_qty)s Ready"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "%(product)s: Insufficient Quantity To Unbuild"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "%(product_name)s (new) %(number_of_boms)s"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "%(qty)s %(measure)s unbuilt in %(order)s"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
msgid "%i work orders"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "%s Child MO's"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "%s cannot be deleted. Try to cancel them before."
msgstr "%s silinə bilməz. Onları daha öncə ləğv etməyə çalış."

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_bom_structure
msgid "'Bom Overview - %s' % object.display_name"
msgstr ""

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_finished_product
msgid "'Finished products - %s' % object.name"
msgstr "Bitmiş məhsullar - %s' % object.name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_mrp_mo_overview
msgid "'MO Overview - %s' % object.display_name"
msgstr ""

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_production_order
msgid "'Production Order - %s' % object.name"
msgstr "İstehsal sifarişi - %s' % object.name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_workorder
msgid "'Work Order - %s' % object.name"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "(in"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+ %d gün(lər)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "- Overview"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"            Əllə hərəkətlərin edilməsi lazım ola bilər."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_steps
msgid ""
"1 Step: Consume components from stock and produce.\n"
"              2 Steps: Pick components from stock and then produce.\n"
"              3 Steps: Pick components from stock, produce, and then move final product(s) from production area to stock."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "100"
msgstr "100"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "12345678901"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_leg_product_template
msgid "18″ x 2½″ Square Leg"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "2023-09-15"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "25"
msgstr "25"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "50"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "58"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "60"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "8 GB RAM"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "987654321098"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/workcenter_dashboard_graph/workcenter_dashboard_graph_field.js:0
msgid ":  hours"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pause\" title=\"Pause\"/>"
msgstr "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pauza\" title=\"Pauza\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-play fs-6\" role=\"img\" aria-label=\"Run\" title=\"Run\"/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"Stop\" title=\"Stop\"/>"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Upload files to your product\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                         faylları məhsulunuza yükləyin \n"
"                    </p><p>\n"
"                         qrafik təsvirlər  kimi hər hansı bir faylı saxlamaq üçün bu funksiyanı istifadə edin.\n"
"                    </p>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">In Progress</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">Late</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">To Close</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">Waiting</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"fw-bold text-nowrap\">To Produce</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Backorders</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "<span class=\"o_stat_text\">BoM Overview</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Child MO</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Load</span>"
msgstr "<span class=\"o_stat_text\">Yüklə</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Lost</span>"
msgstr "<span class=\"o_stat_text\">İtirildi</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "<span class=\"o_stat_text\">Manufactured</span>"
msgstr "<span class=\"o_stat_text\">İstehsal olundu</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_form_inherit_mrp
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">OEE</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Operations</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "<span class=\"o_stat_text\">Operations<br/>Performance</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Overview</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Performance</span>"
msgstr "<span class=\"o_stat_text\">Keyfiyyət</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "<span class=\"o_stat_text\">Product Moves</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span class=\"o_stat_text\">Scraps</span>"
msgstr "<span class=\"o_stat_text\">Fraqmentlər</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Source MO</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Traceability</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Unbuilds</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid ""
"<span class=\"text-muted\">Modifying the quantity to produce will also "
"modify the quantities of components to consume for this manufacturing "
"order.</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid ""
"<span>\n"
"            Components\n"
"        </span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span>&amp;nbsp;(minutes)</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Actions</span>"
msgstr "<span>Fəaliyyətlər</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span>Generate BOM</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>New</span>"
msgstr "<span>Yeni</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>Orders</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>PLAN ORDERS</span>"
msgstr "<span>SİFARİŞLƏRİN PLANI</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_report_delivery_no_kit_section
msgid "<span>Products not associated with a kit</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Mühasibat uçotu</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>WORK ORDERS</span>"
msgstr "<span>İŞ SİFARİŞLƏRİ</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span>minutes</span>"
msgstr "<span>Dəqiqələr</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<strong class=\"mr8 oe_inline\">to</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Actual Duration (minutes)</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Barcode</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Deadline:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Description:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Duration (minutes)</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Effectiveness Category: </strong>"
msgstr "<strong>Səmərəlilik Kateqoriyası: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "<strong>Finished Product:</strong><br/>"
msgstr "<strong>Bitmiş Məhsul:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Is a Blocking Reason? </strong>"
msgstr "<strong>Bloklama Səbəbinə görə? </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "<strong>Manufacturing Order:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Operation</strong>"
msgstr "<strong>Əməliyyat</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Product:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity Producing:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity to Produce:</strong><br/>"
msgstr "<strong>İstehsal Miqdarı:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Reason: </strong>"
msgstr "<strong>Səbəb: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Responsible:</strong><br/>"
msgstr "<strong>Məsuldur:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Source:</strong><br/>"
msgstr "<strong>Mənbə:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "<strong>Unit Cost</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>WorkCenter</strong>"
msgstr "<strong>İş mərkəzi</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"A BoM of type Kit is not produced with a manufacturing order.<br/>\n"
"                                Instead, it is used to decompose a BoM into its components when:"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "A Manufacturing Order is already done or cancelled."
msgstr "İstehsal sifarişləri ya artıq yerinə yetirilib və ya ləğv edilib."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid ""
"A product with a kit-type bill of materials can not have a reordering rule."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_rule__action
msgid "Action"
msgstr "Əməliyyat"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_needaction
msgid "Action Needed"
msgstr "Gərəkli Əməliyyat"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__active
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__active
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__active
msgid "Active"
msgstr "Aktiv"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_ids
msgid "Activities"
msgstr "Fəaliyyətlər"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_exception_decoration
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Faəliyyət istisnaetmə İşarəsi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_state
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_state
msgid "Activity State"
msgstr "Fəaliyyət Statusu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_type_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_icon
msgid "Activity Type Icon"
msgstr "Fəaliyyət Növü ikonu"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Add a description..."
msgstr "Təsvir əlavə et..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Add a line"
msgstr "Sətir əlavə edin"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Add by-products to bills of materials. This can be used to get several "
"finished products as well. Without this option you only do: A + B = C. With "
"the option: A + B = C + D."
msgstr ""
"Məhsullara görə material fakturası əlavə et Bu, bir neçə bitmiş məhsul əldə "
"etmək üçün də istifadə edilə bilər. Bu seçim olmadan yalnız: A + B = C. "
"Seçim olduqda isə: A + B = C + D et."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Add quality checks to your work orders"
msgstr "Sifarişiniz üçün keyfiyyətə nəzarət əlavə et"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_tag
msgid "Add tag for the workcenter"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_manager
msgid "Administrator"
msgstr "Administrator"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__after
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "After"
msgstr "Sonra"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "All"
msgstr "BÜTÜN"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__all_move_ids
msgid "All Move"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__all_move_raw_ids
msgid "All Move Raw"
msgstr ""

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_line_bom_qty_zero
msgid ""
"All product quantities must be greater or equal to 0.\n"
"Lines with 0 quantities can be used as optional lines. \n"
"You should install the mrp_byproduct module if you want to manage extra products on BoMs!"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Allocation"
msgstr "Məzuniyyət payı"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Allocation Report"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Allocation Report Labels"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_reception_report
msgid "Allocation Report for Manufacturing Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__allow_workorder_dependencies
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__allow_workorder_dependencies
msgid "Allow Work Order Dependencies"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Allow manufacturing users to modify quantities to consume, without the need "
"for prior approval"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,help:mrp.field_stock_picking_type__use_create_components_lots
msgid "Allow to create new lot/serial numbers for the components"
msgstr "Komponentlər üçün yeni lot / serial nömrələri yaratmağa icazə verin"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__flexible
msgid "Allowed"
msgstr "İcazə Verilib"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reserve_visible
msgid "Allowed to Reserve Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unreserve_visible
msgid "Allowed to Unreserve Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__warning
msgid "Allowed with warning"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid "Alternative Workcenters"
msgstr "Alternativ İş mərkəzləri"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid ""
"Alternative workcenters that can be substituted to this one in order to "
"dispatch production"
msgstr ""
"İstehsalı göndərmək üçün bununla əvəz edilə bilən alternativ iş mərkəzləri"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid ""
"An unbuild order is used to break down a finished product into its "
"components."
msgstr ""
"Ləğv edilmiş sifariş, hazır məhsulu tərkib hissələrinə ayırmaq üçün istifadə"
" olunur."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "Apply on Variants"
msgstr "Variantlar üzrə tətbiq edin"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Archive Operation"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Archived"
msgstr "Arxivləndi"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Are you sure you want to cancel this manufacturing order?"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Assembling"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_attachment_count
msgid "Attachment Count"
msgstr "Qoşma Sayı"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.js:0
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Attachments"
msgstr "Qoşmalar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__attachments_count
msgid "Attachments Count"
msgstr "Qoşmaların Sayı"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_variant_attributes
msgid "Attribute Values"
msgstr "Atribut Dəyərləri"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_mrp_reception_report
msgid "Auto Print Allocation Report"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_mrp_reception_report_labels
msgid "Auto Print Allocation Report Labels"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_done_production_order
msgid "Auto Print Done Production Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_generated_mrp_lot
msgid "Auto Print Generated Lot/SN Label"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_done_mrp_lot
msgid "Auto Print Produced Lot Label"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_done_mrp_product_labels
msgid "Auto Print Produced Product Labels"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_generated_mrp_lot
msgid ""
"Automatically print the lot/SN label when the \"Create a new serial/lot "
"number\" button is used."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Availabilities"
msgstr "Mövcudluqlar"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Availabilities on products."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__availability
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Availability"
msgstr "Əlçatanlıq"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Availability Losses"
msgstr "Zərərlərin Mövcudluğu"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__available
msgid "Available"
msgstr "Mövcud"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "Avatar"
msgstr "avatar"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__produce_delay
msgid ""
"Average lead time in days to manufacture this product. In the case of multi-"
"level BOM, the manufacturing lead times of the components will be added. In "
"case the product is subcontracted, this can be used to determine the date at"
" which components should be sent to the subcontractor."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Avg Cost of Components per Unit"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Avg Cost of Operations per Unit"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Avg Total Cost per Unit"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__variant_bom_ids
msgid "BOM Product Variants"
msgstr "Məhsul reseptinin Məhsul Variantları"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "BOM Product Variants needed to apply this line."
msgstr ""
"Məhsul reseptinin Məhsul Variantları bu xəttə tətbiq olunmaq üçün lazımdır."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_line_ids
msgid "BOM lines of the referred bom"
msgstr "İstinad olunan Məhsul reseptinin Məhsul resepti xətləri"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/product_catalog/kanban_controller.js:0
msgid "Back to BoM"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/product_catalog/kanban_controller.js:0
msgid "Back to Production"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder_line
msgid "Backorder Confirmation Line"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_backorder_line_ids
msgid "Backorder Confirmation Lines"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Backorder MO"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Backorder MO's"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__backorder_sequence
msgid "Backorder Sequence"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__backorder_sequence
msgid ""
"Backorder sequence, if equals to 0 means there is not related backorder"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__barcode
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Barcode"
msgstr "Barkod"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode_batch
msgid "Based on"
msgstr "Əsasən"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_batch_produce
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__production_text
msgid "Batch Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__before
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Before"
msgstr "Əvvəl"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom
#: model:ir.model.fields,field_description:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_bom_id
#: model:ir.model.fields,field_description:mrp.field_product_replenish__bom_id
#: model:ir.model.fields,field_description:mrp.field_stock_replenish_mixin__bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Bill of Material"
msgstr "Məhsul Resepti"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "Məhsul Resepti siyahısı"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
msgid "Bill of Material line"
msgstr "Məhsul Resepti siyahısı"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_bom_id
msgid "Bill of Material used on the Production Order"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_open_bom
#: model:ir.actions.act_window,name:mrp.template_open_bom
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_ids
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__bom_id
#: model:ir.model.fields.selection,name:mrp.selection__product_document__attached_on_mrp__bom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Bill of Materials"
msgstr "Məhsul Reseptləri"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_bom_form_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom_form_action
msgid "Bills of Materials"
msgstr "Materialların Spesifikasiyalarının Siyahıları"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__mo_bom_id
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_bom_id
msgid ""
"Bills of Materials, also called recipes, are used to autocomplete components"
" and work order instructions."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid ""
"Bills of materials allow you to define the list of required raw\n"
"                materials used to make a finished product; through a manufacturing\n"
"                order or a pack of products."
msgstr ""
"Materialların Spesifikasiyalarının Siyahıları hazır məhsul \n"
"                hazırlamaq üçün istifadə olunan tələb olunan xammal siyahısını müəyyənləşdirməyə imkan verir;\n"
"                 istehsal sifarişi və ya məhsul paketi vasitəsilə."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.js:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block"
msgstr "Blok"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter_wo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block Workcenter"
msgstr "Blok İş Mərkəzi"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__blocked
msgid "Blocked"
msgstr "Bloklanıb"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__blocked_by_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__blocked_by_workorder_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Blocked By"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked Time"
msgstr "Qadağan olunmuş vaxt"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked hours over the last month"
msgstr "Son bir ay ərzində qadağan edilmiş saatlar "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__name
msgid "Blocking Reason"
msgstr "Bloklama Səbəbi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__needed_by_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__needed_by_workorder_ids
msgid "Blocks"
msgstr "Bloklar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_id
msgid "BoM"
msgstr "Məhsul Resepti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_line_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_line_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "BoM Components"
msgstr "Məhsul Reseptinin Komponentləri"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Cost"
msgstr "Məhsul Reseptinin Dəyəri"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "BoM Costs"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__bom_line_id
msgid "BoM Line"
msgstr "Məhsul Reseptinin Xətti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__bom_line_ids
msgid "BoM Lines"
msgstr "Məhsul Reseptinin Xətləri"

#. module: mrp
#: model:ir.actions.client,name:mrp.action_report_mrp_bom
#: model:ir.actions.report,name:mrp.action_report_bom_structure
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Overview"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__type
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "BoM Type"
msgstr "Məhsul Reseptinin Növü"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_bolt_product_template
msgid "Bolt"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_byproducts_block/mrp_mo_overview_byproducts_block.xml:0
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_byproducts
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_byproducts
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "By-Products"
msgstr "Məhsullara görə"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_id
msgid "By-product"
msgstr "Məhsula görə"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "By-product %s should not be the same as BoM product."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__byproduct_id
msgid "By-product line that generated the move in a manufacturing order"
msgstr "İstehsal qaydasında hərəkəti yaradan yan məhsul xətti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__byproduct_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__byproduct_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "By-products"
msgstr "Məhsullara görə"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
msgid "By-products cost shares must be positive."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_byproduct
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_byproduct_form_view
msgid "Byproduct"
msgstr "Yan məhsul"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
msgid "Byproducts"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Can't find any production location."
msgstr "Heç bir istehsal yeri tapılmır."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Cancel"
msgstr "Ləğv edin"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__cancel
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__cancel
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Cancelled"
msgstr "Ləğv olundu"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"Cannot compute days to prepare due to missing route info for at least 1 "
"component or for the final product."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Cannot delete a manufacturing order in done state."
msgstr "Bitmiş vəziyyətdə istehsal sifarişini silmək olmur."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__default_capacity
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__capacity
msgid "Capacity"
msgstr "Həcm "

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_capacity_positive_capacity
msgid "Capacity should be a positive number."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_reported_from_previous_wo
msgid "Carried Quantity"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Catalog"
msgstr "Kataloq"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__loss_type
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Category"
msgstr "Kateqoriya"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Center A"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Change Product Qty"
msgstr "Məhsulun Miqdarını Dəyişin"

#. module: mrp
#: model:ir.model,name:mrp.model_change_production_qty
msgid "Change Production Qty"
msgstr "İstehsalat Miqdarını Dəyişin"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_change_production_qty
msgid "Change Quantity To Produce"
msgstr "İstehsal Kəmiyyətini Dəyişin"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"Changing the product or variant will permanently reset all previously "
"encoded variant-related data."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Check availability"
msgstr "Mövcudluğunu yoxlayın"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/wizard/stock_label_type.py:0
msgid "Choose Labels Layout"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Choose Type of Labels To Print"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_picking_label_type
msgid "Choose whether to print product or lot/sn labels"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_stop
msgid "Cleanup Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__time_stop
msgid "Cleanup Time (minutes)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__code
msgid "Code"
msgstr "Kod"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__color
msgid "Color"
msgstr "Rəng"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__color
msgid "Color Index"
msgstr "Rəng İndeksi"

#. module: mrp
#: model:ir.model,name:mrp.model_res_company
msgid "Companies"
msgstr "Şirkətlər"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__company_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Company"
msgstr "Şirkət"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Component"
msgstr "Komponent"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability
msgid "Component Status"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
msgid "Component of Draft MO"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__component_separator
msgid "Component separator"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_raw_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Components"
msgstr "Komponentlər"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability_state
msgid "Components Availability State"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Components Available"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_src_id
msgid "Components Location"
msgstr "Komponentlərin Lokalizasiyası"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__priority
msgid ""
"Components will be reserved first for the MO with the highest priorities."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Compute"
msgstr "Hesablamaq"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__auto
msgid "Compute based on tracked time"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"Compute the days required to resupply all components from BoM, by either "
"buying or manufacturing the components and/or subassemblies."
"                                                       Also note that "
"purchase security lead times will be added when appropriate."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_computed_on
msgid "Computed on last"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_res_config_settings
msgid "Config Settings"
msgstr "Parametrləri Konfiqurasiya edin"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_configuration
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Configuration"
msgstr "Konfiqurasiya"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Confirm"
msgstr "Təsdiq edin"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Confirmed"
msgstr "Təsdiq olundu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_consumed_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Consumed"
msgstr "İstehlak"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__consume_line_ids
msgid "Consumed Disassembly Lines"
msgstr "İstehlak Xətləri Sökmə"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__consume_unbuild_id
msgid "Consumed Disassembly Order"
msgstr "İstehlak Sökmə Sifarişi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__operation_id
msgid "Consumed in Operation"
msgstr "Əməliyyatda İstehlak olunur"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_production__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__consumption
msgid "Consumption"
msgstr "İstehlak"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_consumption_warning
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Consumption Warning"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_production__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Ölçü vahidləri arasındakı konversiya yalnız onlar eyni kateqoriyaya aid "
"olduqda baş verə bilər. Konversiya nisbətlərə əsasən həyata keçiriləcək."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Copy Existing Operations"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_copy_to_bom_tree_view
msgid "Copy selected operations"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Cost Breakdown of Products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__cost_share
#: model:ir.model.fields,field_description:mrp.field_stock_move__cost_share
msgid "Cost Share (%)"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost as it is currently accumulated"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost based on cost projection"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost based on related replenishments. Otherwise cost from product form"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost based on the BoM"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Cost of Components per unit"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Cost of Operations per unit"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__costs_hour
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__costs_hour
msgid "Cost per hour"
msgstr "Saatda dəyəri"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Costing Information"
msgstr "Xərclər haqqında Məlumat"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
msgid "Costs"
msgstr "Dəyərlər"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking__production_count
msgid "Count of MO generated"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_backorder_count
msgid "Count of linked backorder"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__use_create_components_lots
msgid "Create New Lots/Serial Numbers for Components"
msgstr "Komponentlər üçün Yeni Partiyalar/ Seriya Nömrələri yarat"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create a Backorder"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid ""
"Create a backorder if you expect to process the remaining products later. Do"
" not create a backorder if you will not process the remaining products."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid "Create a new operation"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid "Create a new work center"
msgstr "Yeni iş mərkəzi yarat"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_workcenter_report
msgid "Create a new work orders performance"
msgstr "Yeni iş sifarişləri icrası yarat"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__days_to_prepare_mo
msgid ""
"Create and confirm Manufacturing Orders this many days in advance, to have enough time to replenish components or manufacture semi-finished products.\n"
"Note that security lead times will also be considered when appropriate."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create backorder"
msgstr "Təxirə salınmış sifariş yarat"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__allow_operation_dependencies
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__allow_operation_dependencies
msgid ""
"Create operation level dependencies that will influence both planning and "
"the status of work orders upon MO confirmation. If this feature is ticked, "
"and nothing is specified, Odoo will assume that all operations can be "
"started simultaneously."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__created_production_id
msgid "Created Production Order"
msgstr "Yaradılmış Məhsul Sifarişləri"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_uid
msgid "Created by"
msgstr "Tərəfindən yaradılıb"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_date
msgid "Created on"
msgstr "Tarixdə yaradıldı"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Creates a new serial/lot number"
msgstr "Yeni seriya/ partiya nömrəsini yaradır"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__currency_id
msgid "Currency"
msgstr "Valyuta"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_producing
msgid "Currently Produced Quantity"
msgstr "Hazırda İstehsal Olunan Miqdar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_description_variants
msgid "Custom Description"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Date"
msgstr "Tarix"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__search_date_category
msgid "Date Category"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Date by Month"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_finished
msgid ""
"Date you expect to finish production or actual date you finished production."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_start
msgid ""
"Date you plan to start production or date you actually started production."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Date: Last 365 Days"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_pdf_line
msgid "Days"
msgstr "günlər"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "Days to Supply Components"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__days_to_prepare_mo
msgid "Days to prepare Manufacturing Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_deadline
msgid "Deadline"
msgstr "Son Tarix"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Default Duration"
msgstr "Susmaya Görə Davametmə Müddəti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__use_manufacturing_lead
msgid "Default Manufacturing Lead Time"
msgstr "İstehsalın Həyata Keçirildiyi Susmaya Görə Müddət"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__default_capacity
msgid ""
"Default number of pieces (in product UoM) that can be produced in parallel "
"(at the same time) at this work center. For example: the capacity is 5 and "
"you need to produce 10 units, then the operation time listed on the BOM will"
" be multiplied by two. However, note that both time before and after "
"production will only be counted once."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Bütün anbar əməliyyatları üçün istifadə olunan standart ölçü vahidi."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid ""
"Define the components and finished products you wish to use in\n"
"                bill of materials and manufacturing orders."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__resource_calendar_id
msgid ""
"Define the working schedule of the resource. If not set, the resource will "
"have fully flexible working hours."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__consumption
msgid ""
"Defines if you can consume more or less components than the quantity defined on the BoM:\n"
"  * Allowed: allowed for all manufacturing users.\n"
"  * Allowed with warning: allowed for all manufacturing users with summary of consumption differences when closing the manufacturing order.\n"
"  Note that in the case of component Highlight Consumption, where consumption is registered manually exclusively, consumption warnings will still be issued when appropriate also.\n"
"  * Blocked: only a manager can close a manufacturing order when the BoM consumption is not respected."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delay_alert_date
msgid "Delay Alert Date"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Delayed Productions"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Delegate part of the production process to subcontractors"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delivery_count
msgid "Delivery Orders"
msgstr "Çatdırılma Sifarişləri"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__description
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_note
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description"
msgstr "Təsvir"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description of the work center..."
msgstr "İş mərkəzinin təsviri..."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_dest_id
msgid "Destination Location"
msgstr "Təyinat Məkanı"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unbuild_id
msgid "Disassembly Order"
msgstr "Sökmə Sifarişi"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Discard"
msgstr "Ləğv edin"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.xml:0
msgid "Display"
msgstr "Göstərin"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__display_name
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__display_name
msgid "Display Name"
msgstr "Göstəriləcək Ad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lot_ids
msgid "Display the serial number shortcut on the moves"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Displays the consumed Lot/Serial Numbers."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Displays the produced Lot/Serial Numbers."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "Do you confirm you want to unbuild"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Documentation"
msgstr "Sənədləşmə"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.xml:0
#: model:ir.model.fields,field_description:mrp.field_stock_move__is_done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Done"
msgstr "Hazırdır"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__draft
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__draft
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Draft"
msgstr "Qaralama"

#. module: mrp
#: model:product.template,name:mrp.product_product_drawer_drawer_product_template
msgid "Drawer Black"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_drawer_case_product_template
msgid "Drawer Case Black"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_drawer_drawer_product_template
msgid "Drawer on casters for great usability."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__duration
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Duration"
msgstr "Müddət"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Duration (minutes)"
msgstr "Müddət (dəqiqələr)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode
msgid "Duration Computation"
msgstr "Hesablama Müddəti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_percent
msgid "Duration Deviation (%)"
msgstr "Yayınma Müddəti (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_unit
msgid "Duration Per Unit"
msgstr "Vahid Müddəti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_type
msgid "Effectiveness"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_type
msgid "Effectiveness Category"
msgstr "Effektivlik Kateqoriyası"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_finished
msgid "End"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_end
msgid "End Date"
msgstr "Bitmə Tarixi"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__tracking
#: model:ir.model.fields,help:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,help:mrp.field_mrp_workorder__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Anbarınızda  saxlanıla bilən məhsulunun izlənməsini təmin edin."

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason1
msgid "Equipment Failure"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Estimated %s"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s) occurred on the manufacturing order(s):"
msgstr "İstehsal sifariş(ləri) üzrə yaranan İstisna(lar)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s):"
msgstr "İstisna(lar)"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Exp %s"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__expected
msgid "Expected"
msgstr "Ehtimal edilən"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Expected %s"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__duration_expected
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_expected
msgid "Expected Duration"
msgstr "Ehtimal edilən Müddət"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Expected Duration (minutes)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__production_text_help
msgid "Explanation for batch production"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Files attached to the product."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Filters"
msgstr "Filterlər"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_final_id
msgid "Final Location from procurement"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__done
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Finished"
msgstr "Tamalandı"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__order_finished_lot_id
msgid "Finished Lot/Serial Number"
msgstr "Tamalanmış Lot\\Seriya Nömrəsi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_finished_ids
msgid "Finished Moves"
msgstr "Tamalanmış Köçürmələr "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__finished_move_line_ids
msgid "Finished Product"
msgstr "Tamamlanmış Məhsul"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_finished_product
msgid "Finished Product Label (PDF)"
msgstr "Tamamlanmış Məhsul Etiketi (PDF) "

#. module: mrp
#: model:ir.actions.report,name:mrp.label_manufacture_template
msgid "Finished Product Label (ZPL)"
msgstr "Tamamlanmış Məhsul Etiketi (PDF) "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_finished_ids
msgid "Finished Products"
msgstr "Tamamlanmış Məhsullar "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_dest_id
msgid "Finished Products Location"
msgstr "Tamamlanmış Məhsulların Yerləşdiyi Yer"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lot_name
msgid "First Lot/SN"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__consumption
msgid "Flexible Consumption"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.js:0
msgid "Fold"
msgstr "Qat"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_follower_ids
msgid "Followers"
msgstr "İzləyicilər"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_partner_ids
msgid "Followers (Partners)"
msgstr "İzləyicilər (Tərəfdaşlar)"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_type_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Gözəl şriftli ikon, məsələn fa-tapşırıqlar"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Force"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Forecast"
msgstr "Proqnoz"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Forecast Report"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Forecasted"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__forecasted_issue
msgid "Forecasted Issue"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Free to Use"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Free to Use / On Hand"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Free to use / On Hand"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "From"
msgstr "Başlama Tarixi"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Fully Productive"
msgstr "Tam Məhsuldar"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason7
msgid "Fully Productive Time"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Future Activities"
msgstr "Gələcək Fəaliyyətlər"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "General Information"
msgstr "Ümumi Məlumat"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Generate"
msgstr "Yaratmaq "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Generate Serial Numbers"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Generate a new BoM from this Manufacturing Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__generated_mrp_lot_label_to_print
msgid "Generated Lot/SN Label to Print"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
msgid "Get statistics about the work orders duration related to this routing."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__sequence
msgid ""
"Gives the sequence order when displaying a list of routing Work Centers."
msgstr ""
"Marşrutlaşdırma İş Mərkəzlərinin siyahısını göstərərkən, ardıcılıq qaydasını"
" verir. "

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__sequence
msgid "Gives the sequence order when displaying a list of work centers."
msgstr "İş Mərkəzlərinin  siyahısını göstərərkən, ardıcılıq qaydasını verir. "

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__sequence
msgid "Gives the sequence order when displaying."
msgstr "Göstərərkən, ardıcılıq qaydasını verir. "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__google_slide
msgid "Google Slide"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Google Slide Link"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Group By"
msgstr "Görə Qrupla"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Group By..."
msgstr "Aşağıdakılara görə Qrupla..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Group by..."
msgstr "Qruplaşdır..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Hardware"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_produced
msgid "Has Been Produced"
msgstr "İstehsal edilib"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking__has_kits
msgid "Has Kits"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_production__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__has_message
msgid "Has Message"
msgstr "Mesajı Var"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__has_routing_lines
msgid "Has Routing Lines"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__has_worksheet
msgid "Has Worksheet"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__product_document__attached_on_mrp__hidden
msgid "Hidden"
msgstr "Gizli"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__manual_consumption
msgid "Highlight Consumption"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__costs_hour
msgid "Hourly processing cost."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Hours"
msgstr "Saatlar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__id
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__id
msgid "ID"
msgstr "ID"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_exception_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon"
msgstr "Simvol"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_exception_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "İstisna fəaliyyəti göstərən simvol."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_id
msgid ""
"If a product variant is defined the BOM is available only for this product."
msgstr ""
"Məhsul variantı müəyyən edilibsə, Məhsul resepti yalnız bir məhsul üzrə "
"mövcud olur."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_needaction
msgid "If checked, new messages require your attention."
msgstr "İşarələnibsə, yeni mesajlara baxmalısınız."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "İşarələnibsə, bəzi mesajların çatdırılmasında xəta var."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__propagate_cancel
msgid ""
"If checked, when the previous move of the move (which was generated by a "
"next procurement) is cancelled or split, the move generated by this move "
"will too"
msgstr ""
"İşarə qoyulubsa, hərəkətin əvvəlki hərəkəti (növbəti satınalma ilə "
"yaradılmış olan) ləğv edildikdə və ya bölündükdə, bu hərəkətlə yaradılan "
"hərəkətlə də eynisi baş verəcək"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Aktiv sahə Yanlış olaraq təyin edilibsə, sizə ehtiyat qeydini silmədən "
"gizlətməyə icazə veriləcək."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_mrp_reception_report_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the allocation "
"report labels of a MO when it is done."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_mrp_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically print the allocation "
"report of a MO when it is done and has assigned moves."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_done_mrp_lot
msgid ""
"If this checkbox is ticked, Odoo will automatically print the lot/SN label "
"of a MO when it is done."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_done_mrp_product_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the product labels"
" of a MO when it is done."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_done_production_order
msgid ""
"If this checkbox is ticked, Odoo will automatically print the production "
"order of a MO when it is done."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Impacted Transfer(s):"
msgstr "Təsir görmüş Köçürmə (Köçürmələrlər)"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "Import Template for Bills of Materials"
msgstr "Materialların Reseptləri üzrə Şablonu İdxal Et"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"Impossible to plan the workorder. Please check the workcenter "
"availabilities."
msgstr ""
"İş sifarişini planlaşdırmaq mümkün deyil. Xahiş edirik, iş mərkəzi "
"imkanlarını yoxlayın."

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
msgid "Impossible to plan. Please check the workcenter availabilities."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__progress
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__progress
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "In Progress"
msgstr "Həyata Keçirilməkdə"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "In Transit"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_view_search_catalog
msgid "In the BoM"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_view_search_catalog
msgid "In the MO"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_deadline
msgid ""
"Informative date allowing to define when the manufacturing order should be "
"processed at the latest to fulfill delivery on time."
msgstr ""
"Çatdırılmanın vaxtında həyata keçirilməsi üçün, istehsal sifarişinin ən son "
"nə zaman işlənməli olduğunu müəyyən etmək imkanı verən məlumatvermə "
"xarakterli tarix."

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_moves
msgid "Inventory Moves"
msgstr "İNventar Hərəkətləri"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__move_line_ids
msgid ""
"Inventory moves for which you must scan a lot number at this work order"
msgstr ""
"Bu iş sifarişində partiya nömrəsini skan etməli olduğunuz inventar "
"hərəkətləri"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_delayed
msgid "Is Delayed"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_is_follower
msgid "Is Follower"
msgstr "İzləyicidir"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__is_kits
#: model:ir.model.fields,field_description:mrp.field_product_template__is_kits
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__product_is_kit
msgid "Is Kits"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_locked
msgid "Is Locked"
msgstr "Blok Edilib"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__manual
msgid "Is a Blocking Reason"
msgstr "Bloketmə Səbəbidir"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_user_working
msgid "Is the Current User Working"
msgstr "Cari İstifadəçinin İşləməsidir"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "It has already been unblocked."
msgstr "O, artıq blokdan çıxarılıb"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"It is not possible to unplan one single Work Order. You should unplan the "
"Manufacturing Order instead in order to unplan all the linked operations."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_planned
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_planned
msgid "Its Operations are Planned"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__json_popover
msgid "JSON data for the popover widget"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "John Doe"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "Kanban Nəzarət Paneli Qrafiki"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__description_bom_line
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__description_bom_line
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__bom_id
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__phantom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Kit"
msgstr "Dəst"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Laptop"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Laptop Model X"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Laptop model X"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Laptop with 16GB RAM"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_uid
msgid "Last Updated by"
msgstr "Son Yeniləyən"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_date
msgid "Last Updated on"
msgstr "Son Yenilənmə tarixi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__last_working_user_id
msgid "Last user that worked on this work order."
msgstr "Bu iş sifarişi üzrə işləmiş son istifadəçi"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__late
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Late"
msgstr "Son"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Activities"
msgstr "Ən son Əməliyyatlar"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Availability"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late MO or Late delivery of components"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__components_availability
msgid ""
"Latest component availability status for this MO. If green, then the MO's "
"readiness status is ready, as per BOM configuration."
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_wood_ply_product_template
msgid "Layers that are stick together to assemble wood panels."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Lead Time"
msgstr "Göndərmə vaxtı"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
msgid "Lead Times"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__leave_id
msgid "Leave"
msgstr "Tərk edin"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_product_document__attached_on_mrp
msgid ""
"Leave hidden if document only accessible on product form.\n"
"Select Bill of Materials to visualise this document as a product attachment when this product is in a bill of material."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning_line
msgid "Line of issue consumption"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__location_id
msgid "Location"
msgstr "Məkan"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_id
msgid "Location where the product you want to unbuild is."
msgstr "Sökmək istədiyiniz məhsulun olduğu məkan."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_src_id
msgid "Location where the system will look for components."
msgstr "Sistemin komponentləri axtaracağı məkan."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "Sistemin son məhsulları saxlayacağı məkan."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_dest_id
msgid ""
"Location where you want to send the components resulting from the unbuild "
"order."
msgstr ""
"Sökmə sifarişindən irəli gələn komponentləri göndərmək istədiyiniz məkan."

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_lock_unlock
msgid "Lock/Unlock"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_id
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Loss Reason"
msgstr "İtki Səbəbi"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid "Lot %s does not exist."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lots_quantity_separator
msgid "Lot quantity separator"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lots_separator
msgid "Lot separator"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Lot/SN Label"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__done_mrp_lot_label_to_print
msgid "Lot/SN Label to Print"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Lot/SN Labels"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_lot
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Lot/Serial"
msgstr "Partiya/ Seriya"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__lot_producing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__lot_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__finished_lot_id
msgid "Lot/Serial Number"
msgstr "Partiya/ Seriya nömrəsi"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Lot/Serial Numbers"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_traceability
msgid "Lots/Serial Numbers"
msgstr "Partiyalar/ Seriya nömrələri"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_backorder_id
msgid "MO Backorder"
msgstr ""

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_cancelled
#: model:mail.message.subtype,name:mrp.mrp_mo_in_cancelled
msgid "MO Cancelled"
msgstr ""

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_confirmed
#: model:mail.message.subtype,name:mrp.mrp_mo_in_confirmed
msgid "MO Confirmed"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "MO Cost"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "MO Costs"
msgstr ""

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_done
#: model:mail.message.subtype,name:mrp.mrp_mo_in_done
msgid "MO Done"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "MO Generated by %s"
msgstr ""

#. module: mrp
#: model:ir.actions.client,name:mrp.action_report_mo_overview
#: model:ir.actions.report,name:mrp.action_report_mrp_mo_overview
msgid "MO Overview"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "MO Pending"
msgstr ""

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_progress
#: model:mail.message.subtype,name:mrp.mrp_mo_in_progress
msgid "MO Progress"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reservation_state
msgid "MO Readiness"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "MO Ready"
msgstr ""

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_to_close
#: model:mail.message.subtype,name:mrp.mrp_mo_in_to_close
msgid "MO To Close"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_document__attached_on_mrp
msgid "MRP : Visible at"
msgstr ""

#. module: mrp
#: model:ir.actions.client,name:mrp.mrp_reception_action
msgid "MRP Reception Report"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_routings
msgid "MRP Work Orders"
msgstr "MTP üzrə İş Sifarişləri"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss_type
msgid "MRP Workorder productivity losses"
msgstr "MTP üzrə İş Sifarişlərində məhsuldarlıq itkiləri"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "MRP-001"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_routings
msgid "Manage Work Order Operations"
msgstr "İş Sifarişləri ilə bağlı Əməliyyatları İdarə et"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__manual_consumption
msgid "Manual Consumption"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid "Manual Duration"
msgstr "Əllə Müddət"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manuf. Lead Time"
msgstr ""

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_steps
#: model:ir.model.fields.selection,name:mrp.selection__stock_rule__action__manufacture
#: model:stock.route,name:mrp.route_warehouse0_manufacture
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_stock_rule
msgid "Manufacture"
msgstr "İstehsal"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__mrp_one_step
msgid "Manufacture (1 step)"
msgstr "İstehsal (1 mərhələ)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_mto_pull_id
msgid "Manufacture MTO Rule"
msgstr "İstehsal MTO Qaydası"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_pull_id
msgid "Manufacture Rule"
msgstr "İstehsal Qaydası"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "Manufacture Security Lead Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__normal
msgid "Manufacture this product"
msgstr "Bu məhsulu istehsal et"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_to_resupply
msgid "Manufacture to Resupply"
msgstr "Ehtiyatların artırılması üçün istehsal et"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__mrp_product_qty
#: model:ir.model.fields,field_description:mrp.field_product_template__mrp_product_qty
msgid "Manufactured"
msgstr "İstehsal olunub"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "Manufactured Products"
msgstr "İstehsal Olunmuş Məhsullar"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Manufactured in the last 365 days"
msgstr "Son 365 gündə istehsal olunub"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.actions.client,name:mrp.action_mrp_display_fullscreen
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__code__mrp_operation
#: model:ir.ui.menu,name:mrp.menu_mrp_root
#: model_terms:ir.ui.view,arch_db:mrp.product_document_form
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Manufacturing"
msgstr "İstehsal"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_buttons.xml:0
msgid "Manufacturing Forecast"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__produce_delay
#: model:ir.model.fields,field_description:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__manufacturing_lead
msgid "Manufacturing Lead Time"
msgstr "İstehsalın Həyata Keçirildiyi Müddət"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manu_type_id
msgid "Manufacturing Operation Type"
msgstr "İstehsal Əməliyyatının Növü"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__production_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Manufacturing Order"
msgstr "istehsal sifarişi"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_product_mrp_production_workcenter
#: model:ir.actions.act_window,name:mrp.action_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_production_action
#: model:ir.actions.act_window,name:mrp.mrp_production_action_picking_deshboard
#: model:ir.ui.menu,name:mrp.menu_mrp_production_action
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_view_activity
#: model_terms:ir.ui.view,arch_db:mrp.view_production_calendar
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_production_pivot
msgid "Manufacturing Orders"
msgstr "İstehsal Sifarişləri"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are in confirmed state."
msgstr "Təsdiq edilmiş vəziyyətdə olan İstehsal Sifarişləri"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__ready_to_produce
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manufacturing Readiness"
msgstr "Məhsulun İstehsal üçün Hazır Olması"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Manufacturing Reference"
msgstr "İstehsal İstinadları"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__manufacturing_visibility_days
msgid "Manufacturing Visibility Days"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc.\n"
"                They can be defined via the configuration menu."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reservation_state
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_availability
msgid ""
"Manufacturing readiness for this MO, as per bill of material configuration:\n"
"            * Ready: The material is available to start the production.\n"
"            * Waiting: The material is not available to start the production.\n"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_picking_tree_mrp_operation
#: model:ir.actions.act_window,name:mrp.action_picking_tree_mrp_operation_graph
#: model:ir.ui.menu,name:mrp.mrp_operation_picking
msgid "Manufacturings"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Marc Demo"
msgstr ""

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_mark_done
msgid "Mark as Done"
msgstr "Hazır kimi işarələ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Mass Produce"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_mps
msgid "Master Production Schedule"
msgstr "Əsas İstehsal Planı"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason0
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Material Availability"
msgstr "Materialın Mövcudluğu"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_merge
msgid "Merge"
msgstr "Birləşdirin"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_has_error
msgid "Message Delivery error"
msgstr "Mesajın Çatdırılmasında xəta"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Minimal İnventarlaşdırma Qaydası"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Minutes"
msgstr "Dəqiqələr"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Miscellaneous"
msgstr "Müxtəlif"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_byproduct_ids
msgid "Move Byproduct"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr "İzləmə üzrə Hərəkətlər"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_consumption_warning_line_ids
msgid "Mrp Consumption Warning Line"
msgstr ""

#. module: mrp
#: model:ir.actions.client,name:mrp.action_mrp_display
msgid "Mrp Display"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_procurement_group__mrp_production_ids
msgid "Mrp Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_count
msgid "Mrp Production Count"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__my_activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__my_activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mənim Fəaliyyətlərimin Son Tarixi "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "My MOs"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__never_product_template_attribute_value_ids
msgid "Never attribute values"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/stock_rule.py:0
msgid "New"
msgstr "Yeni"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "New BoM from %(mo_name)s"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Next Activity"
msgstr "Növbəti fəaliyyət"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_calendar_event_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_calendar_event_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Növbəti Fəaliyyət Təqvimi Tədbiri"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Növbəti Fəaliyyətin Son Tarixi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_summary
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_summary
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_summary
msgid "Next Activity Summary"
msgstr "Növbəti Fəaliyyət Xülasəsi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_id
msgid "Next Activity Type"
msgstr "Yeni Fəaliyyət Növü"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "No Backorder"
msgstr "Təkrarlanmış Sifariş Yoxdur"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid "No bill of materials found. Let's create one!"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "No data available."
msgstr "Heç bir məlumat mövcud deyil."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workcenter_load_report_graph
msgid "No data yet!"
msgstr "Hələ məlumat yoxdur!"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "No manufacturing order found. Let's create one."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid "No product found. Let's create one!"
msgstr "Məhsul tapılmadı. Birini yaradaq."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_blocked
msgid "No productivity loss for this equipment"
msgstr "Bu avadanlıq üzrə məhsuldarlıq itkisi yoxdur"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid "No unbuild order found"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid "No work orders to do!"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid ""
"No workorder currently in progress. Click to mark work center as blocked."
msgstr ""
"Hal-hazırda icra olunan iş sifarişi yoxdur. İş mərkəzini bloklanmış kimi "
"işarələmək üçün klikləyin."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__normal
msgid "Normal"
msgstr "Normal"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__unavailable
msgid "Not Available"
msgstr "Mövcud deyil"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Not Ready"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Note that another version of this BOM is available."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid ""
"Note that archived work center(s): '%s' is/are still linked to active Bill "
"of Materials, which means that operations can still be planned on it/them. "
"To prevent this, deletion of the work center is recommended instead."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/product.py:0
msgid ""
"Note that product(s): '%s' is/are still linked to active Bill of Materials, "
"which means that the product can still be used on it/them."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_needaction_counter
msgid "Number of Actions"
msgstr "Hərəkətlərin sayı"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_in_progress
msgid "Number of Manufacturing Orders In Progress"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_late
msgid "Number of Manufacturing Orders Late"
msgstr "Gecikən İstehsalat Sifarişlərinin Sayı"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_to_close
msgid "Number of Manufacturing Orders To Close"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_waiting
msgid "Number of Manufacturing Orders Waiting"
msgstr "Növbədə olan İstehsalat Sifarişlərinin Sayı"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_todo
msgid "Number of Manufacturing Orders to Process"
msgstr "Həyata Keçiriləcək İstehsalat Sifarişlərinin Sayı"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lot_qty
msgid "Number of SN"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unbuild_count
msgid "Number of Unbuilds"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_has_error_counter
msgid "Number of errors"
msgstr "Xətaların sayı"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_child_count
msgid "Number of generated MO"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Əməliyyat tələb edən mesajların sayı"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Çatdırılma xətası olan mesajların sayı"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__capacity
msgid "Number of pieces that can be produced in parallel for this product."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_source_count
msgid "Number of source MO"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "OEE"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee_target
msgid "OEE Target"
msgstr "Aşağıdakı qaydaların OEE Hədəfi"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid ""
"Odoo opens a PDF preview by default. If you want to print instantly,\n"
"                                install the IoT App on a computer that is on the same local network as the\n"
"                                barcode operator and configure the routing of the reports."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee
msgid "Oee"
msgstr "OEE"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "On Hand"
msgstr "İstifadə Haqqında "

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"Only manufacturing orders in either a draft or confirmed state can be %s."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Only manufacturing orders with a Bill of Materials can be %s."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Open"
msgstr "Açın"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view_mo_form
msgid "Open Work Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Operation"
msgstr "Əməliyyat "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__allow_operation_dependencies
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__allow_operation_dependencies
msgid "Operation Dependencies"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__operation_id
msgid "Operation To Consume"
msgstr "İstehlak üçün Əməliyyat "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__picking_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_type_id
msgid "Operation Type"
msgstr "Əməliyyat növü"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid ""
"Operation define that need to be done to realize a Work Order.\n"
"                Each operation is done at a specific Work Center and has a specific duration."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/product.py:0
msgid "Operation not supported"
msgstr "Əməliyyat dəstəklənmir"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_operations_block/mrp_mo_overview_operations_block.xml:0
#: model:ir.actions.act_window,name:mrp.mrp_routing_action
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__allowed_operation_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_manufacturing
#: model:ir.ui.menu,name:mrp.menu_mrp_routing_action
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_operations
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_calendar
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Operations"
msgstr "Əməliyyatlar"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Done"
msgstr "Edilmiş Əməliyyatlar"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Planned"
msgstr "Planlaşdırılmış Əməliyyatlar"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Operations Search Filters"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__needed_by_operation_ids
msgid "Operations that cannot start before this operation is completed."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__blocked_by_operation_ids
msgid "Operations that need to be completed before this operation can start."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__orderpoint_id
msgid "Orderpoint"
msgstr "Sifariş nöqtəsi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__order_ids
msgid "Orders"
msgstr "Sifarişlər"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_production
msgid "Original Production Quantity"
msgstr "Orijinal İstehsal Miqdarı"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_outdated_bom
msgid "Outdated BoM"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee_target
msgid "Overall Effective Efficiency Target in percentage"
msgstr "Faizlə Ümumi Effektiv Səmərərlilik Hədəfi"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_oee
#: model:ir.ui.menu,name:mrp.menu_mrp_workcenter_productivity_report
msgid "Overall Equipment Effectiveness"
msgstr "Ümumi Avadanlıq Effektivliyi "

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee
msgid "Overall Equipment Effectiveness, based on the last month"
msgstr "Son aya əsaslanan Ümumi Avadanlıq Effektivliyi "

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_oee
msgid "Overall Equipment Effectiveness: no working or blocked time"
msgstr "Ümumi Avadanlıq Effektivliyi: işləyən və ya bloklanmış vaxt yoxdur"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__pdf
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__done_mrp_lot_label_to_print__pdf
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__generated_mrp_lot_label_to_print__pdf
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__mrp_product_label_to_print__pdf
msgid "PDF"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Package barcode"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_id
msgid "Parent BoM"
msgstr "Əsas Məhsul Resepti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__parent_product_tmpl_id
msgid "Parent Product Template"
msgstr "Əsas Məhsul Şablonu "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_consumption_warning_id
msgid "Parent Wizard"
msgstr "Əsas Sehrbaz"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields,help:mrp.field_mrp_workorder__worksheet_google_slide
msgid ""
"Paste the url of your Google Slide. Make sure the access to the document is "
"public."
msgstr ""
"Google Slaydınızın URL-sini yapışdırın. Sənədə girişin açıq olduğundan əmin "
"olun."

#. module: mrp
#: model:ir.actions.server,name:mrp.action_pause_workorders
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Pause"
msgstr "Dayandır"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Pending"
msgstr "Gözləmədədir"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__performance
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__performance
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Performance"
msgstr "Məhsuldarlıq"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Performance Losses"
msgstr "Məhsuldarlığın Azalması"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__performance
msgid "Performance over the last month"
msgstr "Son bir ay ərzində məhsuldarlıq"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick Components"
msgstr "Komponentləri seç"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick components and then manufacture"
msgstr "Komponenetləri seçin və sonra istehsal edin"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm
msgid "Pick components then manufacture (2 steps)"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick components, manufacture and then store products (3 steps)"
msgstr ""
"Komponenetləri seçin və istehsalra edin və məhsulları yerləşdirin ( "
"3mərhələ) "

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm_sam
msgid "Pick components, manufacture, then store products (3 steps)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_mto_pull_id
msgid "Picking Before Manufacturing MTO Rule"
msgstr "İstehsalın MTO Qaydasından əvvəl Seçim"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_type_id
msgid "Picking Before Manufacturing Operation Type"
msgstr "İstehsal Əməliyyat Növündən Əvvəl Seçim"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_route_id
msgid "Picking Before Manufacturing Route"
msgstr "İstehsal Marşrutundan Əvvəl Seçim"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking_type
msgid "Picking Type"
msgstr "Seçim Növü "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_ids
msgid "Picking associated to this manufacturing order"
msgstr "Bu istehsal əmri ilə əlaqəli seçim"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_loc_id
msgid "Picking before Manufacturing Location"
msgstr "İstehsal Sahəsindən əvvəl Seçim"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Pieces"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Plan"
msgstr "Plan"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Plan Orders"
msgstr "Sifarişləri Planlaşdırın"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_plan_with_components_availability
msgid "Plan based on Components Availability"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Plan manufacturing or purchase orders based on forecasts"
msgstr ""
"Proqnozlara əsasən istehsal və ya satınalma sifarişlərini planlaşdırın"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planned"
msgstr "Planlaşdırılmış"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Planned at the same time as other workorder(s) at %s"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.mrp_planning_menu_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Planning"
msgstr "Planlaşdırma "

#. module: mrp
#: model:product.template,name:mrp.product_product_plastic_laminate_product_template
msgid "Plastic Laminate"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_move.py:0
msgid "Please enter a positive quantity."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Please set the first Serial Number or a default sequence"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid "Please specify the first serial number you would like to use."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid "Please specify the serial number you would like to use."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Please unblock the work center to start the work order."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Please unblock the work center to validate the work order"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_ply_product_template
msgid "Ply Layer"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_ply_veneer_product_template
msgid "Ply Veneer"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__json_popover
msgid "Popover Data JSON"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__possible_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__possible_bom_product_template_attribute_value_ids
msgid "Possible Product Template Attribute Value"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Post-Production"
msgstr "İstehsal Sonrası"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pre-Production"
msgstr "İstehsaldan öncə"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Prepare MO"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Print"
msgstr "Çap edin"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
msgid "Print All Variants"
msgstr ""

#. module: mrp
#: model:ir.actions.server,name:mrp.action_print_labels
msgid "Print Labels"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.xml:0
msgid "Print Work Order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Print labels as:"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Print when \"Create new Lot/SN\""
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Print when done"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__priority
msgid "Priority"
msgstr "Prioritet"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason5
msgid "Process Defect"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Process manufacturing orders from the barcode application"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Process operations at specific work centers"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__produce_line_ids
msgid "Processed Disassembly Lines"
msgstr "İşlənən Sökmə Sətirləri"

#. module: mrp
#: model:ir.model,name:mrp.model_procurement_group
#: model:ir.model.fields,field_description:mrp.field_mrp_production__procurement_group_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Procurement Group"
msgstr "Satınalma qrupu"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_procurement_compute_mrp
msgid "Procurement: run scheduler"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Produce"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Produce All"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_batch_produce
msgid "Produce a batch of production order"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_byproducts
msgid "Produce residual products"
msgstr "Qalıq məhsullar istehsal edin"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Produce residual products (A + B -> C + D)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "Produced"
msgstr "İsteahsal olunan"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__operation_id
msgid "Produced in Operation"
msgstr "Əməliyyatda İstehsal olunan"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model,name:mrp.model_product_template
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Product"
msgstr "Məhsul"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Product Attachments"
msgstr "Məhsul Əlavələri "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__capacity_ids
msgid "Product Capacities"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__product_catalog_product_is_in_bom
msgid "Product Catalog Product Is In Bom"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__product_catalog_product_is_in_mo
msgid "Product Catalog Product Is In Mo"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Product Cost"
msgstr "Məhsul Qiyməti"

#. module: mrp
#: model:ir.model,name:mrp.model_product_document
msgid "Product Document"
msgstr "Məhsul Sənədi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_virtual_available
msgid "Product Forecasted Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__mrp_product_label_to_print
msgid "Product Label to Print"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Product Labels"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_plm
msgid "Product Lifecycle Management (PLM)"
msgstr "Məhsulun Xidmət müddətinin İdarəedilməsi (PLM)"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Məhsul Hərəkətləri (Ehtiyat Keçid Xətti)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_qty_available
msgid "Product On Hand Quantity"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Product Quantity"
msgstr "Məhsulun Miqdarı"

#. module: mrp
#: model:ir.model,name:mrp.model_product_replenish
msgid "Product Replenish"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__product_template
msgid "Product Template"
msgstr "Məhsul Şablonu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_uom_id
msgid "Product Unit of Measure"
msgstr "Məhsulun Ölçü Vahidi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__product_uom_id
msgid "Product UoM"
msgstr "Məhsul UoM"

#. module: mrp
#: model:ir.model,name:mrp.model_product_product
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_id
msgid "Product Variant"
msgstr "Məhsul Çeşidi"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_product_variant_action
#: model:ir.ui.menu,name:mrp.product_variant_mrp
msgid "Product Variants"
msgstr "Məhsul Çeşidləri"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_capacity_unique_product
msgid "Product capacity should be unique for each workcenter."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Product to build..."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__production_id
#: model:ir.model.fields,field_description:mrp.field_picking_label_type__production_ids
#: model:ir.model.fields,field_description:mrp.field_stock_picking__production_ids
msgid "Production"
msgstr "Məhsul"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_capacity
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_capacity
msgid "Production Capacity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_date
msgid "Production Date"
msgstr "İstehsal Tarixi"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Production Information"
msgstr "İstehsal Məlumatı"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_location_id
msgid "Production Location"
msgstr "İstehsal Yeri"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_production_order
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__production_id
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Production Order"
msgstr "İstehsal Sifarişi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__raw_material_production_id
msgid "Production Order for components"
msgstr "Komponentlər üçün İstehsal Sifarişi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__production_id
msgid "Production Order for finished products"
msgstr "Tamamlanmış Məhsullar üçün İstehsal Sifarişi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_state
msgid "Production State"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Production Workcenter"
msgstr "İstehsal İş Mərkəzi "

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
msgid "Production of Draft MO"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Production started late"
msgstr "İstehsal gec balşadı"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__production_ids
msgid "Productions To Split"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__productive
msgid "Productive"
msgstr "Məhsuldar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__productive_time
msgid "Productive Time"
msgstr "Məhsuldar Vaxt "

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__productive_time
msgid "Productive hours over the last month"
msgstr "Son bir ay ərzində məhsuldar saatlar "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Productivity"
msgstr "Məhsuldarlıq "

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_blocked
msgid "Productivity Losses"
msgstr "Məhsuldarlıq Zərərləri"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_template_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom
#: model:ir.ui.menu,name:mrp.menu_mrp_product_form
msgid "Products"
msgstr "Məhsullar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__progress
msgid "Progress Done (%)"
msgstr "İrəliləyiş vaxtı  tamamlandı (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__propagate_cancel
msgid "Propagate cancel and split"
msgstr "Ləğvetməni və bölməni çoxalt"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__quality
msgid "Quality"
msgstr "Keyfiyyət"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Quality Losses"
msgstr "Keyfiyyətin Azalması"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quant_ids
msgid "Quant"
msgstr "Miqdar"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_produced
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quantity
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Quantity"
msgstr "Miqdar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_produced
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view_mo_form
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Quantity Produced"
msgstr "İstehsal edilmiş Miqdar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_producing
msgid "Quantity Producing"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Quantity Remaining"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_remaining
msgid "Quantity To Be Produced"
msgstr "İstehsal Olunacaq Miqdar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__should_consume_qty
msgid "Quantity To Consume"
msgstr "İstehlak Olunacaq Miqdar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__quantity
msgid "Quantity To Produce"
msgstr "İstehsalda Miqdar"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_capacity
#: model:ir.model.fields,help:mrp.field_mrp_production_split__production_capacity
msgid "Quantity that can be produced with the current stock of components"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_quant
msgid "Quants"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "RAM"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__rating_ids
msgid "Ratings"
msgstr "Qiymətləndirmələr"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_raw_ids
msgid "Raw Moves"
msgstr "İşlənməmiş yerdəyişmələr"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__assigned
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__ready
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Ready"
msgstr "Hazır"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Ready to Produce"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Real Cost"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Real Costs"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__duration
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration
msgid "Real Duration"
msgstr "Real Müddət"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Receipt"
msgstr "Qəbz"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Receipts"
msgstr "Qəbzlər"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Reception time estimation."
msgstr ""

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason4
msgid "Reduced Speed"
msgstr ""

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason6
msgid "Reduced Yield"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__code
#: model:ir.model.fields,field_description:mrp.field_mrp_production__name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__name
msgid "Reference"
msgstr "Rəy"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_name_uniq
msgid "Reference must be unique per Company!"
msgstr "İstinad hər bir şirkət üçün unikal olmalıdır!"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__origin
msgid ""
"Reference of the document that generated this production order request."
msgstr "Bu istehsal sifarişi sorğusunu yaradan sənədə istinad."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Reference:"
msgstr "İstinad:"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_remaining_days_unformatted_field.js:0
msgid "Remaining Days"
msgstr "Qalan Günlər"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_workorder_popover.xml:0
msgid "Replan"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.xml:0
msgid "Replenish"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Replenish on Order (MTO)"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Replenishments"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_reporting
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Reporting"
msgstr "Hesabatlıq"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Reserved"
msgstr "Rezerv edilib"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_id
msgid "Resource"
msgstr "Resurs"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__user_id
msgid "Responsible"
msgstr "Məsul"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_user_id
msgid "Responsible User"
msgstr "Məsul İstifadəçi"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Resupply lead time."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Route"
msgstr "Marşrut"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Routing"
msgstr "Marşrutlaşdırma"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__routing_line_ids
msgid "Routing Lines"
msgstr "Marşrutlaşdırma xətləri"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
msgid "Routing Work Centers"
msgstr "Marşrutlaşdırma İş Mərkəzləri"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-in Çatdırılmasında xəta"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__date
msgid "Schedule Date"
msgstr "Tarixi planlaşdırın"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Schedule manufacturing orders earlier to avoid delays"
msgstr ""
"Gecikmələrin qarşısını almaq üçün istehsal sifarişlərini əvvəldən "
"planlaşdırın"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Scheduled Date"
msgstr "Planlaşdırılmış Tarix"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Scheduled End"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"Scheduled before the previous work order, planned from %(start)s to %(end)s"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_workorder_popover.xml:0
msgid "Scheduling Information"
msgstr ""

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_scrap
#: model:ir.model,name:mrp.model_stock_scrap
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_scrap
msgid "Scrap"
msgstr "İstifadəyə yararsız"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_count
msgid "Scrap Move"
msgstr "İstifadəyə Yararsız Məhsulun Daşınması"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Scrap Products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_ids
msgid "Scraps"
msgstr "İstifadəyə Yararsız Məhsullar"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_screw_product_template
msgid "Screw"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Search"
msgstr "Axtarın"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Search Bill Of Material"
msgstr "Məhsul Reseptinin Axtarışı"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Search Production"
msgstr "İstehsal Axtarışı"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Search Work Orders"
msgstr "İş Sifarişlərinin Axtarışı"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Search for mrp workcenter"
msgstr "MTP iş mərkəzini axtarın"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Security Lead Time"
msgstr "Tədarük Müddətinin Təhlükəsizliyi"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,help:mrp.field_res_config_settings__manufacturing_lead
msgid "Security days for each manufacturing operation."
msgstr "Hər istehsal əməliyyatı üçün təhlükəsizlik günləri."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
msgid "Select Operations to Copy"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Selection not supported."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__sequence
msgid "Sequence"
msgstr "Ardıcıllıq"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Set Quantities & Validate"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Set Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__manual
msgid "Set duration manually"
msgstr "Müddəti əl ilə təyin edin"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Set the order that work orders should be processed in. Activate the feature "
"within each BoM's Miscellaneous tab"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_configuration
#: model:ir.ui.menu,name:mrp.menu_mrp_config
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Settings"
msgstr "Parametrlər"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_start
msgid "Setup Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__time_start
msgid "Setup Time (minutes)"
msgstr ""

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason2
msgid "Setup and Adjustments"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_allocation
msgid "Show Allocation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__show_bom
msgid "Show BoM column"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_replenish__show_bom
#: model:ir.model.fields,field_description:mrp.field_stock_replenish_mixin__show_bom
msgid "Show Bom"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_final_lots
msgid "Show Final Lots"
msgstr "Yekun Partiyaları Göstərin"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lock
msgid "Show Lock/unlock buttons"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__show_json_popover
msgid "Show Popover?"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_produce
msgid "Show Produce"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_produce_all
msgid "Show Produce All"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Show all records which has next action date is before today"
msgstr "Növbəti fəaliyyət tarixi bu günə qədər olan bütün qeydləri göstərin"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__show_backorder_lines
msgid "Show backorder lines"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__leave_id
msgid "Slot into workcenter calendar once planned"
msgstr "Planlaşdırdıqdan sonra iş mərkəzindəki təqviminə yerləşdirin"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_head_product_template
msgid "Solid wood is a durable natural material."
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_product_template
msgid "Solid wood table."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid ""
"Some of your byproducts are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct byproducts."
msgstr ""
"Əlavə məhsullarınızdan bəziləri izlənilir, doğru əlavə məhsulları əldə etmək"
" üçün istehsal sifarişini təyin etməlisiniz."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid ""
"Some of your components are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct components."
msgstr ""
"Komponentlərinizdən bəziləri izlənilir, doğru komponentləri əldə etmək üçün "
"istehsal sifarişini təyin etməlisiniz."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"Some work orders are already done, so you cannot unplan this manufacturing order.\n"
"\n"
"It’d be a shame to waste all that progress, right?"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"Some work orders have already started, so you cannot unplan this manufacturing order.\n"
"\n"
"It’d be a shame to waste all that progress, right?"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__origin
msgid "Source"
msgstr "Mənbə"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_id
msgid "Source Location"
msgstr "Mənbə Yeri"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Specific Capacities"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__capacity_ids
msgid ""
"Specific number of pieces that can be produced in parallel per product."
msgstr ""

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_split
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
msgid "Split"
msgstr "Ayırma"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__counter
msgid "Split #"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_detailed_vals_ids
msgid "Split Details"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__mrp_production_split_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Split Production"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split_line
msgid "Split Production Detail"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_split_multi_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Split Productions"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_split
msgid "Split production"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_split_multi
msgid "Split productions"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_screw_product_template
msgid "Stainless steel screw"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_bolt_product_template
msgid "Stainless steel screw full (dia - 5mm, Length - 10mm)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Starred"
msgstr "İşarələnmiş"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_start_workorders
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_start
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Start"
msgstr "Başlayın"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_start
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Start Date"
msgstr "Başlanğıc Tarixi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__state
msgid "State"
msgstr "Dövlət"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__state
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Status"
msgstr "Status"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_state
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Fəaliyyətlərə əsaslanan status\n"
"Gecikmiş: Gözlənilən tarixdən keçib\n"
"Bu gün: Fəaliyyət tarixi bu gündür\n"
"Planlaşdırılıb: Gələcək fəaliyyətlər."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_type_id
msgid "Stock After Manufacturing Operation Type"
msgstr "İstehsal Əməliyyatının Növündən Sonra Stoklama"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_rule_id
msgid "Stock After Manufacturing Rule"
msgstr "İstehsal Qaydalarından sonra Stoklama"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_availability
msgid "Stock Availability"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move
msgid "Stock Move"
msgstr "Stokun Hərəkəti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_dest_ids
msgid "Stock Movements of Produced Goods"
msgstr "İstehsal Olunmuş Malların Stok Hərəkəti"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_unbuild_moves
msgid "Stock Moves"
msgstr "Stok Hərəkətləri"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_rule
msgid "Stock Rule"
msgstr "Stok Qaydaları"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_loc_id
msgid "Stock after Manufacturing Location"
msgstr "İstehsal Məkanından sonra Stoklama"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Stok qaydaları üzrə hesabat"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Store Finished Product"
msgstr "Tamamlanmış Məhsulları Saxlayın"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_bom_id
msgid "Sub BoM"
msgstr "Əlavə Məhsul Resepti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_subcontracting
msgid "Subcontracting"
msgstr "Subpodrat"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_product_template
msgid "Table"
msgstr "Cədvəl"

#. module: mrp
#: model:product.template,name:mrp.product_product_table_kit_product_template
msgid "Table Kit"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_leg_product_template
msgid "Table Leg"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_head_product_template
msgid "Table Top"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_table_kit_product_template
msgid "Table kit"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tag_ids
msgid "Tag"
msgstr "Etiket"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__name
msgid "Tag Name"
msgstr "Etiket Adı"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__has_routing_lines
msgid "Technical field for workcenter views"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_produce_all
msgid "Technical field to check if produce all button can be shown"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_produce
msgid "Technical field to check if produce button can be shown"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reserve_visible
msgid "Technical field to check when we can reserve quantities"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr "Texniki sahədə yoxlamadan imtina edə biləcəyimiz zaman"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__text
msgid "Text"
msgstr "Mətn"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__is_outdated_bom
msgid "The BoM has been updated since creation of the MO"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The Product Unit of Measure you chose has a different category than in the "
"product form."
msgstr ""
"Seçdiyiniz Məhsulun Ölçü Vahidi məhsul formasından fərqli bir kateqoriyaya "
"malikdir."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "The Workorder (%s) cannot be started twice!"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The attribute value %(attribute)s set on product %(product)s does not match "
"the BoM product %(bom_product)s."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "The capacity must be strictly positive."
msgstr "Həcm əhəmiyyətli dərəcədə müsbət mahiyyət kəsb etməlidir."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "The component %s should not be the same as the product to produce."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The current configuration is incorrect because it would create a cycle "
"between these products: %s."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__day_2
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "The day after tomorrow"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_orderpoint.py:0
msgid "The following replenishment order has been generated"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_produced
msgid "The number of products already handled by this work order"
msgstr "Bu iş sifarişi ilə artıq işlənmiş məhsulların sayı"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__operation_id
msgid ""
"The operation where the components are consumed, or the finished products "
"created."
msgstr ""
"Komponentlərin istehlak edildiyi və ya hazır məhsulların yaradıldığı "
"əməliyyat."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__cost_share
msgid ""
"The percentage of the final production cost for this by-product line "
"(divided between the quantity produced).The total of all by-products' cost "
"share must be less than or equal to 100."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__cost_share
msgid ""
"The percentage of the final production cost for this by-product. The total "
"of all by-products' cost share must be smaller or equal to 100."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"The planned end date of the work order cannot be prior to the planned start "
"date, please correct this to save the work order."
msgstr ""
"İş əmrinin planlaşdırılan bitmə tarixi planlaşdırılan başlanğıc tarixindən "
"əvvəl ola bilməz, xahiş edirik, iş əmrini saxlamaq üçün bunu düzəldin."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The product has already been used at least once, editing its structure may "
"lead to undesirable behaviours. You should rather archive the product and "
"create a new one with a new bill of materials."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_reported_from_previous_wo
msgid ""
"The quantity already produced awaiting allocation in the backorders chain."
msgstr ""

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_qty_positive
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_qty_positive
msgid "The quantity to produce must be positive!"
msgstr "İstehsal ediləcək miqdar müsbət olmalıdır!"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_unbuild_qty_positive
msgid "The quantity to unbuild must be positive!"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"The serial number %(number)s used for byproduct %(product_name)s has already"
" been produced"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"The serial number %(number)s used for component %(component)s has already "
"been consumed"
msgstr ""

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_tag_tag_name_unique
msgid "The tag name must be unique."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "The total cost share for a BoM's by-products cannot exceed 100."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"The total cost share for a manufacturing order's by-products cannot exceed "
"100."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
msgid "The total should be equal to the quantity to produce."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "The work order should have already been processed."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"There are no components to consume. Are you still sure you want to continue?"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
msgid "There is no defined calendar on workcenter %s."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid "There's no product move yet"
msgstr "Hələ məhsulun hərəkəti yoxdur"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "This Week"
msgstr "Bu Həftə"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Bu sahə resursların hansı vaxt müddətində fəaliyyət göstərəcəyini müəyyən "
"etmək üçün istifadə olunur."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
msgid "This is a BoM of type Kit!"
msgstr "Bu Dəst tipli Məhsul Reseptidir!"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid ""
"This is the cost based on the BoM of the product. It is computed by summing "
"the costs of the components and operations needed to build the product."
msgstr ""
"Bu, məhsulun Məhsul Reseptinə əsaslanan xərclərdir. Məhsulun qurulması üçün "
"lazım olan komponentlərin və əməliyyatların dəyərinin cəmlənməsi üçün "
"hesablanır."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "This is the cost defined on the product."
msgstr "Bu məhsul üzrə müəyyən edilən dəyərdir."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                You can filter on the product to see all the past movements for the product."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This production has been merge in %s"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This production order has been created from Replenishment Report."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This serial number for product %s has already been produced"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_qty
msgid ""
"This should be the smallest quantity that this product can be produced in. "
"If the BOM contains operations, make sure the work center capacity is "
"accurate."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__time_ids
msgid "Time"
msgstr "Vaxt"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_efficiency
msgid "Time Efficiency"
msgstr "Vaxtın İstifadə Faizi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_ids
msgid "Time Logs"
msgstr "Vaxt Jurnalları"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Time Tracking"
msgstr "Vaxtın İzlənməsi"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Time Tracking: %(user)s"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__time_stop
msgid "Time in minutes for the cleaning."
msgstr "Təmizləmə üçün vaxt, dəqiqələrlə."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__time_start
msgid "Time in minutes for the setup."
msgstr "Tənzimləmə üçün vaxt, dəqiqələrlə."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid ""
"Time in minutes:- In manual mode, time used- In automatic mode, supposed "
"first time when there aren't any work orders yet"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tz
msgid "Timezone"
msgstr "Saat qurşağı"

#. module: mrp
#: model:digest.tip,name:mrp.digest_tip_mrp_0
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid "Tip: Use tablets in the shop to control manufacturing"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "To"
msgstr "Bitmə Tarixi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__to_backorder
msgid "To Backorder"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__to_close
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Close"
msgstr "Bağlamaq üçün"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_expected_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "To Consume"
msgstr "İstehlak etmək üçün "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Do"
msgstr "Görülməli iş"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "To Launch"
msgstr "İşə Salmaq Üçün"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "To Manufacture"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "To Order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "To Produce"
msgstr "İstehsal Etmək Üçün"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__today
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today"
msgstr "Bu gün"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today Activities"
msgstr "Bugünkü Fəaliyyətlər"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__day_1
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Tomorrow"
msgstr "Sabah"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_wood_wear_product_template
msgid "Top layer of a wood panel."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Total Cost of Components"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Total Cost of Operations"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Total Cost of Production"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Total Duration"
msgstr "Ümumi Davametmə Müddəti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_late_count
msgid "Total Late Orders"
msgstr "Cəmi Gecikdirilmiş Sifarişlər"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_pending_count
msgid "Total Pending Orders"
msgstr "Cəmi Gözləyən Sifarişlər"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total Qty"
msgstr "Ümumi Miqdar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_qty
msgid "Total Quantity"
msgstr "Ümumi Miqdar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_progress_count
msgid "Total Running Orders"
msgstr "Cari Sifarişlərin Ümumi Sayı"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Total To Consume"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total expected duration"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__duration_expected
msgid "Total expected duration (in minutes)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total real duration"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__duration
msgid "Total real duration (in minutes)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Traceability"
msgstr "İzləmə imkanı"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_traceability_report
msgid "Traceability Report"
msgstr "İzləmə imkanı haqqında hesabat"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_tracking
msgid "Tracking"
msgstr "İzləmə"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking
msgid "Transfer"
msgstr "Köçürmə"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Transfers"
msgstr "Transferlər"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "Əməliyyatın Növü"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_exception_decoration
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Qeyddəki istisna fəaliyyət növü."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Unable to split with more than the quantity to produce."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.js:0
msgid "Unblock"
msgstr "Blokdan çıxardın"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__unbuild_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild"
msgstr "Ləğv edin"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild Order"
msgstr "Sifarişi Ləğv edin"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_unbuild
#: model:ir.ui.menu,name:mrp.menu_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Unbuild Orders"
msgstr "Sifarişləri Ləğv edin"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Unbuild: %s"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unbuild_ids
msgid "Unbuilds"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Unbuilt"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.js:0
msgid "Unfold"
msgstr "Açmaq"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Unit"
msgstr "Vahid"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Unit Cost"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Unit Costs"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unit_factor
msgid "Unit Factor"
msgstr "Vahid Faktor"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_uom_name
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Unit of Measure"
msgstr "Ölçü  Vahidi"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_id
msgid ""
"Unit of Measure (Unit of Measure) is the unit of measurement for the "
"inventory control"
msgstr "Ölçü Vahid inventar nəzarəti üçün ölçü vahididir"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Units"
msgstr "Vahidlər"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_unlocked_by_default
msgid "Unlock Manufacturing Orders"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_unlocked_by_default
msgid "Unlocked by default"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unplan"
msgstr "Planlaşdırmayın"

#. module: mrp
#: model:ir.actions.server,name:mrp.mrp_production_action_unreserve_tree
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unreserve"
msgstr "Rezervdən çıxardın"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "UoM"
msgstr "Ölçü Vahidi"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Update BoM"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Upload your PDF file."
msgstr "PDF faylınızı yükləyin"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__1
msgid "Urgent"
msgstr "Təcili"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid ""
"Use Manufacturing Orders (MO) to build finished products while consuming "
"components: i.e. 1 Table = 4 Table Legs + 1 Table Top"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_workorder_dependencies
msgid "Use Operation Dependencies"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_reception_report
msgid "Use Reception Report with Manufacturing Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Used In"
msgstr "İstifadə olunur"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__user_id
#: model:res.groups,name:mrp.group_mrp_user
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "User"
msgstr "İstifadəçi"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Using a MPS report to schedule your reordering and manufacturing operations "
"is useful if you have long lead time and if you produce based on sales "
"forecasts."
msgstr ""
"Yenidən sifariş və istehsal əməliyyatlarını planlaşdırmaq üçün bir MPS "
"hesabatından istifadə etmək uzun müddətə sahib olduğunuzda və satış "
"proqnozlarına əsasən istehsal etdiyiniz təqdirdə faydalıdır."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__valid_details
msgid "Valid"
msgstr "Düzgün"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Validate"
msgstr "Təsdiqləyin"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_consumption_warning.py:0
msgid ""
"Values cannot be set and validated because a Lot/Serial Number needs to be "
"specified for a tracked product that is having its consumed amount "
"increased:%(products)s"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
msgid "Variant"
msgstr "Çeşid"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Vendor ABC"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "View WorkOrder"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"View and allocate production quantities to customer orders or other "
"manufacturing orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse_orderpoint__manufacturing_visibility_days
msgid "Visibility Days applied on the manufacturing routes."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Waiting"
msgstr "Gözlənilir"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__waiting
msgid "Waiting Another Operation"
msgstr "Növbəti Əməliyyat Gözlənilir"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Waiting Availability"
msgstr "Mövcud olması Gözlənilir"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__pending
msgid "Waiting for another WO"
msgstr "Başqa bir WO Gözlənilir"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__waiting
msgid "Waiting for components"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Waiting the previous work order, planned from %(start)s to %(end)s"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: model:ir.model,name:mrp.model_stock_warehouse
#: model:ir.model.fields,field_description:mrp.field_mrp_production__warehouse_id
msgid "Warehouse"
msgstr "Anbar"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warn_insufficient_qty_unbuild
msgid "Warn Insufficient Unbuild Quantity"
msgstr "Kifayət etməyən miqdarın ləğvi barədə xəbərdarlıq"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/stock_scrap.py:0
msgid "Warning"
msgstr "Xəbərdarlıq"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Warnings"
msgstr "Xəbərdarlıqlar"

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_wear_product_template
msgid "Wear Layer"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__website_message_ids
msgid "Website Messages"
msgstr "Veb sayt Mesajları"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__website_message_ids
msgid "Website communication history"
msgstr "Veb saytın kommunikasiya tarixçəsi"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__picking_type_id
msgid ""
"When a procurement has a ‘produce’ route with a operation type set, it will "
"try to create a Manufacturing Order for that product using a BoM of the same"
" operation type. That allows to define stock rules which trigger different "
"manufacturing orders with different BoMs."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__manual_consumption
#: model:ir.model.fields,help:mrp.field_stock_move__manual_consumption
msgid ""
"When activated, then the registration of consumption for that component is recorded manually exclusively.\n"
"If not activated, and any of the components consumption is edited manually on the manufacturing order, Odoo assumes manual consumption also."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__asap
msgid "When components for 1st operation are available"
msgstr "1-ci əməliyyat üçün komponentlər mövcud olduqda"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_to_resupply
msgid ""
"When products are manufactured, they can be manufactured in this warehouse."
msgstr "Məhsullar istehsal edildikdə, onlar bu anbarda istehsal edilə bilər."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid ""
"When products are needed in <b>%s</b>, <br/> a manufacturing order is "
"created to fulfill the need."
msgstr ""
"<b>%s</b> məhsullarına ehtiyac olduqda, <br/> ehtiyacını yerinə yetirmək "
"üçün bir istehsal sifarişi yaradılır."

#. module: mrp
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid ""
"With the Odoo work center control panel, your worker can start work orders "
"in the shop and follow instructions of the worksheet. Quality tests are "
"perfectly integrated into the process. Workers can trigger feedback loops, "
"maintenance alerts, scrap products, etc."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split_multi
msgid "Wizard to Split Multiple Productions"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split
msgid "Wizard to Split a Production"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder
msgid "Wizard to mark as done or create back order"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_panel_product_template
msgid "Wood Panel"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Work Center"
msgstr "İş Mərkəzi"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_capacity
msgid "Work Center Capacity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workcenter_load
msgid "Work Center Load"
msgstr "İş Mərkəzi Yükü"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Work Center Loads"
msgstr "İş Mərkəzi Yükləri"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Work Center Name"
msgstr "İş Mərkəzinin Adı"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "İş Mərkəzinin İstifadəsi"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
msgid "Work Center load"
msgstr "İş Mərkəzi Yükü"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_action
#: model:ir.ui.menu,name:mrp.menu_view_resource_search_mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Centers"
msgstr "İş Mərkəzləri"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_kanban_action
msgid "Work Centers Overview"
msgstr "İş Mərkəzlərinə Baxış"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Work Instruction"
msgstr "İş haqqında Təlimat"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_workorder
#: model:ir.model,name:mrp.model_mrp_workorder
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workorder_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__name
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__workorder_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__workorder_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Work Order"
msgstr "İş Sifarişi"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_workorder_dependencies
msgid "Work Order Dependencies"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Work Order Operations allow you to create and manage the manufacturing "
"operations that should be followed within your work centers in order to "
"produce a product. They are attached to bills of materials that will define "
"the required components."
msgstr ""
"İş Sifarişi Əməliyyatları, məhsul istehsal etmək üçün iş mərkəzlərinizdə "
"izlənilməsi lazım olan istehsal əməliyyatlarını yaratmağa və idarə etməyə "
"imkan verir. Lazımi komponentləri təyin edəcək məhsul reseplərinə əlavə "
"olunurlar."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__workorder_id
msgid "Work Order To Consume"
msgstr "İstehlak üçün İş Sifarişi"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_routing_time
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production_specific
#: model:ir.actions.act_window,name:mrp.action_work_orders
#: model:ir.actions.act_window,name:mrp.mrp_workorder_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_workorder_report
#: model:ir.actions.act_window,name:mrp.mrp_workorder_todo
#: model:ir.model.fields,field_description:mrp.field_mrp_production__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_work_order_report
#: model:ir.ui.menu,name:mrp.menu_mrp_workorder_todo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Orders"
msgstr "İş Sifarişləri"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workorder_workcenter_report
msgid "Work Orders Performance"
msgstr "İş Sifarişlərinin Performansı"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_workcenter
msgid "Work Orders Planning"
msgstr "İş Sifarişinin  Planlaşdırılması "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Work Sheet"
msgstr "İş Hesabı"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Work center"
msgstr "İş Mərkəzi"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"                    Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Work orders in progress. Click to block work center."
msgstr "İş sifarişləri davam edir. İş mərkəzini bloklamaq üçün klikləyin."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Workcenter"
msgstr "İş mərkəzi"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "Workcenter %s cannot be an alternative of itself."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Workcenter Productivity"
msgstr "İş mərkəzinin Məhsuldarlığı"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "İş mərkəzinin Məhsuldarlığı Jurnalı"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Workcenter Productivity Loss"
msgstr "İş mərkəzinin Məhsuldarlığı İtkisi"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss
msgid "Workcenter Productivity Losses"
msgstr "İş mərkəzinin Məhsuldarlığı İtkiləri"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__working_state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_state
msgid "Workcenter Status"
msgstr "İş mərkəzinin Vəziyyəti"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Workcenter blocked, click to unblock."
msgstr "İş mərkəzi bloklandı, blokdan çıxartmaq üçün klikləyin."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_calendar_id
msgid "Working Hours"
msgstr "İş Saatları"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_user_ids
msgid "Working user on this work order."
msgstr "Bu iş sifarişi üzərində işləyən istifadəçi."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_type
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet
msgid "Worksheet"
msgstr "İş səhifəsi "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_type
msgid "Worksheet Type"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_google_slide
msgid "Worksheet URL"
msgstr "İş səhifəsi URL"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid ""
"Write one line per finished product to produce, with serial numbers as "
"follows:\n"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__yesterday
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Yesterday"
msgstr "Dünən"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_lot.py:0
msgid ""
"You are not allowed to create or edit a lot or serial number for the "
"components with the operation type \"Manufacturing\". To change this, go on "
"the operation type and tick the box \"Create New Lots/Serial Numbers for "
"Components\"."
msgstr ""
"Sizə \"İstehsal\" əməliyyatı növü olan komponentlər üçün partiya və ya "
"seriya nömrəsini yaratmağa və ya redaktə etməyə icazə verilmir. Bunu "
"dəyişmək üçün əməliyyat növünə gedin və \"Komponentlər üçün yeni "
"partiyalar/seriya nömrələri yarat\" işarəsini qeyd edin."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"You can not create a kit-type bill of materials for products that have at "
"least one reordering rule."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"You can not delete a Bill of Material with running manufacturing orders.\n"
"Please close or cancel it first."
msgstr ""
"İstehsal sifarişləri olan bir Məhsul Reseptini silə bilməzsiniz.\n"
"Xahiş edirəm əvvəlcə bağlayın və ya ləğv edin."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"You can only merge manufacturing orders of identical products with same BoM."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"You can only merge manufacturing orders with no additional components or by-"
"products."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You can only merge manufacturing with the same operation type"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You can only merge manufacturing with the same state."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"You cannot change the workcenter of a work order that is in progress or "
"done."
msgstr ""
"Siz yerinə yetirilən və ya yerinə yetirilmiş iş tapşırığının iş mərkəzini "
"dəyişə bilməzsiniz."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "You cannot create a new Bill of Material from here."
msgstr "Buradan yeni Məhsul Resepti yarada bilməzsiniz."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot create cyclic dependency."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You cannot delete an unbuild order if the state is 'Done'."
msgstr ""
"Siz ləğv edilmiş sifarişi, əgər o \"İcra edilib\" vəziyyətindədirsə silə "
"bilməzsiniz."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You cannot have %s  as the finished product and in the Byproducts"
msgstr ""
"Siz %s-dən hazır məhsul kimi və yan məhsul kimi istifadə edə bilməzsiniz."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot link this work order to another manufacturing order."
msgstr ""
"Siz bu iş sifarişini başqa bir istehsal sifarişi ilə əlaqələndirə "
"bilməzsiniz."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You cannot move a manufacturing order once it is cancelled or done."
msgstr ""
"Siz istehsal sifarişini ləğv edildikdən və ya yerinə yetirildikdən sonra "
"köçürə bilməzsiniz."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot produce the same serial number twice."
msgstr "Siz eyni seriya nömrəsini iki dəfə istehsal edə bilməzsiniz."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot start a work order that is already done or cancelled"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You cannot unbuild a undone manufacturing order."
msgstr "Siz icra edilməmiş istehsal sifarişini ləğv edə bilməzsiniz."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"You cannot use the 'Apply on Variant' functionality and simultaneously "
"create a BoM for a specific variant."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid ""
"You consumed a different quantity than expected for the following products.\n"
"                        <b invisible=\"consumption == 'strict'\">\n"
"                            Please confirm it has been done on purpose.\n"
"                        </b>\n"
"                        <b invisible=\"consumption != 'strict'\">\n"
"                            Please review your component consumption or ask a manager to validate\n"
"                            <span invisible=\"mrp_production_count != 1\">this manufacturing order</span>\n"
"                            <span invisible=\"mrp_production_count == 1\">these manufacturing orders</span>.\n"
"                        </b>"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You need at least two production orders to merge them."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"You need to define at least one productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""
"Siz 'Effektivlik' kateqoriyasında ən azı bir məhsuldarlıq azalmasını müəyyən"
" etməlisiniz. Birini İstehsal tətbiqinin menyusundan yaradın: Konfiqurasiya "
"/ Məhsuldarlıq Azalması."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"You need to define at least one productivity loss in the category "
"'Productivity'. Create one from the Manufacturing app, menu: Configuration /"
" Productivity Losses."
msgstr ""
"Siz 'Məhsuldarlıq' kateqoriyasında ən azı bir məhsuldarlıq azalmasını "
"müəyyən etməlisiniz. Birini İstehsal tətbiqinin menyusundan yaradın: "
"Konfiqurasiya / Məhsuldarlıq Azalması."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid ""
"You need to define at least one unactive productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""
"Siz 'Effektivlik' kateqoriyasında aktiv olmayan ən azı bir məhsuldarlıq "
"azalmasını müəyyən etməlisiniz. Birini İstehsal tətbiqinin menyusundan "
"yaradın: Konfiqurasiya / Məhsuldarlıq Azalması."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You need to provide a lot for the finished product."
msgstr "Hazır məhsul üçün çoxlu sayda təmin etmək lazımdır."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"You need to supply Lot/Serial Number for products and 'consume' them: "
"%(missing_products)s"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_backorder
msgid "You produced less than initial demand"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You should provide a lot number for the final product."
msgstr "Son məhsul üçün siz partiya nömrəsini təqdim etməlisiniz."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_quant.py:0
msgid ""
"You should update the components quantity instead of directly updating the "
"quantity of the kit product."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__done_mrp_lot_label_to_print__zpl
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__generated_mrp_lot_label_to_print__zpl
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__mrp_product_label_to_print__zpl
msgid "ZPL"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "cancelled"
msgstr "Ləğv olundu"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "days"
msgstr "Gün"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "days before"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "expected duration"
msgstr "Ehtimal edilən Müddət"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "from location"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "it is added as a component in a manufacturing order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"it is moved via a transfer, such as a receipt or a delivery order for "
"instance."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "last"
msgstr "Ən son"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "manufacturing order"
msgstr "istehsal sifarişi"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "merged"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "minutes"
msgstr "Dəqiqələr"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "of"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "ordered instead of"
msgstr "əvəzinə sifariş verildi"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "per workcenter"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "quantity has been updated."
msgstr "miqdar yeniləndi."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "real duration"
msgstr "Real Müddət"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "split"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "work orders"
msgstr "İş Sifarişləri"
