<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_package_destination_form_view" model="ir.ui.view">
        <field name="name">stock.package.destination.view</field>
        <field name="model">stock.package.destination</field>
        <field name="arch" type="xml">
            <form>
                <div>
                    You are trying to put products going to different locations into the same package
                </div>
                <div>
                    <field name="move_line_ids" style="margin-top:10px;">
                        <list>
                            <field name="product_id"/>
                            <field name="location_dest_id"/>
                            <field name="quantity" String="quantity"/>
                            <field name="lot_id" groups="stock.group_production_lot"/>
                        </list>
                        <kanban>
                            <templates>
                                <t t-name="card">
                                    <div class="row">
                                        <field name="product_id" class="col-6"/>
                                        <field name="quantity" class="col-6" String="quantity"/>
                                    </div>
                                    <field name="location_dest_id"/>
                                </t>
                            </templates>
                        </kanban>
                    </field>
                </div>
                <div>
                    <strong>Where do you want to send the products?</strong>
                </div>
                <div>
                    <field name="filtered_location" invisible="1"/>
                    <field name="location_dest_id" domain="[('id', 'in', filtered_location)]" options="{'no_create': True, 'no_open': True}"/>
                </div>
                <footer>
                    <button string="Confirm" name="action_done" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Discard" name="cancel_button" class="btn-secondary" special="cancel" data-hotkey="x"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
