<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="stock.popoverButton">
        <a tabindex="0" t-on-click.stop="showPopup" t-attf-class="p-1 fa #{ icon || 'fa-info-circle'} #{ color || 'text-primary'}"/>
    </t>

    <div t-name="stock.popoverContent" class="m-3">
        <h6 t-out="props.title"/>
        <t t-if="props.popoverTemplate" t-call="{{props.popoverTemplate}}" />
        <t t-else="" t-out="props.msg"/>
    </div>
</templates>
