<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_putaway_list" model="ir.ui.view">
        <field name="name">stock.putaway.rule.list</field>
        <field name="model">stock.putaway.rule</field>
        <field name="arch" type="xml">
            <list string="Putaway Rules" editable="bottom" sample='1'>
                <field name="company_id" column_invisible="True"/>
                <field name="package_type_ids" column_invisible="True"/>
                <field name="sequence" widget="handle"
                       column_invisible="context.get('invisible_handle', False)"/>
                <field name="location_in_id" string="When product arrives in"
                       options="{'no_create': True}"
                       readonly="context.get('fixed_location', False)"/>
                <field name="product_id" string="Product"
                       options="{'no_create': True, 'no_open': True}"
                       readonly="context.get('single_product', False) or category_id"
                       placeholder="All Products"
                       force_save="1"/>
                <field name="category_id" string="Product Category"
                       options="{'no_create': True, 'no_open': True}"
                       readonly="context.get('fixed_category', False) or product_id"
                       placeholder="All Categories"
                       force_save="1"/>
                <field name="package_type_ids" string="Package type"
                       options="{'no_create': True, 'no_open': True}"
                       groups="stock.group_tracking_lot" widget="many2many_tags"
                       optional="show"/>
                <field name="location_out_id" string="Store to"
                       readonly="not location_in_id"
                       options="{'no_create': True}"
                       optional="show"/>
                <field name="sublocation"
                       optional="show"
                       required="True"
                       force_save="1"/>
                <field name="storage_category_id" string="Having Category"
                       groups="stock.group_stock_multi_locations"
                       options="{'no_create': True}"
                       required="sublocation == 'closest_location'"
                       readonly="sublocation == 'no'"
                       placeholder="Any Category"
                       optional="show"/>
                <field name="company_id" groups="stock.group_stock_multi_locations" force_save="1" readonly="context.get('fixed_location', False)" options="{'no_create': True}" optional="show"/>
            </list>
        </field>
    </record>

    <record id="view_removal" model="ir.ui.view">
        <field name="name">product.removal.form</field>
        <field name="model">product.removal</field>
        <field name="arch" type="xml">
            <form string="Removal">
                <group col="4">
                    <field name="name"/>
                    <field name="method"/>
                </group>
           </form>
        </field>
    </record>

    <record id="action_putaway_tree" model="ir.actions.act_window">
        <field name="name">Putaways Rules</field>
        <field name="res_model">stock.putaway.rule</field>
        <field name="view_mode">list</field>
        <field name="view_id" ref="stock_putaway_list"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No putaway rule found. Let's create one!
            </p><p>
                Create new putaway rules to dispatch automatically specific products to their appropriate destination location upon receptions.
            </p>
        </field>
    </record>

    <record id="view_putaway_search" model="ir.ui.view">
        <field name="name">stock.putaway.rule.search</field>
        <field name="model">stock.putaway.rule</field>
        <field name="arch" type="xml">
            <search string="Putaway Rules">
                <field name="product_id"/>
                <field name="category_id"/>
                <field name="location_in_id"/>
                <field name="location_out_id"/>
                <group expand='0' string='Filters'>
                    <filter name="filter_to_rules_on_product"
                            string="Rules on Products"
                            domain="[('product_id', '!=', False)]"/>
                    <filter name="filter_to_rules_on_category"
                            string="Rules on Categories"
                            domain="[('category_id' ,'!=', False)]"/>
                </group>
                <group expand="0" string="Group By">
                        <filter string="Location: When arrives to" name="location_in" context="{'group_by':'location_in_id'}"/>
                        <filter string="Location: Store to" name="location_out" context="{'group_by':'location_out_id'}"/>
                    </group>
            </search>
        </field>
    </record>

    <record model="ir.actions.act_window" id="category_open_putaway"> <!-- Putaway rules from category -->
        <field name="name">Putaway Rules</field>
        <field name="res_model">stock.putaway.rule</field>
        <field name="context">{
            'search_default_category_id': [active_id],
            'fixed_category': True,
        }</field>
    </record>
    <record model="ir.actions.act_window" id="location_open_putaway"> <!-- Putaway rules from location -->
        <field name="name">Putaway Rules</field>
        <field name="res_model">stock.putaway.rule</field>
        <field name="context">{'fixed_location': True}</field>
        <field name="domain">['|', ('location_out_id', '=', active_id), ('location_in_id', '=', active_id)]</field>
    </record>

    <menuitem id="menu_putaway" name="Putaway Rules" parent="stock.menu_warehouse_config"
        action="action_putaway_tree" sequence="8" groups="stock.group_stock_multi_locations"/>
</odoo>
