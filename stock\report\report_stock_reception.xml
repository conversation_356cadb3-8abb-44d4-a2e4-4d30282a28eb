<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Reception Report Labels -->
    <template id="report_reception_report_label">
        <t t-if="quantity" t-set="qtys" t-value="[int(q) for q in quantity.split(',')]"/>
        <t t-else="" t-set="qtys" t-value="[1 for q in range(len(docs))]"/>
        <t t-call="web.basic_layout">
            <div class="page">
                <t t-foreach="range(len(docs))" t-as="index">
                    <t t-set="move" t-value="docs[index]"/>
                    <t t-set="qty" t-value="qtys[index]"/>
                    <t t-set="source" t-value="move._get_source_document()"/>
                    <t t-foreach="range(qty)" t-as="j">
                        <div class="o_label_page o_label_dymo">
                            <span t-out="move.product_id.display_name">Product Display Name</span> <br/>
                            <span t-if="source" t-out="source.name">Source Name</span>
                            <div class="address"
                                t-if="move.picking_id and move.picking_id.partner_id"
                                t-field="move.picking_id.partner_id"
                                t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True}'/>
                        </div>
                    </t>
                </t>
            </div>
        </t>
    </template>

    <!-- Reception Report -->
    <record id="stock_reception_report_action" model="ir.actions.report">
        <field name="name">Reception Report</field>
        <field name="model">stock.picking</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">stock.report_reception</field>
    </record>

    <record id="stock_reception_action" model="ir.actions.client">
        <field name="name">Reception Report</field>
        <field name="tag">reception_report</field>
        <field name="res_model">report.stock.report_reception</field>
    </record>

    <template id="report_reception_body">
        <div class="o_report_reception justify-content-between">
            <div class="o_report_reception_header my-5">
                <h3>
                    <t t-if="docs">
                        <t t-foreach="docs" t-as="doc">
                            <div class="p-2">
                                <a href="#" t-att-res-model="doc_model" view-type="form" t-att-res-id="doc.id">
                                    <span t-out="doc.display_name">Demo Display Name</span>
                                </a>
                                <span t-field="doc.display_name" t-options="{'widget': 'barcode', 'width': 300, 'height': 50}">Display Name</span>
                                <span t-field="doc.state" t-attf-class="badge rounded-pill bg-opacity-50 #{'bg-success' if doc.state == 'done' else 'bg-info'}">Andrwep</span>
                            </div>
                        </t>
                    </t>
                    <t t-else="">
                        <span t-out="reason">Reason</span>
                    </t>
                </h3>
            </div>
            <table t-if="sources_to_lines" class="o_report_reception_table table table-sm text-start">
                <t t-foreach="sources_to_lines" t-as="source">
                    <thead t-if="any(line['is_assigned'] for line in sources_to_lines[source])">
                        <tr class="bg-light">
                            <th>
                                <span t-field="source[0].display_name" t-options="{'widget': 'barcode', 'width': 200, 'height': 30}">Display Name</span>
                                <i t-if="source[0].priority == '1'" class="o_priority o_priority_star fa fa-star"/>
                                <a name="source_link" href="#" t-att-res-model="source[0]._name" view-type="form" t-att-res-id="source[0].id">
                                    <span t-out="source[0].display_name">DEMO_SOURCE_DISPLAY_NAME</span>
                                </a>
                                <span t-if="len(source) > 1">
                                    (<a name="origin_link" href="#" t-att-res-model="source[1]._name" view-type="form" t-att-res-id="source[1].id">
                                    <span t-out="source[1].display_name">DEMO_ORIGIN_DISPLAY_NAME</span></a>)
                                </span>
                                <span t-if="source[0]._name == 'stock.picking' and source[0].partner_id">:
                                    <a name="source_link" href="#" t-att-res-model="source[0].partner_id._name" t-att-res-id="source[0].partner_id.id" view-type="form">
                                        <span t-out="source[0].partner_id.name">DEMO_PARTNER_NAME</span>
                                    </a>
                                </span>
                            </th>
                            <th>Expected Delivery: <span t-out="sources_to_formatted_scheduled_date[source]">DEMO_DATE</span></th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-set="possible_moves" t-value="sources_to_lines[source]"/>
                        <t t-foreach="possible_moves" t-as="line">
                            <!-- don't print non-assigned lines when printing pdf -->
                            <tr t-if="line['is_assigned']">
                                <td>
                                    <span t-out="line['product']['display_name']">DEMO_PRODUCT_DISPLAY_NAME</span>
                                </td>
                                <td>
                                    <span t-out="line['quantity']">DEMO_QUANTITY</span>
                                    <span groups="uom.group_uom" t-out="line['uom']">DEMO_UOM</span>
                                </td>
                            </tr>
                        </t>
                    </tbody>
                </t>
            </table>
            <p t-else="">
                No allocation need found.
            </p>
        </div>
    </template>

    <template id="report_reception">
        <t t-call="web.html_container">
            <t t-call="web.internal_layout">
                <t t-call="stock.report_reception_body"/>
            </t>
        </t>
    </template>
</odoo>
