<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="mrp_bom_view_form_inherit_mrp_custom" model="ir.ui.view">
        <field name="name">mrp.bom.view.form.inherit.mrp.custom</field>
        <field name="model">mrp.bom</field>
        <field name="inherit_id" ref="mrp.mrp_bom_form_view" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='type']" position="after">
                <field
                    name="product_label_id"
                    invisible="type != 'normal'"
                    options="{'no_create_edit': True, 'no_create': True}"
                />
                <field
                    name="is_packaging_nomenclature"
                />
            </xpath>
        </field>
    </record>
</odoo>
