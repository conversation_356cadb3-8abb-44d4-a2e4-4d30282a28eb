<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">
    <div t-name="stock.leadDays">
        <h2>Lead Times</h2>
        <hr/>
        <p>
            The forecasted stock on the <b t-esc="jsonValue.lead_days_date"/>
            is <t t-if="jsonValue.qty_to_order &lt;= 0"><b><t t-esc="qtyForecast"/></b></t><t t-else="">
            below the inventory <b>minimum of <t t-esc="productMinQty"/>
            : <t t-esc="qtyToOrder"/> should be replenished</b> to reach the maximum of
            <t t-esc="productMaxQty"/>.</t>
        </p>
        <table t-if="jsonValue.lead_days_description" class="table table-borderless table-sm">
            <tbody>
                <tr>
                    <td>Today</td>
                    <td class="text-end" t-out="jsonValue.today"/>
                </tr>
                <tr t-foreach="jsonValue.lead_days_description" t-key="descr" t-as="descr">
                    <td t-out="descr[0]"/>
                    <td class="text-end" t-out="descr[1]"/>
                </tr>
                <tr class="table-info">
                    <td>Forecasted Date</td>
                    <td class="text-end text-nowrap">
                        = <t t-out="jsonValue.lead_days_date"/>
                    </td>
                </tr>
                <tr t-if="jsonValue.visibility_days > 0">
                    <td>Visibility days</td>
                    <td class="text-end text-nowrap">
                        + <t t-out="jsonValue.visibility_days"/> Day(s)
                    </td>
                </tr>
                <tr t-if="jsonValue.visibility_days > 0" class="table-info fw-bold">
                    <td>Forecasted Date + Visibility Days</td>
                    <td class="text-end text-nowrap">
                        = <t t-out="jsonValue.visibility_days_date"/>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <div t-name="stock.replenishmentHistory">
        <h2>Sales History</h2>
        <hr/>
        <table t-if="jsonValue.replenishment_history" class="table table-borderless">
            <tbody>
                <t t-foreach="jsonValue.replenishment_history" t-key="line.name" t-as="line">
                    <tr>
                        <td><t t-esc="line.name"/></td>
                        <td class="text-end text-nowrap" t-esc="line.quantity + ' ' + line.uom_name"/>
                    </tr>
                </t>
            </tbody>
        </table>
    </div>
</templates>
