<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_change_product_quantity" model="ir.ui.view">
            <field name="name">Change Product Quantity</field>
            <field name="model">stock.change.product.qty</field>
            <field name="arch" type="xml">
                <form string="Update Product Quantity">
                    <div class="container">
                        <field name="product_tmpl_id" invisible="1"/>
                        <field name="product_variant_count" invisible="1"/>
                        <div class="row" invisible="context.get('default_product_id')">
                            <label for="product_id" class="col-5 col-md-4 col-lg-3"/>
                            <field name="product_id" class="col-6"
                                options="{'no_open': True, 'no_create': True}"
                                domain="[('product_tmpl_id', '=', product_tmpl_id)]"
                                readonly="context.get('default_product_id')"
                                invisible="product_variant_count == 1"/>
                        </div>
                        <div class="row">
                            <label for="new_quantity" class="col-5 col-md-4 col-lg-3"/>
                            <div class="col-6 col-md-5 col-lg-4 row">
                                <field name="new_quantity" class="col-4 pe-0"/>
                                <field name="product_uom_id" groups="uom.group_uom" options="{'no_open': True}"
                                    class="col-8 ps-2"/>
                            </div>
                        </div>
                    </div>
                    <footer>
                        <button name="change_product_qty" string="Apply" type="object" class="btn-primary" data-hotkey="q"/>
                        <button string="Discard" class="btn-secondary" special="cancel" data-hotkey="x"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_change_product_quantity" model="ir.actions.act_window">
            <field name="name">Change Product Quantity</field>
            <field name="res_model">stock.change.product.qty</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
    </data>
</odoo>
