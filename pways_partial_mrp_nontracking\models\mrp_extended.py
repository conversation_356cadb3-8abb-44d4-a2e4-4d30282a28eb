from odoo import models, fields, api, _
from datetime import date, datetime
from odoo.exceptions import UserError, ValidationError
from odoo.tools import float_round
from odoo.tools.float_utils import float_compare

class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    qty_equal = fields.Boolean(string='Qty Equal', compute='_compute_qty_equal')
    qty_consuming = fields.Float(string="Partial Qty", digits='Product Unit of Measure')
    over_prod_qty = fields.Float(string="Total Produce Qty", digits='Product Unit of Measure', compute='_compute_over_production_qty')

    @api.depends('move_finished_ids')
    def _compute_over_production_qty(self):
        for production in self:
            finish_moves = production.move_finished_ids.filtered(lambda m: m.product_id == production.product_id and m.state == 'done')
            qty_consuming = sum(finish_moves.mapped('quantity'))
            production.over_prod_qty = qty_consuming

    @api.onchange('qty_consuming')
    def _onchange_qty_consuming(self):
        qty_producing = 0
        for production in self:
            if production.qty_consuming >= 1:
                finish_moves = production.move_finished_ids.filtered(lambda m: m.product_id == production.product_id and m.state == 'done')
                qty_consuming = sum(finish_moves.mapped('quantity'))
                production.qty_producing = production.qty_consuming  + qty_consuming


    @api.depends('qty_produced', 'product_qty')
    def _compute_qty_equal(self):
        self.qty_equal = False
        for record in self:
            record.qty_equal = record.qty_produced == record.product_qty

    def action_produced_continue(self):

        for production in self:
            if not production.qty_consuming:
                raise ValidationError(_("Please enter partial production quantity in order."))

            for rec in production.move_raw_ids:
                if not rec.quantity:
                    raise ValidationError(_("Please enter done quantity in components."))

            finish_moves = production.move_finished_ids.filtered(lambda m: m.product_id == production.product_id and m.state not in ('done', 'cancel'))
            for move in finish_moves:
                if move.quantity and move.picked:
                    continue
                # Update the lot_id for move lines in non-final states if it's not already set
                if move.product_id.tracking == 'lot' :
                    if production.lot_producing_id :
                        for sm_line in move.filtered(lambda m: m.state not in ('done', 'cancel')).move_line_ids:
                            if not sm_line.lot_id :
                                sm_line.lot_id = production.lot_producing_id
                move._set_quantity_done(float_round(production.qty_consuming, precision_rounding=production.product_uom_id.rounding, rounding_method='HALF-UP'))

            for move in production.move_raw_ids | production.move_finished_ids:
                rounding = self.env['decimal.precision'].precision_get('Product Unit of Measure')
                if float_compare(move.quantity, move.product_uom_qty, precision_digits=rounding) <= 0:
                    move.picked = True
                move._action_done()

            production.qty_consuming = 0.0

        return True


