from odoo import api, fields, models


class MrpBom(models.Model):
    _inherit = "mrp.bom"

    product_label_id = fields.Many2one(
        "product.product",
        string="Product label",
        domain="[('id', 'in', product_ids_allowed)]",
        help="Product used for label number control.",
    )
    product_ids_allowed = fields.Many2many(
        "product.product",
        compute="_compute_product_ids_allowed",
        store=False,
    )
    is_packaging_nomenclature = fields.<PERSON><PERSON><PERSON>(
        string="Packaging Nomenclature"
    )

    # -------------------------------------------------------------------------
    # COMPUTE METHODS
    # -------------------------------------------------------------------------

    @api.depends("bom_line_ids.product_id")
    def _compute_product_ids_allowed(self):
        for bom in self:
            bom.product_ids_allowed = bom.bom_line_ids.mapped("product_id")
