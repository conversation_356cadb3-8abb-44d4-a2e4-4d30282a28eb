# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-11 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"\n"
"\n"
"Transfers %(transfer_list)s: You need to supply a Lot/Serial number for products %(product_list)s."
msgstr ""
"\n"
"\n"
"Transfers %(transfer_list)s: Sie müssen eine Los-/Seriennummer für Produkte %(product_list)s angeben."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"\n"
"(%(serial_number)s) exists in location %(location)s"
msgstr ""
"\n"
"(%(serial_number)s) existiert in %(location)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"\n"
"The quantity done for the product %(product)s doesn't respect the rounding precision defined on the unit of measure %(unit)s.\n"
"Please change the quantity done or the rounding precision of your unit of measure."
msgstr ""
"\n"
"Die Menge, die für das Produkt „%(product)s“ angegeben wurde, entspricht nicht der Rundungsgenauigkeit, die für die Maßeinheit „%(unit)s“ definiert wurde.\n"
"Bitte ändern Sie die angegebene Menge oder die Rundungsgenauigkeit Ihrer Maßeinheit."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__state
msgid ""
" * Draft: The transfer is not confirmed yet. Reservation doesn't apply.\n"
" * Waiting another operation: This transfer is waiting for another operation before being ready.\n"
" * Waiting: The transfer is waiting for the availability of some products.\n"
"(a) The shipping policy is \"As soon as possible\": no product could be reserved.\n"
"(b) The shipping policy is \"When all products are ready\": not all the products could be reserved.\n"
" * Ready: The transfer is ready to be processed.\n"
"(a) The shipping policy is \"As soon as possible\": at least one product has been reserved.\n"
"(b) The shipping policy is \"When all products are ready\": all product have been reserved.\n"
" * Done: The transfer has been processed.\n"
" * Cancelled: The transfer has been cancelled."
msgstr ""
"* Entwurf: Dieser Transfer ist noch nicht bestätigt. Reservierungen werden "
"nicht angewendet. * Wartet auf anderen Vorgang: Bevor dieser Transfer bereit"
" ist, muss auf einen anderen Vorgang gewartet werden. * Wartet: Dieser "
"Transfer wartet auf die Verfügbarkeit von einigen Produkten. (a) Die "
"Versandbedingung ist „Sobald wie möglich“: Keine Produkte können reserviert "
"werden. (b) Die Versandbedingung ist „Wenn alle Produkte bereit sind“: Nicht"
" alle Produkte können reserviert. * Bereit: Der Transfer ist zur Bearbeitung"
" bereit. (a) Die Versandbedingung ist „Sobald wie möglich“: Wenigstens ein "
"Produkt kann reserviert werden. (b) Die Versandbedingung ist „Wenn alle "
"Produkte bereit sind“: Alle Produkte können reserviert werden. * Erledigt: "
"Der Transfer wurde getätigt. * Abgebrochen: Der Transfer wurde abgebrochen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid " - Product: %(product)s, Lot/Serial Number: %(lot)s"
msgstr "- Produkt: %(product)s, Seriennummer: %(lot)s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__cyclic_inventory_frequency
#: model:ir.model.fields,help:stock.field_stock_quant__cyclic_inventory_frequency
msgid ""
" When different than 0, inventory count date for products stored at this "
"location will be automatically set at the defined frequency."
msgstr ""
"Wenn dieser Wert von 0 abweicht, wird das Zähldatum für die an diesem Ort "
"gelagerten Produkte automatisch mit der festgelegten Häufigkeit angesetzt."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_count
msgid "# Returns"
msgstr "# Retouren"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s (copy)(%(id)s)"
msgstr "%(name)s (Kopie)(%(id)s)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence cross dock"
msgstr "%(name)s Cross-Docking-Sequenz "

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence in"
msgstr "%(name)s: Sequenz für Eingang"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence internal"
msgstr "%(name)s: Sequenz für interne Transfers"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence out"
msgstr "%(name)s: Sequenz für Ausgang"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence packing"
msgstr "%(name)s: Sequenz für Verpackung"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence picking"
msgstr "%(name)s: Sequenz für Kommissionierung"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence quality control"
msgstr "%(name)s: Sequenz für Qualitätskontrolle"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence storage"
msgstr "%(name)s: Sequenz für Lagerung"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"%(operations)s have default source or destination locations within warehouse"
" %(warehouse)s, therefore you cannot archive it."
msgstr ""
"%(operations)s haben Standardquell- oder -ziellagerorte im Lager "
"%(warehouse)s, daher können Sie sie nicht archivieren."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "%(product)s: Insufficient Quantity To Scrap"
msgstr "%(product)s: unzureichende auszurangierende Menge"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"%(product_name)s --> Product UoM is %(product_uom)s "
"(%(product_uom_category)s) - Move UoM is %(move_uom)s "
"(%(move_uom_category)s)"
msgstr ""
"%(product_name)s --> Maßeinheit des Produkts ist %(product_uom)s "
"(%(product_uom_category)s) - Maßeinheit der Bewegung ist %(move_uom)s "
"(%(move_uom_category)s)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "%(warehouse)s Sequence %(code)s"
msgstr "%(warehouse)s Sequenz %(code)s"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid ""
"%(warehouse)s can only provide %(free_qty)s %(uom)s, while the quantity to "
"order is %(qty_to_order)s %(uom)s."
msgstr ""
"In %(warehouse)s sind nur %(free_qty)s %(uom)s verfügbar, während die zu "
"bestellende Menge %(qty_to_order)s %(uom)s beträgt."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(warehouse)s: Supply Product from %(supplier)s"
msgstr "%(warehouse)s: Produkt nachliefern aus %(supplier)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_package_type.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_rule.py:0
#: code:addons/stock/models/stock_storage_category.py:0
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%s (copy)"
msgstr "%s (Kopie)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "%s [reverted]"
msgstr "%s [zurückgesetzt]"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid "%s days"
msgstr "%s Tagen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "%s: Can't split: quantities done can't be above demand"
msgstr ""
"%s: Kann nicht aufgeteilt werden: Die produzierten Mengen können nicht über "
"dem Bedarf liegen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"%s: Nothing to split, all demand is done. For split you need at least one "
"line not fully fulfilled"
msgstr ""
"%s: Es gibt nichts aufzuteilen, der Bedarf sind erfüllt. Für die Aufteilung "
"benötigen Sie mindestens eine nicht vollständig erfüllte Zeile"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"%s: Nothing to split. Fill the quantities you want in a new transfer in the "
"done quantities"
msgstr ""
"%s: Nichts zu teilen. Füllen Sie die gewünschten Mengen in einem neuen "
"Transfer in die erledigten Mengen"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_inventory
msgid "'Count Sheet'"
msgstr "'Zählbogen'"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_delivery
msgid ""
"'Delivery Slip - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr ""
"'Lieferschein - %s - %s' % (object.partner_id.name or '', object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_location_barcode
msgid "'Location - %s' % object.name"
msgstr "'Lagerort - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_lot_label
msgid "'Lot-Serial - %s' % object.name"
msgstr "'Los-Serie - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_type_label
msgid "'Operation-type - %s' % object.name"
msgstr "'Vorgangsart - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_packages
msgid "'Packages - %s' % (object.name)"
msgstr "'Pakete - %s' % (object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking
msgid ""
"'Picking Operations - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr ""
"'Kommissioniervorgänge - %s - %s' % (object.partner_id.name or '', "
"object.name)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "(copy of) %s"
msgstr "(Kopie von) %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(document barcode)"
msgstr "(Dokumentbarcode)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(package barcode)"
msgstr "(Paketbarcode)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(product barcode)"
msgstr "(Produktbarcode)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(serial barcode)"
msgstr "(Serienbarcode)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__state
#: model:ir.model.fields,help:stock.field_stock_move_line__state
msgid ""
"* New: The stock move is created but not confirmed.\n"
"* Waiting Another Move: A linked stock move should be done before this one.\n"
"* Waiting Availability: The stock move is confirmed but the product can't be reserved.\n"
"* Available: The product of the stock move is reserved.\n"
"* Done: The product has been transferred and the transfer has been confirmed."
msgstr ""
"* Neu: Die Lagerbuchung wurde erstellt, aber nicht bestätigt.\n"
"* Warten auf andere Bewegung: Eine verknüpfte Lagerbuchung sollte vor dieser durchgeführt werden.\n"
"* Warten auf Verfügbarkeit: Die Lagerbuchung ist bestätigt, aber das Produkt kann nicht reserviert werden.\n"
"* Verfügbar: Das Produkt der Lagerbuchung ist reserviert.\n"
"* Erledigt: Das Produkt wurde bewegt und der Transfer wurde bestätigt."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__usage
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_usage
#: model:ir.model.fields,help:stock.field_stock_move__location_usage
#: model:ir.model.fields,help:stock.field_stock_move_line__location_dest_usage
#: model:ir.model.fields,help:stock.field_stock_move_line__location_usage
msgid ""
"* Vendor Location: Virtual location representing the source location for products coming from your vendors\n"
"* View: Virtual location used to create a hierarchical structures for your warehouse, aggregating its child locations ; can't directly contain products\n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"* Customer Location: Virtual location representing the destination location for products sent to your customers\n"
"* Inventory Loss: Virtual location serving as counterpart for inventory operations used to correct stock levels (Physical inventories)\n"
"* Production: Virtual counterpart location for production operations: this location consumes the components and produces finished products\n"
"* Transit Location: Counterpart location that should be used in inter-company or inter-warehouses operations"
msgstr ""
"* Standort des Lieferanten: virtueller Standort, der den Herkunftsort für Produkte von Ihren Lieferanten darstellt.\n"
"* Ansicht: virtueller Lagerort, der dazu dient, eine hierarchische Struktur für Ihr Lager zu erstellen, indem er seine untergeordneten Standorte zusammenfasst; kann keine Produkte direkt enthalten\n"
"* Interner Lagerort: physische Standorte innerhalb Ihrer eigenen Lagerhäuser\n"
"* Kundenstandort: virtueller Standort, der den Zielort für Produkte darstellt, die an Ihre Kunden geliefert werden.\n"
"* Bestandsschwund: virtueller Standort, der als Gegenstück für Inventurvorgängen dient, die zur Korrektur von Lagerbeständen verwendet werden (physische Inventuren)\n"
"* Produktion: virtueller Gegenstandort für Produktionsvorgänge: dieser Ort verbraucht die Komponenten und produziert die Endprodukte\n"
"* Umschlagslager: Gegenstandort, der für Vorgänge zwischen Unternehmen oder zwischen Lagern verwendet werden sollte."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+ %d Tag(e)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid ", max:"
msgstr ", Max:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "-&gt;"
msgstr "-&gt;"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"         Manuelle Aktionen können erforderlich sein."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__day
msgid "1 Day"
msgstr "1 Tag"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__month
msgid "1 Month"
msgstr "1 Monat"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__week
msgid "1 Week"
msgstr "1 Woche"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "12.0"
msgstr "12.0"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_generic_barcode
msgid "1234567890"
msgstr "1234567890"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "12345678901"
msgstr "12345678901"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__2x7xprice
msgid "2 x 7 with price"
msgstr "2 x 7 mit Preis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "2021-9-01"
msgstr "2021-9-01"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "2023-01-01"
msgstr "2023-01-01"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "2023-09-24"
msgstr "2023-09-24"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "3.00"
msgstr "3,00"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__print_format__4x12
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x12
msgid "4 x 12"
msgstr "4 x 12"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__4x12_lots
msgid "4 x 12 - One per lot/SN"
msgstr "4 x 12 - Eins pro Los/SN"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__4x12_units
msgid "4 x 12 - One per unit"
msgstr "4 x 12 - Eins pro Einheit"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x12xprice
msgid "4 x 12 with price"
msgstr "4 x 12 mit Preis"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x7xprice
msgid "4 x 7 with price"
msgstr "4 x 7 mit Preis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "54326786758"
msgstr "54326786758"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid ""
"<br/>\n"
"                    <strong>Current Inventory: </strong>"
msgstr ""
"<br/>\n"
"                    <strong>Aktueller Bestand: </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid ""
"<br/>\n"
"                Want to speed up operations?"
msgstr ""
"<br/>\n"
"                Möchten Sie die Vorgänge beschleunigen?"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>A need is created in <b>%s</b> and a rule will be triggered to fulfill "
"it."
msgstr ""
"<br>Ein Bedarf ist erstellt in <b>%s</b> und eine Regel wird ausgelöst, um "
"ihn zu decken."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>If the products are not available in <b>%s</b>, a rule will be triggered"
" to bring the missing quantity in this location."
msgstr ""
"<br>Wenn die Produkte nicht in <b>%s</b> verfügbar sind, wird eine Regel "
"ausgelöst, um die fehlende Menge an diesen Standort zu bringen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>The products will be moved towards <b>%(destination)s</b>, <br/> as "
"specified from <b>%(operation)s</b> destination."
msgstr ""
"<br>Die Produkte werden nach <b>%(destination)s</b> verschoben, <br/> wie im"
" Ziel von <b>%(operation)s</b> festgelegt."

#. module: stock
#: model:mail.template,body_html:stock.mail_template_data_delivery_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"        We are glad to inform you that your order has been shipped.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Your tracking reference is\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Please find your delivery order attached for more details.<br/><br/>\n"
"        Thank you,\n"
"        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hallo <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"       wir freuen uns, Ihnen mitteilen zu können, dass Ihre Bestellung versandt wurde.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Ihre Verfolgungsreferenz ist\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Weitere Einzelheiten entnehmen Sie bitte dem beigefügten Lieferschein.<br/><br/>\n"
"        Vielen Dank\n"
"        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                All products could not be reserved. Click on the \"Check Availability\" button to try to reserve products."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                Es konnten nicht alle Produkte reserviert werden. Klicken Sie auf die Schaltfläche „Verfügbarkeit prüfen“, um zu versuchen, Produkte zu reservieren."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Back Orders</span>"
msgstr "<span class=\"col-6\">Lieferrückstände</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Late</span>"
msgstr "<span class=\"col-6\">Verspätet</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Operations</span>"
msgstr "<span class=\"col-6\">Vorgänge</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Waiting</span>"
msgstr "<span class=\"col-6\">Wartend</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Scannable Package Contents</span>"
msgstr "<span class=\"o_form_label\">Scanbare Paketinhalte</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Allocation</span>"
msgstr "<span class=\"o_stat_text\">Zuteilung</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Forecasted</span>"
msgstr "<span class=\"o_stat_text\">Prognostiziert</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">In:</span>"
msgstr "<span class=\"o_stat_text\">Eingang:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Location</span>"
msgstr "<span class=\"o_stat_text\">Standort</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "<span class=\"o_stat_text\">Lot/Serial Numbers</span>"
msgstr "<span class=\"o_stat_text\">Los-/Seriennummern</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Max:</span>"
msgstr "<span class=\"o_stat_text\">Max:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Min:</span>"
msgstr "<span class=\"o_stat_text\">Min:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Moves</span>"
msgstr "<span class=\"o_stat_text\">Bewegungen</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Next Transfer</span>"
msgstr "<span class=\"o_stat_text\">Nächster Transfer</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">On Hand</span>"
msgstr "<span class=\"o_stat_text\">Vorrätig</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Operations</span>"
msgstr "<span class=\"o_stat_text\">Vorgänge</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Out:</span>"
msgstr "<span class=\"o_stat_text\">Ausgang:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "<span class=\"o_stat_text\">Product Moves</span>"
msgstr "<span class=\"o_stat_text\">Produktbewegungen</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Putaway Rules</span>"
msgstr "<span class=\"o_stat_text\">Einlagerungsregeln</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "<span class=\"o_stat_text\">Routes</span>"
msgstr "<span class=\"o_stat_text\">Routen</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Storage Capacities</span>"
msgstr "<span class=\"o_stat_text\">Lagerkapazitäten</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Traceability</span>"
msgstr "<span class=\"o_stat_text\">Rückverfolgbarkeit</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Delivery Address:</strong></span>"
msgstr "<span><strong>Lieferadresse:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "<span>OBTRETU</span>"
msgstr "<span>OBTRETU</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_view_kanban_catalog
msgid "<span>On Hand: </span>"
msgstr "<span>Vorrätig: </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "<span>Package Type: </span>"
msgstr "<span>Pakettyp: </span> "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_delivery_no_package_section_line
msgid "<span>Products with no package assigned</span>"
msgstr "<span>Produkte ohne Paket</span> "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<span>Remaining quantities not yet delivered:</span>"
msgstr "<span>Verbleibende Menge noch nicht geliefert:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "<span>days</span>"
msgstr "<span>Tage</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid ""
"<strong>\n"
"                The done move line has been corrected.\n"
"            </strong>"
msgstr ""
"<strong>\n"
"                Die Zeile für die getätigte Buchung wurde korrigiert.\n"
"            </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Customer Address</strong>"
msgstr "<strong>Kundenadresse</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Delivered</strong>"
msgstr "<strong>Geliefert</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Delivery address</strong>"
msgstr "<strong>Lieferadresse</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"<strong>Due to some stock moves done between your initial update of the "
"quantity and now, the difference of quantity is not consistent "
"anymore.</strong>"
msgstr ""
"<strong>Aufgrund einiger Lagerbuchungen, die seit Ihrer ursprünglichen "
"Aktualisierung der Menge stattgefunden haben, ist die Differenz der Menge "
"nicht mehr konsistent.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>From</strong>"
msgstr "<strong>Von</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Lot/Serial Number</strong>"
msgstr "<strong>Los-/Seriennummer</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Max qty:</strong>"
msgstr "<strong>Max. Menge:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Min qty:</strong>"
msgstr "<strong>Min. Menge</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Order</strong>"
msgstr "<strong>Auftrag</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Ordered</strong>"
msgstr "<strong>Bestellt</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Pack Date:</strong>"
msgstr "<strong>Verpackungsdatum:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Package Type:</strong>"
msgstr "<strong>Pakettyp:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product Barcode</strong>"
msgstr "<strong>Produktbarcode</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product</strong>"
msgstr "<strong>Produkt</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Quantity</strong>"
msgstr "<strong>Menge</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Recipient address</strong>"
msgstr "<strong>Empfängeradresse</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Scheduled Date</strong>"
msgstr "<strong>Geplantes Datum</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Shipping Date</strong>"
msgstr "<strong>Versanddatum</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Signature</strong>"
msgstr "<strong>Unterschrift</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Status</strong>"
msgstr "<strong>Status</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid "<strong>The initial demand has been updated.</strong>"
msgstr "<strong>Die Anfangsnachfrage wurde aktualisiert.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>To</strong>"
msgstr "<strong>Nach</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "<strong>Tracked product(s):</strong>"
msgstr "<strong>Verfolgte Produkte:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Vendor Address</strong>"
msgstr "<strong>Adresse des Lieferanten</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Warehouse Address</strong>"
msgstr "<strong>Lagerhausadresse</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Warehouse address</strong>"
msgstr "<strong>Lagerhausadresse</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid "<strong>Where do you want to send the products?</strong>"
msgstr "<strong>Wo möchten Sie diese Produkte versenden?</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr " melden möchten? Dies kann zu Inkonsistenzen in Ihrem Bestand führen."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_barcode_uniq
msgid "A barcode can only be assigned to one package type!"
msgstr "Ein Barcode darf nur genau einem Pakettyp zugeordnet sein!"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_product_location_check
msgid "A replenishment rule already exists for this product on this location."
msgstr ""
"Für dieses Produkt existiert bereits eine Auffüllregel an diesem Standort."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__is_storable
#: model:ir.model.fields,help:stock.field_product_template__is_storable
#: model:ir.model.fields,help:stock.field_stock_move__is_storable
msgid "A storable product is a product for which you manage stock."
msgstr ""
"Ein lagerfähiges Produkt ist ein Produkt, für das Sie den Lagerbestand "
"verwalten."

#. module: stock
#: model:res.groups,name:stock.group_warning_stock
msgid "A warning can be set on a partner (Stock)"
msgstr "Eine Warnung kann auf einen Partner gesetzt werden (Lager)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__action
msgid "Action"
msgstr "Aktion"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction
msgid "Action Needed"
msgstr "Aktion notwendig"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__replenish_location
msgid ""
"Activate this function to get all quantities to replenish at this particular"
" location"
msgstr ""
"Aktivieren Sie diese Funktion, um alle Mengen an diesem bestimmten Standort "
"aufzufüllen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__active
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__active
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_route__active
#: model:ir.model.fields,field_description:stock.field_stock_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__active
msgid "Active"
msgstr "Aktiv"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_ids
msgid "Activities"
msgstr "Aktivitäten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_exception_decoration
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitätsausnahme-Dekoration"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_state
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_state
msgid "Activity State"
msgstr "Status der Aktivität"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_type_icon
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_icon
msgid "Activity Type Icon"
msgstr "Symbol des Aktivitätstyps"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_view_activity
msgid "Activity view"
msgstr "Aktivitätsansicht"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Add a Product"
msgstr "Produkt hinzufügen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_production_lot_form
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid "Add a lot/serial number"
msgstr "Los-/Seriennummer hinzufügen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid "Add a new location"
msgstr "Neuen Lagerort hinzufügen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid "Add a new route"
msgstr "Neue Route hinzufügen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_storage_category
msgid "Add a new storage category"
msgstr "Eine neue Lagerkategorie hinzufügen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"Add an internal note that will be printed on the Picking Operations sheet"
msgstr ""
"Fügen Sie eine interne Notiz hinzu, die auf dem Kommissioniervorgang "
"ausgedruckt wird"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_adv_location
msgid ""
"Add and customize route operations to process product moves in your warehouse(s): e.g. unload > quality control > stock for incoming products, pick > pack > ship for outgoing products. \n"
" You can also set putaway strategies on warehouse locations in order to send incoming products into specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"Fügen Sie Routenvorgänge hinzu und passen Sie sie an, um Produktbewegungen in Ihrem Lagerhaus zu verarbeiten: z. B. Ausladen > Qualitätskontrolle > Bestand für eingehende Produkte, Kommissionieren > Verpacken > Versenden für ausgehende Produkte. \n"
" Sie können auch Einlagerungsstrategien für Lagerorte festlegen, um eingehende Produkte direkt an bestimmte untergeordnete Lagerorte zu senden (z. B. in spezielle Schränke und Staufächer)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Add and customize route operations to process product moves in your "
"warehouse(s): e.g. unload > quality control > stock for incoming products, "
"pick > pack > ship for outgoing products. You can also set putaway "
"strategies on warehouse locations in order to send incoming products into "
"specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"Fügen Sie Routenvorgänge hinzu und passen Sie sie an, um Produktbewegungen "
"in Ihrem Lagerhaus zu verarbeiten: z. B. Ausladen > Qualitätskontrolle > "
"Bestand für eingehende Produkte, Kommissionieren > Verpacken > Versenden für"
" ausgehende Produkte. Sie können auch Einlagerungsstrategien für Lagerorte "
"festlegen, um eingehende Produkte direkt an bestimmte untergeordnete "
"Lagerorte zu senden (z. B. in spezielle Schränke und Staufächer)."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/fields/stock_move_line_x2_many_field.js:0
msgid "Add line: %s"
msgstr "Zeile hinzufügen: %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Add quality checks to your transfer operations"
msgstr "Ergänzen Sie Ihre Transfervorgänge um Qualitätsprüfungen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Additional Info"
msgstr "Zusätzliche Infos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__comment
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Additional Information"
msgstr "Zusätzliche Informationen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__address
#: model:ir.model.fields,field_description:stock.field_stock_picking__warehouse_address_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__partner_id
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Address"
msgstr "Adresse"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__partner_address_id
msgid "Address where goods should be delivered. Optional."
msgstr "Adresse, an die Waren geliefert werden. Optional."

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_adjustments
msgid "Adjustments"
msgstr "Anpassungen"

#. module: stock
#: model:res.groups,name:stock.group_stock_manager
msgid "Administrator"
msgstr "Administrator"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Advanced Scheduling"
msgstr "Erweiterte Planung"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_order
msgid "Advanced: Apply Procurement Rules"
msgstr "Erweitert: Beschaffungsregeln anwenden"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__after
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "After"
msgstr "Nach"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "All"
msgstr "Alle"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "All Categories"
msgstr "Alle Kategorien"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "All Companies"
msgstr "Alle Unternehmen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "All Products"
msgstr "Alle Produkte"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_action_picking_type
msgid "All Transfers"
msgstr "Alle Transfers"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_report_search_panel.xml:0
msgid "All Warehouses"
msgstr "Alle Lagerhäuser"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__one
msgid "All at once"
msgstr "Alle auf einmal"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"All our contractual relations will be governed exclusively by United States "
"law."
msgstr ""
"Für alle unsere vertraglichen Beziehungen gilt ausschließlich das Recht der "
"Vereinigten Staaten."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__returned_move_ids
msgid "All returned moves"
msgstr "Alle Retouren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__allow_new_product
msgid "Allow New Product"
msgstr "Neues Produkt erlauben"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__mixed
msgid "Allow mixed products"
msgstr "Gemischte Produkte erlauben"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__allowed_location_ids
msgid "Allowed Location"
msgstr "Erlaubter Lagerort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__allowed_route_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenish_mixin__allowed_route_ids
msgid "Allowed Route"
msgstr "Erlaubte Route"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__always
msgid "Always"
msgstr "Immer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Andrwep"
msgstr "Andrwep"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Annual Inventory Day and Month"
msgstr "Datum der jährlichen Inventur"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_month
msgid "Annual Inventory Month"
msgstr "Monat der jährlichen Inventur"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_month
msgid ""
"Annual inventory month for products not in a location with a cyclic "
"inventory date. Set to no month if no automatic annual inventory."
msgstr ""
"Monat der jährlichen Inventur für Produkte, die sich nicht an einem Standort"
" mit einem zyklischen Inventurdatum befinden. Auf keinen Monat setzen, wenn "
"keine automatische jährliche Inventur erfolgt."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"Another parent/sub replenish location %s exists, if you wish to change it, "
"uncheck it first"
msgstr ""
"Ein anderer übergeordneter/untergeordneter Auffüllstandort %s existiert, "
"wenn Sie ihn ändern möchten, deaktivieren Sie ihn zunächst."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Any Category"
msgstr "Jede Kategorie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Applicability"
msgstr "Anwendbarkeit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Applicable On"
msgstr "Anwendbar auf"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__packaging_selectable
msgid "Applicable on Packaging"
msgstr "Bei Verpackung auswählbar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__product_selectable
msgid "Applicable on Product"
msgstr "Bei Produkt auswählbar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__product_categ_selectable
msgid "Applicable on Product Category"
msgstr "Bei Produktkategorie auswählbar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_selectable
msgid "Applicable on Warehouse"
msgstr "Bei Lagerhaus auswählbar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Apply"
msgstr "Anwenden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Apply All"
msgstr "Alle anwenden"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__route_id
#: model:ir.model.fields,help:stock.field_stock_replenish_mixin__route_id
msgid ""
"Apply specific route for the replenishment instead of product's default "
"routes."
msgstr ""
"Wenden Sie anstelle der Standardrouten des Produkts bestimmte Routen für die"
" Auffüllung an."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__4
msgid "April"
msgstr "April"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Archived"
msgstr "Archiviert"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Are you sure you want to cancel this transfer?"
msgstr "Sind Sie sicher, dass Sie diesen Transfer stornieren möchten?"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__direct
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__move_type__direct
msgid "As soon as possible"
msgstr "Sobald wie möglich"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__ask
msgid "Ask"
msgstr "Fragen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Assign"
msgstr "Zuweisen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
msgid "Assign All"
msgstr "Alle zuweisen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__owner_id
msgid "Assign Owner"
msgstr "Eigentümer zuweisen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Assigned Moves"
msgstr "Zugewiesene Buchungen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__user_id
msgid "Assigned To"
msgstr "Zugewiesen an"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__at_confirm
msgid "At Confirmation"
msgstr "Bei Bestätigung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "At Customer"
msgstr "Beim Kunden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Attributes"
msgstr "Attribute"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__8
msgid "August"
msgstr "August"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__auto
msgid "Auto"
msgstr "Automatisch"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_delivery_slip
msgid "Auto Print Delivery Slip"
msgstr "Lieferschein automatisch drucken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_lot_labels
msgid "Auto Print Lot/SN Labels"
msgstr "Los-/SN-Etiketten automatisch drucken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_package_label
msgid "Auto Print Package Label"
msgstr "Paketetikett automatisch drucken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_packages
msgid "Auto Print Packages"
msgstr "Pakete automatisch drucken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_product_labels
msgid "Auto Print Product Labels"
msgstr "Produktetiketten automatisch drucken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_reception_report
msgid "Auto Print Reception Report"
msgstr "Empfangsbericht automatisch drucken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_reception_report_labels
msgid "Auto Print Reception Report Labels"
msgstr "Empfangsberichtsetiketten automatisch drucken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_return_slip
msgid "Auto Print Return Slip"
msgstr "Retourenschein automatisch drucken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Automate"
msgstr "Automatisieren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__auto
msgid "Automatic Move"
msgstr "Automatische Buchung"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__transparent
msgid "Automatic No Step Added"
msgstr "Automatisch ohne Folgeschritt"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__assigned
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "Available"
msgstr "Verfügbar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Available Products"
msgstr "Verfügbare Produkte"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__available_quantity
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Available Quantity"
msgstr "Verfügbare Menge"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"Available quantity should be set to zero before changing inventory tracking"
msgstr ""
"Bevor die Bestandsverfolgung geändert wird, sollte die verfügbare Menge 0 "
"sein"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_id
msgid "Back Order of"
msgstr "Lieferrückstand von"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_ids
msgid "Back Orders"
msgstr "Lieferrückstände"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "Rückstandsbestätigung"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation_line
msgid "Backorder Confirmation Line"
msgstr "Rückstandsbestätigungszeile"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__backorder_confirmation_line_ids
msgid "Backorder Confirmation Lines"
msgstr "Rückstandsbestätigungszeilen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Backorder creation"
msgstr "Rückstandserstellung"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Backorders"
msgstr "Lieferrückstände"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__barcode
#: model:ir.model.fields,field_description:stock.field_stock_package_type__barcode
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Barcode"
msgstr "Barcode"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Barcode Demo"
msgstr "Barcode-Demo"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_wms_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr "Barcode-Nomenklatur"

#. module: stock
#: model:ir.model,name:stock.model_barcode_rule
msgid "Barcode Rule"
msgstr "Barcoderegel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode
msgid "Barcode Scanner"
msgstr "Barcode-Scanner"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__valid_ean
msgid "Barcode is valid EAN"
msgstr "Barcode ist eine gültige EAN"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_picking_batch
msgid "Batch, Wave & Cluster Transfers"
msgstr "Stapel-, Wellen- & Cluster-Transfers"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__before
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Before"
msgstr "Vor"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__by_date
msgid "Before scheduled date"
msgstr "Vor dem geplanten Datum"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Below text serves as a suggestion and doesn’t engage Odoo S.A. "
"responsibility."
msgstr ""
"Der nachfolgende Text dient als Anregung und liegt nicht in der "
"Verantwortung von Odoo S.A."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__block
msgid "Blocking Message"
msgstr "Blockierende Meldung"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Blocking: %s"
msgstr "Blockiert: %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__quant_ids
msgid "Bulk Content"
msgstr "Masseninhalt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__weight_bulk
msgid "Bulk Weight"
msgstr "Schüttgewicht"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__lot
msgid "By Lots"
msgstr "Mittels Los"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__none
msgid "By Quantity"
msgstr "Nach Menge"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__serial
msgid "By Unique Serial Number"
msgstr "Mittels einzigartiger Seriennummer"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__procure_method
msgid ""
"By default, the system will take from the stock in the source location and "
"passively wait for availability. The other possibility allows you to "
"directly create a procurement on the source location (and thus ignore its "
"current stock) to gather products. If we want to chain moves and have this "
"one to wait for the previous, this second option should be chosen."
msgstr ""
"Im Standard reduziert das System den Bestand am Quellstandort und wartet "
"passiv die Verfügbarkeit ab. Optional kann eine direkte Beschaffung beim "
"Quellstandort ausgelöst werden, wodurch die tatsächliche Verfügbarkeit "
"ignoriert werden kann. Im Falle einer Verkettung von Bewegungen, bei der "
"eine Verfügbarkeit auch durch die vorherige Bewegung bestimmt wird, sollte "
"diese alternative Möglichkeit ausgewählt werden."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__active
msgid ""
"By unchecking the active field, you may hide a location without deleting it."
msgstr ""
"Ein Lagerort kann verborgen werden, ohne diesen zu löschen, indem das aktive"
" Feld deaktiviert wird."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "COPY"
msgstr "KOPIEREN"

#. module: stock
#: model:product.template,name:stock.product_cable_management_box_product_template
msgid "Cable Management Box"
msgstr "Kabelverwaltungsbox"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_calendar
msgid "Calendar View"
msgstr "Kalenderansicht"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Can't find any customer or supplier location."
msgstr ""
"Ein definierter Lagerort für Kunden oder Lieferanten kann nicht gefunden "
"werden."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Can't find any generic route %s."
msgstr "Es kann keine allgemein gültige Beschaffungsroute %s gefunden werden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.lot_label_layout_form_picking
#: model_terms:ir.ui.view,arch_db:stock.picking_label_type_form
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_warning_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Cancel"
msgstr "Abbrechen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_cancel
msgid "Cancel Next Move"
msgstr "Nächste Bewegung abbrechen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__cancel
msgid "Cancelled"
msgstr "Abgebrochen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Cannot move an empty package"
msgstr "Ein leeres Paket kann nicht bewegt werden"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Cannot remove the location of a non empty package"
msgstr "Der Lagerort eines nicht leeren Pakets kann nicht entfernt werden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__capacity_ids
msgid "Capacity"
msgstr "Kapazität"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Package"
msgstr "Kapazität nach Paket"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Product"
msgstr "Kapazität nach Produkt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_category_id
#: model_terms:ir.ui.view,arch_db:stock.product_search_form_view_stock_report
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Category"
msgstr "Kategorie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__route_from_categ_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_from_categ_ids
msgid "Category Routes"
msgstr "Routenkategorie"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Certain countries apply withholding at source on the amount of invoices, in "
"accordance with their internal legislation. Any withholding at source will "
"be paid by the client to the tax authorities. Under no circumstances can My "
"Company (Chicago) become involved in costs related to a country's "
"legislation. The amount of the invoice will therefore be due to My Company "
"(Chicago) in its entirety and does not include any costs relating to the "
"legislation of the country in which the client is located."
msgstr ""
"In einigen Ländern wird der Rechnungsbetrag gemäß den dortigen "
"Rechtsvorschriften an der Quelle einbehalten. Jede Quellensteuer wird vom "
"Kunden an die Steuerbehörden abgeführt. My Company (Chicago) kann sich unter"
" keinen Umständen an den Kosten im Zusammenhang mit der Gesetzgebung eines "
"Landes beteiligen. Der Rechnungsbetrag geht daher vollständig an My Company "
"(Chicago) und enthält keine Kosten, die mit der Gesetzgebung des Landes, in "
"dem der Kunde ansässig ist, zusammenhängen."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_change_product_quantity
#: model:ir.model,name:stock.model_stock_change_product_qty
msgid "Change Product Quantity"
msgstr "Produktmenge ändern"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"Changing the Lot/Serial number for move lines with different products is not"
" allowed."
msgstr ""
"Es ist nicht erlaubt, die Los-/Seriennummer für Buchungszeilen mit "
"unterschiedlichen Produkten zu ändern."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_orderpoint.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"Changing the company of this record is forbidden at this point, you should "
"rather archive it and create a new one."
msgstr ""
"Eine Änderung des Unternehmens dieses Datensatzes ist an dieser Stelle "
"verboten, Sie sollten ihn lieber archivieren und einen neuen erstellen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Changing the operation type of this record is forbidden at this point."
msgstr ""
"Die Änderung der Vorgangsart dieses Datensatzes ist an dieser Stelle "
"verboten."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "Changing the product is only allowed in 'Draft' state."
msgstr "Die Änderung des Produkts ist nur im Status „Entwurf“ erlaubt."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__barcode_separator
msgid ""
"Character(s) used to separate data contained within an aggregate barcode "
"(i.e. a barcode containing multiple barcode encodings)"
msgstr ""
"Verwendete Zeichen, um Daten in einem aggregierten Barcode zu trennen (d. h."
" ein Barcode mit mehreren Barcode-Codierungen)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Check Availability"
msgstr "Verfügbarkeit prüfen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_packages
msgid "Check the existence of destination packages on move lines"
msgstr "Überprüfen Sie die Existenz von Zielpaketen in Buchungszeilen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_line_exist
msgid "Check the existence of pack operation on the picking"
msgstr ""
"Überprüfen Sie die Existenz eines Verpackungsvorgangs für diese "
"Kommissionierung"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__print_label
msgid ""
"Check this box if you want to generate shipping label in this operation."
msgstr ""
"Kreuzen Sie dieses Kästchen an, wenn Sie Versandetiketten in diesem Vorgang "
"generieren möchten."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__scrap_location
#: model:ir.model.fields,help:stock.field_stock_move__scrapped
msgid ""
"Check this box to allow using this location to put scrapped/damaged goods."
msgstr ""
"Aktivieren Sie diese Option, um diesen Lagerort für Ausschuss/beschädigte "
"Produkte einzusetzen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/wizard/stock_label_type.py:0
msgid "Choose Labels Layout"
msgstr "Etikettenlayout auswählen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Choose Type of Labels To Print"
msgstr "Wählen Sie den zu druckenden Etikettentyp"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quantity_history__inventory_datetime
#: model:ir.model.fields,help:stock.field_stock_request_count__inventory_date
msgid "Choose a date to get the inventory at that date"
msgstr "Wählen Sie ein Datum, um den Bestand an diesem Tag zu erhalten."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Choose destination location"
msgstr "Zielstandort wählen"

#. module: stock
#: model:ir.model,name:stock.model_lot_label_layout
msgid "Choose the sheet layout to print lot labels"
msgstr "Wählen Sie das Layout zum Drucken von Losetiketten"

#. module: stock
#: model:ir.model,name:stock.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr "Wählen Sie das Seitenlayout für den Druck der Etiketten"

#. module: stock
#: model:ir.model,name:stock.model_picking_label_type
msgid "Choose whether to print product or lot/sn labels"
msgstr "Wählen Sie, ob Produkt- oder Los-/SN-Etiketten gedruckt werden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Choose your date"
msgstr "Wählen Sie ein Datum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Clear"
msgstr "Leeren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
msgid "Close"
msgstr "Schließen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__closest_location
#: model:product.removal,name:stock.removal_closest
msgid "Closest Location"
msgstr "Nächstgelegener Standort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__color
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__color
msgid "Color"
msgstr "Farbe"

#. module: stock
#: model:ir.model,name:stock.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__company_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location__company_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__company_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_route__company_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__company_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Company"
msgstr "Unternehmen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs"
msgstr "Berechnen Sie die Versandkosten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Versandkosten für DHL berechnen und versenden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Compute shipping costs and ship with DHL<br/>\n"
"                                    <strong>(please go to Home&gt;Apps to install)</strong>"
msgstr ""
"Berechnen Sie die Versandkosten und versenden Sie mit DHL<br/>\n"
"                                    <strong>(gehen Sie zur Installation zu Start&gt;Apps)</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "Versandkosten für Easypost berechnen und versenden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Versandkosten für FedEx berechnen und versenden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Compute shipping costs and ship with FedEx<br/>\n"
"                                    <strong>(please go to Home&gt;Apps to install)</strong>"
msgstr ""
"Berechnen Sie die Versandkosten und versenden Sie mit FedEx<br/>\n"
"                                    <strong>(gehen Sie zur Installation zu Start&gt;Apps)</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Sendcloud"
msgstr "Versandkosten für Sendcloud berechnen und versenden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Shiprocket"
msgstr "Versandkosten für Shiprocket berechnen und versenden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Starshipit"
msgstr "Versandkosten für Starshipit berechnen und versenden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Versandkosten für UPS berechnen und versenden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Compute shipping costs and ship with UPS<br/>\n"
"                                    <strong>(please go to Home&gt;Apps to install)</strong>"
msgstr ""
"Berechnen Sie die Versandkosten und versenden Sie mit UPS<br/>\n"
"                                    <strong>(gehen Sie zur Installation zu Start&gt;Apps)</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Versandkosten für USPS berechnen und versenden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Compute shipping costs and ship with USPS<br/>\n"
"                                    <strong>(please go to Home&gt;Apps to install)</strong>"
msgstr ""
"Berechnen Sie die Versandkosten und versenden Sie mit USPS<br/>\n"
"                                    <strong>(gehen Sie zur Installation zu Start&gt;Apps)</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Versandkosten für bpost berechnen und versenden"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reservation_date
msgid "Computes when a move should be reserved"
msgstr "Berechnet, wenn eine Buchung reserviert werden sollte"

#. module: stock
#: model:ir.model,name:stock.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_config_settings
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Configuration"
msgstr "Konfiguration"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.lot_label_layout_form_picking
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.picking_label_type_form
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Confirm"
msgstr "Bestätigen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__confirmed
msgid "Confirmed"
msgstr "Bestätigt"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_conflict
msgid "Conflict in Inventory"
msgstr "Konflikt im Inventar"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Conflict in Inventory Adjustment"
msgstr "Konflikt bei der Bestandsaufnahme"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_to_fix_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Conflicts"
msgstr "Konflikte"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__visibility_days
msgid ""
"Consider product forecast these many days in the future upon product replenishment, set to 0 for just-in-time.\n"
"The value depends on the type of the route (Buy or Manufacture)"
msgstr ""
"Berücksichtigen Sie die Produktprognose, die diese vielen Tage in der Zukunft liegt, wenn das Produkt aufgefüllt wird, und setzen Sie den Wert auf 0 für just in time.\n"
"Der Wert hängt von der Art der Route ab (Kaufen oder Fertigen)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_owner
msgid "Consignment"
msgstr "Konsignation"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__consume_line_ids
msgid "Consume Line"
msgstr "Verbrauchposition"

#. module: stock
#: model:ir.model,name:stock.model_res_partner
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Contact"
msgstr "Kontakt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_ids
msgid "Contains"
msgstr "Beinhaltet"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Content"
msgstr "Inhalt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Contents"
msgstr "Inhalte"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Continue"
msgstr "Fortfahren"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
msgid "Control panel buttons"
msgstr "Schaltflächen des Bedienfelds"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_scrap__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Die Umrechnung zwischen Maßeinheiten kann nur erfolgen, wenn sie derselben "
"Kategorie angehören. Die Umrechnung erfolgt auf Basis der Kennzahlen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posx
msgid "Corridor (X)"
msgstr "Lagergang (X)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__set_count
msgid "Count"
msgstr "Anzahl"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_move_ready
msgid "Count Move Ready"
msgstr "Anzahl Bewegungen Bereit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking
msgid "Count Picking"
msgstr "Anzahl Kommissionierungen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_backorders
msgid "Count Picking Backorders"
msgstr "Anzahl Kommissionierungen mit Lieferrückständen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_draft
msgid "Count Picking Draft"
msgstr "Anzahl Kommissionierungen im Entwurf"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_late
msgid "Count Picking Late"
msgstr "Anzahl verspäteter Kommissionierungen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_ready
msgid "Count Picking Ready"
msgstr "Anzahl Kommissionierungen Bereit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_waiting
msgid "Count Picking Waiting"
msgstr "Anzahl wartender Kommissionierungen"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_inventory
msgid "Count Sheet"
msgstr "Zählbogen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Counted Quantity"
msgstr "Gezählte Menge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Counterpart Locations"
msgstr "Gegenlagerorte"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Create Backorder"
msgstr "Lieferrückstand erstellen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Create Backorder?"
msgstr "Lieferrückstand erstellen?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Create New"
msgstr "Neue erstellen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_create_lots
msgid "Create New Lots/Serial Numbers"
msgstr "Neue Los-/Seriennummer erstellen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Create Stock"
msgstr "Bestand erstellen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid ""
"Create a backorder if you expect to process the remaining\n"
"                            products later. Do not create a backorder if you will not\n"
"                            process the remaining products."
msgstr ""
"Erstellen Sie einen Lieferrückstand, wenn Sie erwarten, die restlichen\n"
"                            Produkte später zu verarbeiten. Erstellen Sie keinen Lieferrückstand, wenn Sie die\n"
"                            restlichen Produkte nicht mehr verarbeiten."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid "Create a new operation type"
msgstr "Eine neue Vorgangsart erstellen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid "Create a new package"
msgstr "Neues Paket erstellen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr ""
"Erstellen Sie benutzerdefinierte Arbeitsblätter für Ihre Qualitätsprüfungen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid ""
"Create new putaway rules to dispatch automatically specific products to "
"their appropriate destination location upon receptions."
msgstr ""
"Erstellen Sie neue Einlagerungsregeln, um bestimmte Produkte bei der Annahme"
" automatisch an den entsprechenden Zielstandort zu versenden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create products easily by scanning using"
msgstr "Erstellen Sie Produkte einfach durch Scan unter Verwendung von"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_stock_view
msgid "Create some storable products to see their stock info in this view."
msgstr ""
"Erstellen Sie einige lagerfähige Produkte, um deren Bestandsinformationen in"
" dieser Ansicht zu sehen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__create_uid
#: model:ir.model.fields,field_description:stock.field_picking_label_type__create_uid
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__create_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_lot__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_route__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__create_date
#: model:ir.model.fields,field_description:stock.field_picking_label_type__create_date
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_date
#: model:ir.model.fields,field_description:stock.field_product_removal__create_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location__create_date
#: model:ir.model.fields,field_description:stock.field_stock_lot__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__create_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_route__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"Creating a new warehouse will automatically activate the Storage Locations "
"setting"
msgstr ""
"Beim Anlegen eines neuen Lagerhauses wird die Einstellung Lagerorte "
"automatisch aktiviert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Creation Date"
msgstr "Erstellungsdatum"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date
msgid "Creation Date, usually the time of the order"
msgstr "Erstellungsdatum, vermutlich das Datum des Auftrags"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Creation date"
msgstr "Erstellungsdatum"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__date
msgid ""
"Creation date of this move line until updated due to: quantity being "
"increased, 'picked' status has updated, or move line is done."
msgstr ""
"Erstelldatum dieser Buchungszeile bis Aktualisierung aus folgendem Grund: "
"Menge wurde erhöht, „kommissionierter“ Status wurde aktualisiert oder "
"Buchungszeile ist erledigt."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Cross Dock"
msgstr "Kreuzverkupplung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__xdock_type_id
msgid "Cross Dock Type"
msgstr "Kreuzverkupplungstyp"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Cross-Dock"
msgstr "Kreuzverkupplung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__crossdock_route_id
msgid "Crossdock Route"
msgstr "Kreuzverkupplungsroute"

#. module: stock
#: model:ir.actions.act_window,name:stock.location_open_quants
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Current Stock"
msgstr "Aktueller Bestand"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Aktuell verfügbare Anzahl für Produkte.\n"
"Im Kontext mit einem einzigen Lagerort beinhaltet dies den Bestand dieses oder eines untergeordneten Lagerorts.\n"
"Im Kontext mit einem einzigen Lagerhaus, beinhaltet dies den Bestand an dem Lagerort in diesem Lagerhaus, oder den \n"
"untergeordneten Lagerorten. \n"
"gelagert am Lagerort im Lagerhaus dieses Shops oder an den \n"
"untergeordneten Lagerorten.\n"
"Andernfalls ergibt sich die Menge aus den Beständen aller Lagerorte des Typs „Intern“."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__move_quantity__custom
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__custom
msgid "Custom"
msgstr "Benutzerdefiniert"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer"
msgstr "Kunde"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__sale_delay
#: model:ir.model.fields,field_description:stock.field_product_template__sale_delay
msgid "Customer Lead Time"
msgstr "Kundenvorlaufzeit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_customer
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__customer
msgid "Customer Location"
msgstr "Kundenstandort"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer Locations"
msgstr "Kundenstandorte"

#. module: stock
#: model:ir.model,name:stock.model_stock_lot_report
msgid "Customer Lot Report"
msgstr "Bericht zu Kundenlosen"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lot_report
msgid "Customer lots"
msgstr "Kundenlose"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Customizable Desk"
msgstr "Anpassbarer Schreibtisch"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Cyclic Counting"
msgstr "Regelmäßige Zählung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_DATE"
msgstr "DEMO_DATE"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_ORIGIN_DISPLAY_NAME"
msgstr "DEMO_ORIGIN_DISPLAY_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_PARTNER_NAME"
msgstr "DEMO_PARTNER_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_PRODUCT_DISPLAY_NAME"
msgstr "DEMO_PRODUCT_DISPLAY_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_QUANTITY"
msgstr "DEMO_QUANTITY"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_SOURCE_DISPLAY_NAME"
msgstr "DEMO_SOURCE_DISPLAY_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_UOM"
msgstr "DEMO_UOM"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "DHL Connector"
msgstr "DHL-Konnektor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL-Express-Konnektor"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__date_done
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Date"
msgstr "Datum"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__search_date_category
msgid "Date Category"
msgstr "Datumskategorie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Processing"
msgstr "Datumsverarbeitung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Scheduled"
msgstr "Geplantes Datum"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__date_planned
msgid "Date at which the replenishment should take place."
msgstr "Datum, an dem die Auffüllung stattfinden sollte."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date_done
msgid "Date at which the transfer has been processed or cancelled."
msgstr "Datum, an dem der Transfer verarbeitet oder abgebrochen wurde."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__next_inventory_date
msgid "Date for next planned inventory based on cyclic schedule."
msgstr ""
"Datum für die nächste geplante Inventur auf der Grundlage des zyklischen "
"Zeitplans."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_done
msgid "Date of Transfer"
msgstr "Datum des Transfers"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__last_inventory_date
msgid "Date of the last inventory at this location."
msgstr "Datum der letzten Inventur an diesem Standort."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reservation_date
msgid "Date to Reserve"
msgstr "Datum zum Reservieren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Day and month that annual inventory counts should occur."
msgstr "Tag und Monat, an dem die jährliche Inventur stattfinden soll."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_day
msgid "Day of the month"
msgstr "Tag des Monats"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_day
msgid ""
"Day of the month when the annual inventory should occur. If zero or negative, then the first day of the month will be selected instead.\n"
"        If greater than the last day of a month, then the last day of the month will be selected instead."
msgstr ""
"Tag des Monats, an dem die jährliche Inventur stattfinden soll. Ist der Wert Null oder negativ, wird stattdessen der erste Tag des Monats gewählt.\n"
"Ist der Wert höher als der letzte Tag eines Monats, wird stattdessen der letzte Tag des Monats gewählt."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Day(s)"
msgstr "Tag(e)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before
msgid "Days"
msgstr "Tage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__days_to_order
msgid "Days To Order"
msgstr "Tage zum Bestellen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before_priority
msgid "Days when starred"
msgstr "Tage falls mit Sternchen versehen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_deadline
msgid "Deadline"
msgstr "Frist"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deadline exceed or/and by the scheduled"
msgstr "Frist überschreiten oder/und bis zum geplanten Datum"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Deadline updated due to delay on %s"
msgstr "Frist aktualisiert aufgrund der Verzögerung bei %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__12
msgid "December"
msgstr "Dezember"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Barcode Name"
msgstr "Standard-Barcodename"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Name"
msgstr "Standardname"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default OBTRETU Barcode"
msgstr "Standard-OBTRETU-Barcode"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Return Name"
msgstr "Standardretourenname"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__reception_steps
msgid "Default incoming route to follow"
msgstr "Standardeingangsroute"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__delivery_steps
msgid "Default outgoing route to follow"
msgstr "Standardausgangsroute"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__forecast_uom_id
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_lot__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,help:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_uom
msgid "Default unit of measure used for all stock operations."
msgstr "Standardmaßeinheit, die für alle Lagervorgänge verwendet wird."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_stock
msgid "Default: Take From Stock"
msgstr "Standard: Lagerentnahme"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__route_ids
msgid "Defaults routes through the warehouse"
msgstr "Standardrouten im Lagerhaus"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid ""
"Define a minimum stock rule so that Odoo automatically creates requests for "
"quotations or confirmed manufacturing orders to resupply your stock."
msgstr ""
"Definieren Sie eine Mindestbestandsregel, damit Odoo automatisch "
"Angebotsanfragen oder bestätigte Fertigungsaufträge erstellt, um Ihren "
"Bestand wieder aufzufüllen."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_warehouse_form
msgid "Define a new warehouse"
msgstr "Neues Lagerhaus erstellen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Define your locations to reflect your warehouse structure and\n"
"            organization. Odoo is able to manage physical locations\n"
"            (warehouses, shelves, bin, etc), partner locations (customers,\n"
"            vendors) and virtual locations which are the counterpart of\n"
"            the stock operations like the manufacturing orders\n"
"            consumptions, inventories, etc."
msgstr ""
"Definieren Sie Ihre Standorte, um Ihre Lagerstruktur und\n"
"-organisation zu reflektieren. Odoo ist in der Lage, physische Standorte \n"
"(Lagerhallen, Regale, Abfallsysteme usw.), Partnerlagerorte (Kunden, \n"
"Verkäufer) und virtuelle Standorte zu verwalten, die das Gegenstück \n"
"zu den Vorräten wie der Fertigungsaufträge, Lagerbestände usw. sind."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__removal_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) where to take the products from, which lot etc. for this location. This method can be enforced at the product category level, and a fallback is made on the parent locations if none is set here.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closet location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting)."
msgstr ""
"Definiert die Standardmethode, die verwendet wird, um den genauen Lagerort (Regal) vorzuschlagen, aus dem die Produkte entnommen werden sollen, welches Los usw. für diesen Lagerort. Diese Methode kann auf der Ebene der Produktkategorie erzwungen werden und es wird auf die übergeordneten Lagerorte zurückgegriffen, wenn hier nichts festgelegt ist.\n"
"\n"
"FIFO: Produkte/Lose, die zuerst eingelagert wurden, werden zuerst entnommen.\n"
"LIFO: Produkte/Lose, die zuletzt eingelagert wurden, werden zuerst entnommen.\n"
"Nächstgelegener Lagerort: Die Produkte/Lose, die dem Zielort am nächsten sind, werden zuerst entnommen.\n"
"FEFO: Produkte/Lose mit dem nächstgelegenen Entnahmedatum werden zuerst entnommen (die Verfügbarkeit dieser Methode hängt von der Einstellung „Verfallsdaten“ ab)."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__delay_alert_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__delay_alert_date
msgid "Delay Alert Date"
msgstr "Warndatum bei Verzögerung"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "Delay on %s"
msgstr "Verzögerung bei %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__ship_only
msgid "Deliver (1 step)"
msgstr "Liefern (1 Schritt)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 1 step (ship)"
msgstr "In 1 Schritt liefern (Versand)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 2 steps (pick + ship)"
msgstr "In 2 Schritten liefern (Kommissionierung + Versand)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 3 steps (pick + pack + ship)"
msgstr "In 3 Schritten liefern (Kommissionierung + Verpackung + Versand)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Delivered"
msgstr "Zugestellt"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Delivered Qty"
msgstr "Gelieferte Menge"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_outgoing
#: model:ir.ui.menu,name:stock.out_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deliveries"
msgstr "Auslieferungen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__outgoing
#: model:ir.ui.menu,name:stock.menu_delivery
msgid "Delivery"
msgstr "Lieferung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Delivery Address"
msgstr "Lieferadresse"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__delivery_date
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Delivery Date"
msgstr "Liefertermin"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "Liefermethoden"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.picking_type_out
msgid "Delivery Orders"
msgstr "Lieferaufträge"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_route_id
msgid "Delivery Route"
msgstr "Lieferungsroute"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_delivery
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Delivery Slip"
msgstr "Lieferschein"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__move_type
msgid "Delivery Type"
msgstr "Lieferart"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Delivery date"
msgstr "Lieferdatum"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__sale_delay
#: model:ir.model.fields,help:stock.field_product_template__sale_delay
msgid ""
"Delivery lead time, in days. It's the number of days, promised to the "
"customer, between the confirmation of the sales order and the delivery."
msgstr ""
"Vorlaufzeit für die Lieferung, in Tagen. Es ist die dem Kunden versprochene "
"Anzahl von Tagen zwischen der Bestätigung des Verkaufsauftrags und der "
"Lieferung."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__delivery_count
msgid "Delivery order count"
msgstr "Anzahl Lieferaufträge"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "Delivery orders of %s"
msgstr "Lieferaufträge von %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_qty
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Demand"
msgstr "Bedarf"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Demo Address and Name"
msgstr "Demo-Adresse und Name"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Demo Display Name"
msgstr "Demo-Anzeigename"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "Demo Lot/SN"
msgstr "Demo-Los/SN"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "Demo Product"
msgstr "Demo-Produkt"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_packaging__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product in this packaging: whether it will be bought, manufactured, "
"replenished on order, etc."
msgstr ""
"Abhängig von den installierten Modulen können Sie damit den Weg des Produkts"
" in dieser Verpackung festlegen: ob es gekauft, hergestellt, auf Bestellung "
"aufgefüllt wird usw."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__route_ids
#: model:ir.model.fields,help:stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""
"Abhängig von den installierten Modulen können Sie die Route des Produkts "
"definieren: ob es gekauft, gefertigt, auf Bestellung aufgefüllt wird usw."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__note
#: model:ir.model.fields,field_description:stock.field_stock_move__name
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Description"
msgstr "Beschreibung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Delivery Orders"
msgstr "Beschreibung für Lieferaufträge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Internal Transfers"
msgstr "Beschreibung für interne Transfers"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Receipts"
msgstr "Beschreibung für Wareneingänge"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__description_picking
msgid "Description of Picking"
msgstr "Beschreibung der Kommissionierung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingout
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingout
msgid "Description on Delivery Orders"
msgstr "Beschreibung auf Lieferaufträgen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_picking
#: model:ir.model.fields,field_description:stock.field_product_template__description_picking
msgid "Description on Picking"
msgstr "Beschreibung auf Kommisionierung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingin
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingin
msgid "Description on Receptions"
msgstr "Beschreibung auf Wareneingängen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Description on transfer"
msgstr "Beschreibung auf Transfer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__description_picking
msgid "Description picking"
msgstr "Beschreibung der Kommissionierung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_location_id
msgid "Dest Location"
msgstr "Zielort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_package_id
msgid "Dest Package"
msgstr "Zielpaket"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_package_id_domain
msgid "Dest Package Id Domain"
msgstr "Bereich der Zielpaket-ID"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__partner_id
msgid "Destination Address "
msgstr "Zieladresse "

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Destination Location"
msgstr "Ziellagerort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_usage
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_usage
msgid "Destination Location Type"
msgstr "Art des Lieferzielorts"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Location:"
msgstr "Zielort:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_dest_ids
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Destination Moves"
msgstr "Zielbewegungen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__result_package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Destination Package"
msgstr "Zielpaket"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Package:"
msgstr "Zielpaket:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__location_dest_id
msgid "Destination location"
msgstr "Ziellagerort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_dest_from_rule
msgid "Destination location origin from rule"
msgstr "Herkunft des Ziellagerorts aus Regel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__route_ids
msgid "Destination route"
msgstr "Zielroute"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
msgid "Detailed Operations"
msgstr "Detaillierte Vorgänge"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_details_visible
msgid "Details Visible"
msgstr "Sichtbare Details"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_diff_quantity
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Difference"
msgstr "Differenz"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Discard"
msgstr "Verwerfen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Discard and manually resolve the conflict"
msgstr "Verwerfen und Konflikt manuell lösen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_fleet
msgid "Dispatch Management System"
msgstr "Versandmanagementsystem"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_assign_serial
msgid "Display Assign Serial"
msgstr "„Seriennr. zuordnen“ anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__display_complete
msgid "Display Complete"
msgstr "Vollständig anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_import_lot
msgid "Display Import Lot"
msgstr "Importlos anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_lot_on_delivery_slip
msgid "Display Lots & Serial Numbers on Delivery Slips"
msgstr "Los- & Seriennummern auf Lieferscheinen anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__display_name
#: model:ir.model.fields,field_description:stock.field_picking_label_type__display_name
#: model:ir.model.fields,field_description:stock.field_procurement_group__display_name
#: model:ir.model.fields,field_description:stock.field_product_removal__display_name
#: model:ir.model.fields,field_description:stock.field_product_replenish__display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location__display_name
#: model:ir.model.fields,field_description:stock.field_stock_lot__display_name
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_level__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__display_name
#: model:ir.model.fields,field_description:stock.field_stock_request_count__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_route__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__display_name
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Display Name"
msgstr "Anzeigename"

#. module: stock
#: model:res.groups,name:stock.group_lot_on_delivery_slip
msgid "Display Serial & Lot Number in Delivery Slips"
msgstr "Los- & Seriennummern in Lieferscheinen anzeigen."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.package_level_tree_view_picking
msgid "Display package content"
msgstr "Paketinhalte anzeigen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__disposable
msgid "Disposable Box"
msgstr "Einwegbox"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "Do you confirm you want to scrap"
msgstr "Bestätigen Sie, dass Sie einen Ausschuss von"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Documentation"
msgstr "Dokumentation"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_done
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__done
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Done"
msgstr "Erledigt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
msgid "Done By"
msgstr "Erledigt von"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_quantity
msgid "Done Packaging Quantity"
msgstr "Erledigte Verpackungsmenge"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__draft
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft"
msgstr "Entwurf"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft Moves"
msgstr "Entwurfsbuchung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_dropshipping
msgid "Dropshipping"
msgstr "Streckengeschäft"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid ""
"Due to receipts scheduled in the future, you might end up with "
"excessive stock . Check the Forecasted Report  before reordering"
msgstr ""
"Aufgrund von in der Zukunft geplanten Eingängen kann es vorkommen, dass Sie "
"einen Bestandsüberschuss haben. Prüfen Sie den Prognosebericht, bevor Sie "
"nachbestellen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
msgid "Duplicated SN Warning"
msgstr "Warnung für duplizierte SN"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__sn_duplicated
msgid "Duplicated Serial Number"
msgstr "Duplizierte Seriennummer"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__dymo
msgid "Dymo"
msgstr "Dymo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "Easypost-Konnektor"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Edit Product"
msgstr "Produkt bearbeiten"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Editing quantities in an Inventory Adjustment location is forbidden,those "
"locations are used as counterpart when correcting the quantities."
msgstr ""
"Es ist verboten, Mengen an Standorten für Bestandsaufnahmen zu bearbeiten. "
"Diese Standorte werden als Gegenstück verwendet, wenn die Mengen korrigiert "
"werden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Effective Date"
msgstr "Tatsächliches Datum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Email Confirmation"
msgstr "E-Mail-Bestätigung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_move_email_validation
#: model:ir.model.fields,field_description:stock.field_res_config_settings__stock_move_email_validation
msgid "Email Confirmation picking"
msgstr "E-Mail-Bestätigung für Kommissionierung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_mail_confirmation_template_id
msgid "Email Template confirmation picking"
msgstr "E-Mail-Vorlage zur Bestätigung der Kommissionierung"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__stock_mail_confirmation_template_id
msgid "Email sent to the customer once the order is done."
msgstr ""
"E-Mail wird an den Kunden versandt, sobald der Auftrag bearbeitet wurde."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Empty Locations"
msgstr "Leere Lagerorte"

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid ""
"Enjoy a quick-paced experience with the Odoo barcode app. It is blazing fast"
" and works even without a stable internet connection. It supports all flows:"
" inventory adjustments, batch picking, moving lots or pallets, low inventory"
" checks, etc. Go to the \"Apps\" menu to activate the barcode interface."
msgstr ""
"Genießen Sie zügige Abläufe mit der Odoo-Barcode-App. Sie ist blitzschnell "
"und funktioniert auch ohne stabile Internetverbindung. Sie unterstützt alle "
"Abläufe: Bestandsaufnahme, Stapelkommissionierung, Verschieben von Losen "
"oder Paletten, Überprüfung von niedrigem Bestand usw. Rufen Sie das Menü "
"„Apps“ auf, um die Barcode-Schnittstelle zu aktivieren."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__tracking
#: model:ir.model.fields,help:stock.field_product_template__tracking
#: model:ir.model.fields,help:stock.field_stock_move__has_tracking
#: model:ir.model.fields,help:stock.field_stock_move_line__tracking
#: model:ir.model.fields,help:stock.field_stock_quant__tracking
#: model:ir.model.fields,help:stock.field_stock_scrap__tracking
#: model:ir.model.fields,help:stock.field_stock_track_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""
"Stellen Sie die Rückverfolgbarkeit eines lagerfähigen Produkts in Ihrem "
"Lagerhaus sicher."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Every stock operation in Odoo moves the products from one\n"
"            location to another one.  For instance, if you receive products\n"
"            from a vendor, Odoo will move products from the Vendor\n"
"            location to the Stock location. Each report can be performed on\n"
"            physical, partner or virtual locations."
msgstr ""
"Jeder Bestandsvorgang in Odoo bewegt die Produkte von einem \n"
"            Lagerort zum anderen. Zum Beispiel, wenn Sie Produkte von einem \n"
"            Lieferanten erhalten, wird Odoo Produkte aus dem \n"
"            Lieferantenstandort an den Lagerort verschieben. Jeder Bericht kann an physischen,\n"
"            Partner- oder virtuellen Orten durchgeführt werden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s) occurred on the picking"
msgstr "Bei der Kommissionierung aufgetretene Ausnahme(n)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s):"
msgstr "Ausnahme(n):"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
msgid "Exp"
msgstr "Erw."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Exp %s"
msgstr "Erw. %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__expected
msgid "Expected"
msgstr "Erwartet"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Expected Delivery:"
msgstr "Erwartetes Lieferdatum:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_product_expiry
msgid "Expiration Dates"
msgstr "Ablaufdaten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "External note..."
msgstr "Externe Notiz ..."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_removal__method
msgid "FIFO, LIFO..."
msgstr "FIFO, LIFO"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_favorite
msgid "Favorite"
msgstr "Favorit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__favorite_user_ids
msgid "Favorite User"
msgstr "Lieblingsbenutzer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Favorites"
msgstr "Favoriten"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__2
msgid "February"
msgstr "Februar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_fedex
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "FedEx Connector"
msgstr "FedEx-Konnektor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__filtered_location
msgid "Filtered Location"
msgstr "Gefilterter Standort"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Filters"
msgstr "Filter"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_final_id
msgid "Final Location"
msgstr "Endgültiger Standort"

#. module: stock
#: model:product.removal,name:stock.removal_fifo
msgid "First In First Out (FIFO)"
msgstr "First In – First Out (FIFO)"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "First Lot Number"
msgstr "Erste Losnummer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial
msgid "First SN/Lot"
msgstr "Erste Serien-/Losnummer"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "First Serial Number"
msgstr "Erste Seriennummer"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__fixed
msgid "Fixed"
msgstr "Fest"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_id
msgid "Fixed Procurement Group"
msgstr "Fixierte Beschaffungsgruppe"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_type_icon
#: model:ir.model.fields,help:stock.field_stock_picking__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "FontAwesome-Icon, z. B. fa-tasks"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__removal_strategy_id
msgid "Force Removal Strategy"
msgstr "Entnahmestrategie erzwingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_forecast
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Forecast"
msgstr "Prognose"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_availability
msgid "Forecast Availability"
msgstr "Verfügbarkeit prognostizieren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Forecast Description"
msgstr "Prognosenbeschreibung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Forecast Report"
msgstr "Prognosebericht"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Prognostizierter Bestand (berechnet als Vorrätige Menge + Eingänge - Ausgänge)\n"
"Im Kontext mit einem einzigen Lagerort beinhaltet dies den Bestand dieses oder eines untergeordneten Lagerorts.\n"
"Im Kontext mit einem einzigen Lagerhaus, beinhaltet dies den Bestand an dem Lagerort in diesem Lagerhaus, oder an den \n"
"untergeordneten Lagerorten. Andernfalls ergibt sich die Menge aus den Beständen aller Lagerorte des Typs „Intern“."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__free_qty
msgid ""
"Forecast quantity (computed as Quantity On Hand - reserved quantity)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Prognostizierter Bestand (berechnet als vorrätiger Bestand - reservierte Menge)\n"
"Im Kontext mit einem einzigen Lagerort beinhaltet dies den Bestand dieses oder eines untergeordneten Lagerorts.\n"
"Im Kontext mit einem einzigen Lagerhaus, beinhaltet dies den Bestand am Lagerort in diesem Lagerhaus, oder an den \n"
"untergeordneten Lagerorten. Andernfalls ergibt sich die Menge aus den Beständen aller Lagerorte des Typs\n"
"„Intern“."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_template_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
msgid "Forecasted"
msgstr "Prognostiziert"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Forecasted Date"
msgstr "Prognostiziertes Datum"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Forecasted Date + Visibility Days"
msgstr "Prognostiziertes Datum + Sichtbarkeitstage"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__out
msgid "Forecasted Deliveries"
msgstr "Prognostizierte Lieferungen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_expected_date
msgid "Forecasted Expected date"
msgstr "Prognostiziertes, erwartetes Datum"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Forecasted Inventory"
msgstr "Prognostizierter Bestand"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: model:ir.model.fields,field_description:stock.field_product_product__virtual_available
#: model:ir.model.fields,field_description:stock.field_product_replenish__forecasted_quantity
#: model:ir.model.fields,field_description:stock.field_product_template__virtual_available
#: model:ir.model.fields,field_description:stock.field_stock_move__availability
msgid "Forecasted Quantity"
msgstr "Prognostizierte Menge"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__in
msgid "Forecasted Receipts"
msgstr "Prognostizierte Eingänge"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/stock_forecasted.js:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
#: model:ir.actions.client,name:stock.stock_forecasted_product_product_action
#: model:ir.actions.client,name:stock.stock_forecasted_product_template_action
msgid "Forecasted Report"
msgstr "Prognosebericht"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__forecast
msgid "Forecasted Stock"
msgstr "Prognostizierter Bestand"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__forecast_weight
msgid "Forecasted Weight"
msgstr "Prognostiziertes Gewicht"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Forecasted with Pending"
msgstr "Prognostiziert mit Ausstehenden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__print_format
#: model:ir.model.fields,field_description:stock.field_product_label_layout__print_format
msgid "Format"
msgstr "Format"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__free_qty
msgid "Free Qty"
msgstr "Verfügbare Menge"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Free Stock"
msgstr "Verfügbarer Bestand"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Free Stock in Transit"
msgstr "Verfügbarer Bestand in Transit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__free_qty
msgid "Free To Use Quantity "
msgstr "Verfügbare Menge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Free to Use"
msgstr "Verfügbar"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "From"
msgstr "Von"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__owner_id
msgid "From Owner"
msgstr "Vom Eigentümer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__complete_name
msgid "Full Location Name"
msgstr "Vollständige Lagerortbezeichnung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Future Activities"
msgstr "Anstehende Aktivitäten"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Deliveries"
msgstr "Zukünftige Lieferungen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future P&L"
msgstr "Zukünftige G&V"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Productions"
msgstr "Zukünftige Produktionen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Receipts"
msgstr "Zukünftige Eingänge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "General"
msgstr "Allgemein"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Generate"
msgstr "Generieren"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Generate Lot numbers"
msgstr "Losnummern generieren"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Generate Serial numbers"
msgstr "Seriennummern generieren"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Generate Serials/Lots"
msgstr "Serien/Lose generieren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__print_label
msgid "Generate Shipping Labels"
msgstr "Versandetiketten generieren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get a full traceability from vendors to customers"
msgstr ""
"Erhalten Sie eine vollständige Rückverfolgbarkeit von Lieferanten zu Kunden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get informative or blocking warnings on partners"
msgstr "Erhalten Sie informative oder blockierende Warnungen für Partner."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_putaway_rule__sequence
msgid ""
"Give to the more specialized category, a higher priority to have them in top"
" of the list."
msgstr ""
"Geben Sie den spezielleren Kategorien eine höhere Priorität, damit sie ganz "
"oben auf der Liste stehen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__sequence
msgid "Gives the sequence of this line when displaying the warehouses."
msgstr "Gibt die Sequenz dieser Zeile bei der Anzeige der Lagerhäuser an."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Go to Warehouses"
msgstr "Zum Lagerhaus"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Group By"
msgstr "Gruppieren nach"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Group by..."
msgstr "Gruppieren nach ..."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "HTML reports cannot be auto-printed, skipping report: %s"
msgstr ""
"HTML-Berichte können nicht automatisch gedruckt werden, Bericht wird "
"übersprungen: %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Hardware"
msgstr "Hardware"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__has_message
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_message
#: model:ir.model.fields,field_description:stock.field_stock_scrap__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_exist
msgid "Has Pack Operations"
msgstr "Hat Packvorgänge"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_packages
msgid "Has Packages"
msgstr "Hat Pakete"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__has_return
msgid "Has Return"
msgstr "Hat Retoure"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_scrap_move
msgid "Has Scrap Moves"
msgstr "Hat Ausschussbewegungen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_tracking
msgid "Has Tracking"
msgstr "Wird nachverfolgt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_has_variants
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_has_variants
msgid "Has variants"
msgstr "Hat Varianten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Having Category"
msgstr "Mit Kategorie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__height
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Height"
msgstr "Höhe"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posz
msgid "Height (Z)"
msgstr "Lagerort Etage (Z)"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_height
msgid "Height must be positive"
msgstr "Höhe muss positiv sein"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Hidden until next scheduler."
msgstr "Ausgeblendet bis zum nächsten Planer."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__hide_reservation_method
msgid "Hide Reservation Method"
msgstr "Reservierungsmethode verbergen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "History"
msgstr "Historie"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_orderpoint_search_panel.xml:0
msgid "Horizon"
msgstr "Horizont"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_method
msgid "How products in transfers of this operation type should be reserved."
msgstr ""
"Wie Produkte in Bewegungen dieser Vorgangsart reserviert werden sollen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__id
#: model:ir.model.fields,field_description:stock.field_picking_label_type__id
#: model:ir.model.fields,field_description:stock.field_procurement_group__id
#: model:ir.model.fields,field_description:stock.field_product_removal__id
#: model:ir.model.fields,field_description:stock.field_product_replenish__id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__id
#: model:ir.model.fields,field_description:stock.field_stock_location__id
#: model:ir.model.fields,field_description:stock.field_stock_lot__id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__id
#: model:ir.model.fields,field_description:stock.field_stock_move__id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__id
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__id
#: model:ir.model.fields,field_description:stock.field_stock_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_quant__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__id
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__id
#: model:ir.model.fields,field_description:stock.field_stock_request_count__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__id
#: model:ir.model.fields,field_description:stock.field_stock_route__id
#: model:ir.model.fields,field_description:stock.field_stock_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__id
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__id
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__id
msgid "ID"
msgstr "ID"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_exception_icon
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_icon
msgid "Icon"
msgstr "Icon"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_exception_icon
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icon, um eine Ausnahmeaktivität anzuzeigen."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"If a payment is still outstanding more than sixty (60) days after the due "
"payment date, My Company (Chicago) reserves the right to call on the "
"services of a debt recovery company. All legal expenses will be payable by "
"the client."
msgstr ""
"Wenn eine Zahlung mehr als sechzig (60) Tage nach dem Fälligkeitsdatum noch "
"aussteht, behält sich My Company (Chicago) das Recht vor, die Dienste eines "
"Inkassounternehmens in Anspruch zu nehmen. Alle Gerichtskosten gehen zu "
"Lasten des Kunden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"If a separator is defined, a QR code containing all serial numbers contained"
" in package will be generated, using the defined character(s) to separate "
"each numbers"
msgstr ""
"Wenn ein Trennzeichen definiert ist, wird ein QR-Code generiert, der alle im"
" Paket enthaltenen Seriennummern enthält, wobei das/die definierte(n) "
"Zeichen zur Trennung der einzelnen Nummern verwendet werden."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__same
msgid "If all products are same"
msgstr "Wenn alle Produkte gleich sind"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_needaction
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_error
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Falls markiert, weisen einige Nachrichten einen Zustellungsfehler auf."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__propagate_cancel
msgid "If checked, when this move is cancelled, cancel the linked move too"
msgstr ""
"Wenn angekreuzt, wird beim Abbrechen dieser Bewegung auch die verknüpfte "
"Bewegung abgebrochen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__result_package_id
msgid "If set, the operations are packed into this package"
msgstr "Bei Aktivierung werden sämtliche Vorgänge in dieses Paket gepackt."

#. module: stock
#: model:ir.model.fields,help:stock.field_lot_label_layout__label_quantity
msgid ""
"If the UoM of a lot is not 'units', the lot will be considered as a unit and"
" only one label will be printed for this lot."
msgstr ""
"Wenn die Maßeinheit eines Loses nicht „Einheiten“ ist, wird das Los als eine"
" Einheit betrachtet und nur ein Etikett für dieses Los gedruckt."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"orderpoint without removing it."
msgstr ""
"Wenn das aktive Feld auf Falsch gesetzt ist, können Sie die Ansicht für den "
"Beschaffungspunkt ausblenden."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__active
msgid ""
"If the active field is set to False, it will allow you to hide the route "
"without removing it."
msgstr ""
"Wenn das aktive Feld auf Falsch gesetzt ist, können Sie die Route "
"ausblenden, ohne sie zu entfernen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__empty
msgid "If the location is empty"
msgstr "Wenn der Lagerort leer ist"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__sn_duplicated
msgid "If the same SN is in another Quant"
msgstr "Wenn dieselbe SN in einer anderen Menge ist"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_delivery_slip
msgid ""
"If this checkbox is ticked, Odoo will automatically print the delivery slip "
"of a picking when it is validated."
msgstr ""
"Wenn dieses Kontrollkästchen aktiviert ist, druckt Odoo automatisch den "
"Lieferschein einer Kommissionierung aus, wenn diese validiert wird."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_lot_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the lot/SN labels "
"of a picking when it is validated."
msgstr ""
"Wenn dieses Kontrollkästchen aktiviert ist, druckt Odoo automatisch die "
"Los-/Seriennummeretiketten einer Kommissionierung aus, wenn diese validiert "
"wird."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_package_label
msgid ""
"If this checkbox is ticked, Odoo will automatically print the package label "
"when \"Put in Pack\" button is used."
msgstr ""
"Wenn dieses Kontrollkästchen aktiviert ist, druckt Odoo automatisch das "
"Produktetiketten aus, wenn „Verpacken“ verwendet wird."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_packages
msgid ""
"If this checkbox is ticked, Odoo will automatically print the packages and "
"their contents of a picking when it is validated."
msgstr ""
"Wenn dieses Kontrollkästchen aktiviert ist, druckt Odoo automatisch die "
"Pakete und ihre Inhalte einer Kommissionierung aus, wenn diese validiert "
"wird."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_product_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the product labels"
" of a picking when it is validated."
msgstr ""
"Wenn dieses Kontrollkästchen aktiviert ist, druckt Odoo automatisch die "
"Produktetiketten einer Kommissionierung aus, wenn diese validiert wird."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_reception_report_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the reception "
"report labels of a picking when it is validated."
msgstr ""
"Wenn dieses Kontrollkästchen aktiviert ist, druckt Odoo automatisch die "
"Empfangsberichtsetiketten einer Kommissionierung aus, wenn diese validiert "
"wird."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically print the reception "
"report of a picking when it is validated and has assigned moves."
msgstr ""
"Wenn dieses Kontrollkästchen aktiviert ist, druckt Odoo automatisch den "
"Empfangsbericht einer Kommissionierung aus, wenn diese validiert wird und "
"zugewiesende Buchungen hat."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_return_slip
msgid ""
"If this checkbox is ticked, Odoo will automatically print the return slip of"
" a picking when it is validated."
msgstr ""
"Wenn dieses Kontrollkästchen aktiviert ist, druckt Odoo automatisch den "
"Retourenschein einer Kommissionierung aus, wenn diese validiert wird."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_show_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically show the reception "
"report (if there are moves to allocate to) when validating."
msgstr ""
"Wenn dieses Kontrollkästchen aktiviert ist, zeigt Odoo bei der Validierung "
"automatisch den Empfangsbericht an (wenn es Bewegungen gibt, die zuzuordnen "
"sind)."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_operations
msgid ""
"If this checkbox is ticked, the pickings lines will represent detailed stock"
" operations. If not, the picking lines will represent an aggregate of "
"detailed stock operations."
msgstr ""
"Wenn dieses Kontrollkästchen angekreuzt ist, stellen die "
"Kommissionierungszeilen detaillierte Bestandsvorgänge dar. Falls nicht, "
"stellen die Kommissionierungszeilen eine Gesamheit detaillierter "
"Bestandsvorgänge dar."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_create_lots
msgid ""
"If this is checked only, it will suppose you want to create new Lots/Serial "
"Numbers, so you can provide them in a text field. "
msgstr ""
"Wenn nur dies angekreuzt ist, wird angenommen, Sie wollen neue "
"Los-/Seriennummern erstellen, damit Sie sie in einem Textfeld bereitstellen "
"können."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Lots/Serial Numbers. You "
"can also decide to not put lots in this operation type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr ""
"Wenn die aktiviert ist, können Sie die Los- und Seriennummern auswählen. Sie"
" können sich auch dafür entscheiden, bei dieser Vorgangsart keine Losnummer "
"zu verwenden. Das bedeutet, dass es einen Bestand ohne Los erstellt oder "
"keine Beschränkung für das genommene Los vorsieht."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__return_id
msgid ""
"If this picking was created as a return of another picking, this field links"
" to the original picking."
msgstr ""
"Wenn diese Kommissionierung als Rückgabe einer anderen Kommissionierung "
"erstellt wurde, verweist dieses Feld auf die ursprüngliche Kommissionierung."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__backorder_id
msgid ""
"If this shipment was split, then this field links to the shipment which "
"contains the already processed part."
msgstr ""
"Falls die Sendung aufgeteilt wurde, dann verweist dieses Feld auf die "
"Sendung, die den bereits gelieferten Teil enthält."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_entire_packs
msgid "If ticked, you will be able to select entire packages to move"
msgstr ""
"Wenn Sie dieses Kontrollkästchen aktivieren, können Sie ganze Pakete zum "
"Verschieben auswählen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__active
msgid "If unchecked, it will allow you to hide the rule without removing it."
msgstr ""
"Wenn diese Option nicht markiert ist, können Sie die Regel ausblenden, ohne "
"sie zu entfernen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__backorder_confirmation_id
msgid "Immediate Transfer"
msgstr "Sofortiger Transfer"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Import Lots"
msgstr "Lose importieren"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Import Serials"
msgstr "Seriennummern importieren"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Import Serials/Lots"
msgstr "Serien/Lose importieren"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Import Template for Inventory Adjustments"
msgstr "Importvorlage für Bestandsaufnahme"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "In Stock"
msgstr "Auf Lager"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__in_type_id
msgid "In Type"
msgstr "Anlieferung"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date_deadline
#: model:ir.model.fields,help:stock.field_stock_picking__date_deadline
msgid ""
"In case of outgoing flow, validate the transfer before this date to allow to deliver at promised date to the customer.\n"
"        In case of incoming flow, validate the transfer before this date in order to have these products in stock at the date promised by the supplier"
msgstr ""
"Bei Warenausgängen validieren Sie den Transfer vor diesem Datum, um die Lieferung zum versprochenen Datum an den Kunden zu ermöglichen.\n"
"        Bei Wareneingängen validieren Sie den Transfer vor diesem Datum, damit die Produkte zu dem vom Lieferanten versprochenen Termin auf Lager sind."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "In internal locations"
msgstr "In internen Lagerorten"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"In order for it to be admissible, My Company (Chicago) must be notified of "
"any claim by means of a letter sent by recorded delivery to its registered "
"office within 8 days of the delivery of the goods or the provision of the "
"services."
msgstr ""
"Damit eine Reklamation zulässig ist, muss sie My Company (Chicago) innerhalb"
" von 8 Tagen nach der Lieferung der Waren oder der Erbringung der "
"Dienstleistungen per Einschreiben an ihren eingetragenen Firmensitz "
"mitgeteilt werden."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_product_product__incoming_qty
#: model:ir.model.fields,field_description:stock.field_product_template__incoming_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Incoming"
msgstr "Eingang"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__in_date
msgid "Incoming Date"
msgstr "Eingangsdatum"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Incoming Draft Transfer"
msgstr "Entwurf des Eingangstransfers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__incoming_move_line_ids
msgid "Incoming Move Line"
msgstr "Eingangsbewegungszeile"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_steps
msgid "Incoming Shipments"
msgstr "Wareneingänge"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "Incorrect type of action submitted as a report, skipping action"
msgstr ""
"Fehlerhafter Aktionstyp als Bericht eingereicht, Aktion wird übersprungen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_diff_quantity
msgid ""
"Indicates the gap between the product's theoretical quantity and its counted"
" quantity."
msgstr ""
"Zeigt die Differenz an zwischen der theoretischen und der tatsächlichen "
"Menge eines Produkts."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
msgid "Initial Demand"
msgstr "Anfangsnachfrage"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Input"
msgstr "Eingang"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_input_stock_loc_id
msgid "Input Location"
msgstr "Eingangslager"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "Install"
msgstr "Installieren"

#. module: stock
#: model:ir.actions.server,name:stock.action_install_barcode
msgid "Install Barcode"
msgstr "Barcode installieren"

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_company.py:0
msgid "Inter-warehouse transit"
msgstr "Lagerübergreifender Transit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_id
msgid "Intermediate Location"
msgstr "Übergangsstandort"

#. module: stock
#: model:ir.ui.menu,name:stock.int_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Internal"
msgstr "Intern"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__internal
msgid "Internal Location"
msgstr "Interner Lagerort"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal Locations"
msgstr "Interne Lagerorte"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__ref
msgid "Internal Reference"
msgstr "Interne Referenz"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__internal
msgid "Internal Transfer"
msgstr "Interner Transfer"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_internal
#: model:stock.picking.type,name:stock.picking_type_internal
msgid "Internal Transfers"
msgstr "Interne Transfers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__internal_transit_location_id
msgid "Internal Transit Location"
msgstr "Internes Umschlagslager"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__int_type_id
msgid "Internal Type"
msgstr "Interner Typ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_internal_location_ids
msgid "Internal locations among descendants"
msgstr "Interne Standorte unter den Nachkommen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid "Internal locations having stock can't be converted"
msgstr "Interne Lageorte mit Bestand können nicht umgewandelt werden"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__ref
msgid ""
"Internal reference number in case it differs from the manufacturer's "
"lot/serial number"
msgstr ""
"Interne Referenzunummer, sollte diese von der Los-/Seriennummer des "
"Herstellers abweichen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Invalid domain left operand %s"
msgstr "Ungültiger linker Domain-Operand %s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/models/stock_lot.py:0
msgid "Invalid domain operator %s"
msgstr "Ungültiger Domain-Operator %s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/models/stock_lot.py:0
msgid "Invalid domain right operand '%s'. It must be of type Integer/Float"
msgstr ""
"Ungültiger rechter Bereichsoperand „%s“. Er muss vom Typ "
"Ganzzahl/Gleitkommazahl sein."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"Invalid rule's configuration, the following rule causes an endless loop: %s"
msgstr ""
"Ungültige Regelkonfiguration, die folgende Regel verursacht eine "
"Endlosschleife: %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_auto_apply
msgid "Inventoried Quantity"
msgstr "Inventarisierte Menge"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_inventory_tree
#: model:ir.actions.server,name:stock.action_view_quants
#: model:ir.model.fields,field_description:stock.field_stock_move__is_inventory
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_inventory
#: model:ir.ui.menu,name:stock.menu_stock_root
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_pivot
msgid "Inventory"
msgstr "Lager"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_inventory_adjustement_name
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Inventory Adjustment"
msgstr "Bestandsaufnahme"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.model,name:stock.model_stock_inventory_adjustment_name
msgid "Inventory Adjustment Reference / Reason"
msgstr "Referenz/Grund der Bestandsaufnahme"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_warning
msgid "Inventory Adjustment Warning"
msgstr "Warnung bei Bestandsaufnahme"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Inventory Adjustments"
msgstr "Bestandsaufnahmen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Inventory Count Sheet"
msgstr "Blatt für Bestandsaufnahme"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__inventory_date
msgid "Inventory Date"
msgstr "Datum der Bestandsaufnahme"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__cyclic_inventory_frequency
#: model:ir.model.fields,field_description:stock.field_stock_quant__cyclic_inventory_frequency
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Inventory Frequency"
msgstr "Inventurhäufigkeit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_inventory
msgid "Inventory Location"
msgstr "Lagerort"

#. module: stock
#: model:ir.model,name:stock.model_stock_location
msgid "Inventory Locations"
msgstr "Lagerorte"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__inventory
msgid "Inventory Loss"
msgstr "Bestandsschwund"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_view_inherit_stock
msgid "Inventory Management"
msgstr "Lagerverwaltung"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Inventory On Hand"
msgstr "Vorrätige Bestände"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_type_action
msgid "Inventory Overview"
msgstr "Lagerübersicht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_set
msgid "Inventory Quantity Set"
msgstr "Bestandsmenge eingestellt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__inventory_adjustment_name
msgid "Inventory Reason"
msgstr "Grund für Inventur"

#. module: stock
#: model:ir.model,name:stock.model_stock_route
msgid "Inventory Routes"
msgstr "Lagerrouten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Inventory Valuation"
msgstr "Lagerbewertung"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_inventory_at_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__inventory_datetime
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Inventory at Date"
msgstr "Lagerbestand zum Zeitpunkt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__is_empty
msgid "Is Empty"
msgstr "Ist leer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_fresh_package
msgid "Is Fresh Package"
msgstr "Ist ein Frischepaket"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_locked
msgid "Is Locked"
msgstr "Ist gesperrt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__is_multi_location
msgid "Is Multi Location"
msgstr "Ist Mehrfachstandort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__is_partial_package
msgid "Is Partial Package"
msgstr "Ist Teillieferung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_signed
msgid "Is Signed"
msgstr "Ist unterzeichnet"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__scrap_location
msgid "Is a Scrap Location?"
msgstr "Ist Lagerort für Ausschuss?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_initial_demand_editable
msgid "Is initial demand editable"
msgstr "Ist Anfangsnachfrage bearbeitbar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_deadline_issue
msgid "Is late"
msgstr "Ist verspätet"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_deadline_issue
msgid "Is late or will be late depending on the deadline and scheduled date"
msgstr ""
"Ist verspätet oder wird verspätet sein, abhängig von der Frist und dem "
"geplanten Datum"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_quantity_done_editable
msgid "Is quantity done editable"
msgstr "Ist die erledigte Menge bearbeitbar"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"It is not possible to unreserve more products of %s than you have in stock."
msgstr ""
"Es ist nicht möglich, die Reservierung für mehr Produkte von %s aufzuheben "
"als im Lager vorhanden sind."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_type
msgid "It specifies goods to be deliver partially or all at once"
msgstr "Auslieferung wird als Teillieferung oder Komplettlieferung festgelegt"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__move_type
msgid "It specifies goods to be transferred partially or all at once"
msgstr "Transfer wird als Teil- oder Komplettltransfer festgelegt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__json_popover
msgid "JSON data for the popover widget"
msgstr "JSON-Daten für Pop-over-Widget"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__1
msgid "January"
msgstr "Januar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "John Doe"
msgstr "John Doe"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_lead_days
msgid "Json Lead Days"
msgstr "Json-Vorlauftage"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.js:0
msgid "Json Popup"
msgstr "Json-Pop-up"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_replenishment_history
msgid "Json Replenishment History"
msgstr "Json-Auffüllhistorie"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__7
msgid "July"
msgstr "Juli"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__6
msgid "June"
msgstr "Juni"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "Kanban-Dashboard Grafik"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Counted Quantity"
msgstr "Gezählte Menge behalten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Difference"
msgstr "Differenz beibehalten"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Keep current lines"
msgstr "Aktuelle Zeilen beibehalten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Counted Quantity</strong> (the Difference will be updated)"
msgstr ""
"Behalten Sie die <strong>gezählte Menge</strong> bei (die Differenz wird "
"aktualisiert)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Difference</strong> (the Counted Quantity will be updated "
"to reflect the same difference as when you counted)"
msgstr ""
"Behalten Sie die <strong>Differenz</strong> bei (die gezählte Menge wird "
"aktualisiert, um die gleiche Differenz wie bei der Zählung wiederzugeben)"

#. module: stock
#: model:ir.actions.server,name:stock.action_print_labels
msgid "Labels"
msgstr "Etiketten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_picking_label_type__label_type
msgid "Labels to print"
msgstr "Zu druckende Etiketten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Laptop"
msgstr "Laptop"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 12 Months"
msgstr "Letzten 12 Monate"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 3 Months"
msgstr "Letzten 3 Monate"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 30 Days"
msgstr "Letzten 30 Tage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__last_count_date
msgid "Last Count Date"
msgstr "Datum der letzten Zählung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__last_delivery_partner_id
msgid "Last Delivery Partner"
msgstr "Letzter Lieferpartner"

#. module: stock
#: model:product.removal,name:stock.removal_lifo
msgid "Last In First Out (LIFO)"
msgstr "Last In – First Out (LIFO)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__last_inventory_date
msgid "Last Inventory"
msgstr "Letzte Inventur"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__write_uid
#: model:ir.model.fields,field_description:stock.field_picking_label_type__write_uid
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__write_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_lot__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_route__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__write_date
#: model:ir.model.fields,field_description:stock.field_picking_label_type__write_date
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_date
#: model:ir.model.fields,field_description:stock.field_product_removal__write_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location__write_date
#: model:ir.model.fields,field_description:stock.field_stock_lot__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__write_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_route__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__last_used
msgid "Last Used"
msgstr "Zuletzt verwendet"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__last_count_date
msgid "Last time the Quantity was Updated"
msgstr "Letztes Mal, dass die Menge aktualisiert wurde"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__late
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late"
msgstr "Verspätet"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Activities"
msgstr "Verspätete Aktivitäten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Availability"
msgstr "Verspätete Verfügbarkeit"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_late
msgid "Late Transfers"
msgstr "Letzte Transfers"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__products_availability
msgid "Latest product availability status of the picking"
msgstr "Aktueller Stand der Produktverfügbarkeit bei der Kommissionierung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__lead_days_date
msgid "Lead Days Date"
msgstr "Datum der Vorlauftage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__lead_time
#: model:ir.model.fields,field_description:stock.field_stock_rule__delay
msgid "Lead Time"
msgstr "Vorlaufzeit"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Lead Times"
msgstr "Vorlaufzeiten"

#. module: stock
#: model:product.removal,name:stock.removal_least_packages
msgid "Least Packages"
msgstr "Pakete mit wenigsten Produkten"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__empty
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__none
msgid "Leave Empty"
msgstr "Leer lassen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__company_id
#: model:ir.model.fields,help:stock.field_stock_rule__route_company_id
msgid "Leave this field empty if this route is shared between all companies"
msgstr ""
"Lassen Sie dieses Feld leer, wenn diese Route zwischen allen Unternehmen "
"geteilt wird"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Legend"
msgstr "Legende"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__packaging_length
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Length"
msgstr "Länge"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_length
msgid "Length must be positive"
msgstr "Länge muss positiv sein"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__length_uom_name
msgid "Length unit of measure label"
msgstr "Etikett für die Längenmaßeinheit"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__company_id
#: model:ir.model.fields,help:stock.field_stock_quant__company_id
#: model:ir.model.fields,help:stock.field_stock_quant_relocate__company_id
msgid "Let this field empty if this location is shared between companies"
msgstr ""
"Lassen Sie dieses Feld leer, wenn dieser Lagerort zwischen allen Unternehmen"
" geteilt wird"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Linked Moves"
msgstr "Verknüpfte Bewegungen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of detailed operations"
msgstr "Listenansicht der detaillierten Vorgänge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of next transfers"
msgstr "Listenansicht der nächsten Transfers"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of operations"
msgstr "Listenansicht der Vorgänge"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__location_id
#: model:ir.model.fields,field_description:stock.field_product_template__location_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__location_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__location_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__location_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__location
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Location"
msgstr "Lagerort"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_location_barcode
msgid "Location Barcode"
msgstr "Lagerort-Barcode"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__name
msgid "Location Name"
msgstr "Lagerortname"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__lot_stock_id
msgid "Location Stock"
msgstr "Lagerortbestand"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__usage
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Location Type"
msgstr "Lagerorttyp"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: Store to"
msgstr "Lagerort: Lagern in"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: When arrives to"
msgstr "Lagerort: Bei Ankunft in"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.actions.act_window,name:stock.action_location_form
#: model:ir.actions.act_window,name:stock.action_prod_inv_location_form
#: model:ir.actions.act_window,name:stock.action_storage_category_locations
#: model:ir.ui.menu,name:stock.menu_action_location_form
#: model:ir.ui.menu,name:stock.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.report_location_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Locations"
msgstr "Lagerorte"

#. module: stock
#: model:ir.actions.server,name:stock.action_toggle_is_locked
msgid "Lock/Unlock"
msgstr "Sperren/Entsperren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Logistics"
msgstr "Logistik"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__lot
msgid "Lot"
msgstr "Los"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_lot_customer_report_view_list
msgid "Lot / Serial Number"
msgstr "Los-/Seriennummer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__lot_label_format
msgid "Lot Label Format to auto-print"
msgstr "Format der automatisch zu druckenden Losetiketten"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_lot_template_view
msgid "Lot Label Report"
msgstr "Bericht zu Losetiketten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__lot_properties_definition
msgid "Lot Properties"
msgstr "Los-Eigenschaften"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Lot numbers"
msgstr "Losnummern"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__picking_label_type__label_type__lots
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Lot/SN Labels"
msgstr "Los-/SN-Etiketten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
msgid "Lot/SN:"
msgstr "Los/SN:"

#. module: stock
#: model:ir.model,name:stock.model_stock_lot
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__lot_id
msgid "Lot/Serial"
msgstr "Los/Serie"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Lot/Serial #"
msgstr "Los/Serie #"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Lot/Serial Number"
msgstr "Los-/Seriennummer"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_lot_label
msgid "Lot/Serial Number (PDF)"
msgstr "Los-/Seriennummern (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_lot_template
msgid "Lot/Serial Number (ZPL)"
msgstr "Los-/Seriennummer (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_name
msgid "Lot/Serial Number Name"
msgstr "Name der Los-/Seriennummer"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "Lot/Serial Number Relocated"
msgstr "Los-/Seriennummer umgelagert"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Lot/Serial:"
msgstr "Los/Serie:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_production_lot
msgid "Lots & Serial Numbers"
msgstr "Los- & Seriennummern"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Lots & Serial numbers will appear on the delivery slip"
msgstr "Los- & Seriennummer werden auf dem Lieferschein angegeben"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_production_lot_form
#: model:ir.ui.menu,name:stock.menu_action_production_lot_form
msgid "Lots / Serial Numbers"
msgstr "Los- /Seriennummern"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lots_visible
msgid "Lots Visible"
msgstr "Lose sichtbar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Lots or serial numbers were not provided for tracked products"
msgstr ""
"Für verfolgte Produkte wurden keine Los- oder Seriennummern angegeben."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_product_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Lots/Serial Numbers"
msgstr "Los-/Seriennummern"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_production_lot_form
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid ""
"Lots/Serial numbers help you tracking the path followed by your products.\n"
"            From their traceability report you will see the full history of their use, as well as their composition."
msgstr ""
"Los-/Seriennummern helfen Ihnen, den Weg Ihrer Produkte zu verfolgen.\n"
"            Aus dem Rückverfolgbarkeitsbericht können Sie die gesamte Historie ihrer Verwendung sowie ihre Zusammensetzung ersehen."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_product_replenish
msgid "Low on stock? Let's replenish."
msgstr "Wenig Bestand? Füllen wir ihn auf."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__mto_pull_id
msgid "MTO rule"
msgstr "Auftragsfertigungregel"

#. module: stock
#: model:res.groups,name:stock.group_tracking_owner
msgid "Manage Different Stock Owners"
msgstr "Verschiedene Lagereigentümer verwalten"

#. module: stock
#: model:res.groups,name:stock.group_production_lot
msgid "Manage Lots / Serial Numbers"
msgstr "Los-/Seriennummern verwalten"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_locations
msgid "Manage Multiple Stock Locations"
msgstr "Mehrere Lagerorte verwalten"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_warehouses
msgid "Manage Multiple Warehouses"
msgstr "Mehrere Lagerhäuser verwalten"

#. module: stock
#: model:res.groups,name:stock.group_tracking_lot
msgid "Manage Packages"
msgstr "Pakete verwalten"

#. module: stock
#: model:res.groups,name:stock.group_adv_location
msgid "Manage Push and Pull inventory flows"
msgstr "Push- und Pulllagerabläufe verwalten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Manage product packagings (e.g. pack of 6 bottles, box of 10 pieces)"
msgstr ""
"Verwalten Sie Produktverpackungen (z. B. Packung mit 6 Flaschen, Karton mit "
"10 Stück)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__manual
msgid "Manual"
msgstr "Manuell"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__manual
msgid "Manual Operation"
msgstr "Manueller Vorgang"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "Manual Replenishment"
msgstr "Manuelle Auffüllung"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__manual
msgid "Manually"
msgstr "Manuell"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Manufacturing"
msgstr "Fertigung"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__3
msgid "March"
msgstr "März"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Mark as Todo"
msgstr "Als To-do markieren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Max"
msgstr "Max."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid "Max Quantity"
msgstr "Max. Menge"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__max_weight
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__max_weight
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Max Weight"
msgstr "Max. Gewicht"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_max_weight
msgid "Max Weight must be positive"
msgstr "Max. Gewicht muss positiv sein"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_positive_max_weight
msgid "Max weight should be a positive number."
msgstr "Das Maximalgewicht sollte ein positiver Wert sein."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before_priority
msgid ""
"Maximum number of days before scheduled date that priority picking products "
"should be reserved."
msgstr ""
"Maximale Anzahl von Tagen vor dem geplanten Datum, an dem vorrangig zu "
"kommissionierende Produkte reserviert werden sollten."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before
msgid ""
"Maximum number of days before scheduled date that products should be "
"reserved."
msgstr ""
"Maximale Anzahl von Tagen vor dem geplanten Datum, an dem die Produkte "
"reserviert werden sollen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__max_weight
msgid "Maximum weight shippable in this packaging"
msgstr "Maximalgewicht lieferbar in dieser Verpackung"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__5
msgid "May"
msgstr "Mai"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error
msgid "Message Delivery error"
msgstr "Nachricht mit Zustellungsfehler"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn_msg
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn_msg
msgid "Message for Stock Picking"
msgstr "Mitteilung für Bestandskommissionierung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_ids
msgid "Messages"
msgstr "Mitteilungen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__method
msgid "Method"
msgstr "Methode"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Min"
msgstr "Min."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid "Min Quantity"
msgstr "Min. Menge"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Mindestbestandsregel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr "Mindestbestandsregeln"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__move_ids
msgid "Move"
msgstr "Bewegung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Move Detail"
msgstr "Buchungsdetail"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_entire_packs
msgid "Move Entire Packages"
msgstr "Gesamte Pakete bewegen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_line_ids
msgid "Move Line"
msgstr "Buchungszeile"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
msgid "Move Lines"
msgstr "Buchungszeilen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_lines_count
msgid "Move Lines Count"
msgstr "Anzahl Buchungszeilen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_quantity
msgid "Move Quantity"
msgstr "Menge verschieben"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__origin_returned_move_id
msgid "Move that created the return move"
msgstr "Zugehörige Lieferung zu dieser Rücksendung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__product_return_moves
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Moves"
msgstr "Bewegungen"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_action
#: model:ir.ui.menu,name:stock.stock_move_menu
msgid "Moves Analysis"
msgstr "Buchungsanalyse"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_line_action
#: model:ir.ui.menu,name:stock.stock_move_line_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_pivot
msgid "Moves History"
msgstr "Bewegungshistorie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__group_id
msgid ""
"Moves created through this orderpoint will be put in this procurement group."
" If none is given, the moves generated by stock rules will be grouped into "
"one big picking."
msgstr ""
"Die durch die Beschaffungspunkt ausgelösten Bewegungen werden in einer "
"Gruppe zusammengefasst. Wenn keine Gruppe angegeben wurde, werden alle "
"Lieferungen in einer einzigen großen Kommissionierung zusammengefasst."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_adv_location
msgid "Multi-Step Routes"
msgstr "Mehrstufige Routen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid "Multiple Quantity"
msgstr "Mehrfache Menge"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_package_type
msgid "Multiple capacity rules for one package type."
msgstr "Mehrere Kapazitätsregeln für einen Pakettyp."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_product
msgid "Multiple capacity rules for one product."
msgstr "Mehrere Kapazitätsregeln für ein Produkt."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__my_activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Frist für meine Aktivitäten"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"My Company (Chicago) undertakes to do its best to supply performant services"
" in due time in accordance with the agreed timeframes. However, none of its "
"obligations can be considered as being an obligation to achieve results. My "
"Company (Chicago) cannot under any circumstances, be required by the client "
"to appear as a third party in the context of any claim for damages filed "
"against the client by an end consumer."
msgstr ""
"Mein Unternehmen (Chicago) gibt sein Bestes, um die Leistungen rechtzeitig "
"und in Übereinstimmung mit den vereinbarten Fristen zu erbringen. Keine der "
"Verpflichtungen von My Company (Chicago) kann jedoch als Verpflichtung zur "
"Erzielung von Ergebnissen angesehen werden. My Company (Chicago) kann unter "
"keinen Umständen vom Kunden aufgefordert werden, im Rahmen einer "
"Schadensersatzklage, die ein Endverbraucher gegen den Kunden erhebt, als "
"Drittpartei aufzutreten."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "My Counts"
msgstr "Meine Zählungen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "My Transfers"
msgstr "Meine Transfers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__name
msgid "Name"
msgstr "Name"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Name Demo"
msgstr "Demo-Name"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_in
msgid "Nbr Moves In"
msgstr "Anzahl Eingangsbewegungen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_out
msgid "Nbr Moves Out"
msgstr "Anzahl Ausgangsbewegungen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Negative Forecasted Quantity"
msgstr "Negative prognostizierte Menge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Negative Stock"
msgstr "Negativer Bestand"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__net_weight
msgid "Net Weight"
msgstr "Nettogewicht"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__never
msgid "Never"
msgstr "Niemals"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__never_product_template_attribute_value_ids
msgid "Never attribute Values"
msgstr "„Nie“-Attributwerte"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__new
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "New"
msgstr "Neu"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "New Move: %(product)s"
msgstr "Neue Bewegung: %(product)s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__new_quantity
msgid "New Quantity on Hand"
msgstr "Neu vorrätige Menge"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_form
msgid "New Transfer"
msgstr "Neuer Transfer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_calendar_event_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nächstes Aktivitätskalenderereignis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nächste Aktivitätsfrist"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_summary
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_summary
msgid "Next Activity Summary"
msgstr "Zusammenfassung der nächsten Aktivität"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_id
msgid "Next Activity Type"
msgstr "Nächster Aktivitätstyp"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__next_inventory_date
msgid "Next Expected"
msgstr "Nächste erwartete Inventur"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Next Transfers"
msgstr "Nächste Transfers"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_date
msgid "Next date the On Hand Quantity should be counted."
msgstr "Nächstes Datum, an dem die vorrätige Menge gezählt werden soll."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Next transfer(s) impacted:"
msgstr "Nächste Transfer(s) betroffen:"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__no
msgid "No"
msgstr "Nein"

#. module: stock
#. odoo-python
#: code:addons/stock/report/report_stock_reception.py:0
msgid "No %s selected or a delivery order selected"
msgstr "Keine %s ausgewählt oder ein Lieferauftrag ausgewählt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "No Backorder"
msgstr "Kein Lieferrückstand"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__no-message
msgid "No Message"
msgstr "Keine Nachricht"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "No Stock On Hand"
msgstr "Kein Bestand vorrätig"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "No allocation need found."
msgstr "Kein Zuweisungsbedarf gefunden."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_lot_report
msgid "No data yet!"
msgstr "Noch keine Daten vorhanden!"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No delivery to do!"
msgstr "Keine anstehende Lieferung!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "No negative quantities allowed"
msgstr "Keine negativen Mengen erlaubt"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
msgid "No operation made on this lot."
msgstr "Kein Vorgang wurde für dieses Los ausgeführt."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
msgid "No operations found. Let's create a transfer!"
msgstr "Keine Vorgänge gefunden. Erstellen Sie einen Transfer!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "No product found to generate Serials/Lots for."
msgstr ""
"Es wurde kein Produkt gefunden, für das eine Serien-/Losnummer generiert "
"werden kann."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "No product found. Let's create one!"
msgstr "Kein Produkt gefunden. Erstellen Sie eins!"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid ""
"No products to return (only lines in Done state and not fully returned yet "
"can be returned)."
msgstr ""
"Keine Produkte zu retournieren (es sind nur Positionen im Status Erledigt "
"und noch nicht bereits vorgenommene Retouren aktuell möglich)"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid "No putaway rule found. Let's create one!"
msgstr "Keine Einlagerungsregel gefunden. Erstellen Sie eine!"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No receipt yet! Create a new one."
msgstr "Noch kein Wareneingang! Erstellen Sie einen neuen."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid "No reordering rule found"
msgstr "Keine Nachbestellregeln gefunden"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"No rule has been found to replenish \"%(product)s\" in \"%(location)s\".\n"
"Verify the routes configuration on the product."
msgstr ""
"Es wurde keine Regel gefunden, um „%(product)s“ in „%(location)s“ aufzufüllen.\n"
"Überprüfen Sie die Routenkonfiguration des Produkts."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "No source location defined on stock rule: %s!"
msgstr "Kein Quellort in Bestandsregel definiert: %s!"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid "No stock move found"
msgstr "Keine Lagerbuchung gefunden"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_stock_view
msgid "No stock to show"
msgstr "Kein Bestand anzuzeigen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No transfer found. Let's create one!"
msgstr "Keine Bewegung gefunden. Erstellen Sie eine!"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__0
msgid "Normal"
msgstr "Normal"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
msgid "Not Available"
msgstr "Nicht verfügbar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Not Snoozed"
msgstr "Nicht auf Schlummermodus gesetzt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Note"
msgstr "Notiz"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__note
msgid "Notes"
msgstr "Notizen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Nothing to check the availability for."
msgstr "Es müssen keine Verfügbarkeiten geprüft werden."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__11
msgid "November"
msgstr "November"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Number of SN"
msgstr "Anzahl Seriennummern"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial_count
msgid "Number of SN/Lots"
msgstr "Anzahl Serien-/Losnummern"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_in
msgid "Number of incoming stock moves in the past 12 months"
msgstr "Anzahl der Eingangsbuchungen in den letzten 12 Monaten"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_out
msgid "Number of outgoing stock moves in the past 12 months"
msgstr "Anzahl der Ausgangsbuchungen in den letzten 12 Monaten"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__days_to_order
msgid "Numbers of days  in advance that replenishments demands are created."
msgstr ""
"Anzahl der Tage im Voraus, an denen Auffüllnachfragen erstellt werden."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__10
msgid "October"
msgstr "Oktober"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid ""
"Odoo opens a PDF preview by default. If you (Enterprise users only) want to print instantly,\n"
"                                        install the IoT App on a computer that is on the same local network as the\n"
"                                        barcode operator and configure the routing of the reports.\n"
"                                        <br/>"
msgstr ""
"Odoo öffnet die PDF-Vorschau standardmäßig. Wenn Sie (nur Enterprise-Benutzer) sofort drucken möchten,\n"
"                                        installieren Sie die IoT-App auf Ihrem Rechner, die sich auch demselben lokalen Netzwerk wie\n"
"                                        der Barcode-Operator befindet, und konfigurieren Sie das Routing der Berichte.\n"
"                                        <br/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Office Chair"
msgstr "Bürostuhl"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_quant__on_hand
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_on_hand
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_template_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "On Hand"
msgstr "Vorrätig"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_qty
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "On Hand Quantity"
msgstr "Vorrätige Menge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "On hand Quantity"
msgstr "Vorrätige Menge"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__available_quantity
msgid ""
"On hand quantity which hasn't been reserved on a transfer, in the default "
"unit of measure of the product"
msgstr ""
"Vorrätige Lagermenge, welche nicht für einen Transfer reserviert wurde. "
"Lagermenge ist in der Maßeinheit des Produktes."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_kanban_stock_view
msgid "On hand:"
msgstr "Vorrätig:"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__label_quantity__lots
msgid "One per lot/SN"
msgstr "Eins pro Los/SN"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__label_quantity__units
msgid "One per unit"
msgstr "Eins pro Einheit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Open"
msgstr "Offen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__move_quantity__move
msgid "Operation Quantities"
msgstr "Vorgangsmenge"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Operation Type"
msgstr "Vorgangsart"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__return_picking_type_id
msgid "Operation Type for Returns"
msgstr "Vorgangsart für Retouren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking_type_label
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Operation Types"
msgstr "Vorgangsarten"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_quant.py:0
msgid "Operation not supported"
msgstr "Vorgang nicht unterstützt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_id
msgid "Operation type"
msgstr "Vorgangsart"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking_type_label
msgid "Operation type (PDF)"
msgstr "Vorgangsart (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking_type
msgid "Operation type (ZPL)"
msgstr "Vorgangsart (ZPL)"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids
#: model:ir.ui.menu,name:stock.menu_stock_warehouse_mgmt
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Operations"
msgstr "Vorgänge"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_type_list
#: model:ir.ui.menu,name:stock.menu_pickingtype
msgid "Operations Types"
msgstr "Vorgangsarten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr "Vorgänge ohne Paket"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Optimize your transfers by grouping operations together and assigning jobs "
"to workers"
msgstr ""
"Optimieren Sie Transfers, indem Sie Vorgänge gruppieren und Arbeitern "
"Aufgaben zuweisen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__partner_id
msgid ""
"Optional address where goods are to be delivered, specifically used for "
"allotment"
msgstr ""
"Optionale Lieferadresse, die insbesondere für Konsignationen verwendet wird."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__posx
#: model:ir.model.fields,help:stock.field_stock_location__posy
#: model:ir.model.fields,help:stock.field_stock_location__posz
msgid "Optional localization details, for information purpose only"
msgstr "Optionale Einzelheiten zum Standort, nur zu Informationszwecken"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__returned_move_ids
msgid "Optional: all returned moves created from this move"
msgstr "Optional: Alle Rücksendungen dieser Lieferung"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_dest_ids
msgid "Optional: next stock move when chaining them"
msgstr "Optional: nächste Lagerbuchung bei definierter Lieferkette"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_orig_ids
msgid "Optional: previous stock move when chaining them"
msgstr "Optional: vorherige Lagerbuchung bei definierter Lieferkette"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Options"
msgstr "Optionen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_warning_view
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Order"
msgstr "Bestellen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
msgid "Order To Max"
msgstr "Maximum bestellen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Order signed"
msgstr "Auftrag unterschrieben"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Order signed by %s"
msgstr "Auftrag unterschrieben von %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Ordered"
msgstr "Bestellt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__orderpoint_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__orderpoint_id
msgid "Orderpoint"
msgstr "Beschaffungspunkt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin"
msgstr "Herkunft"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin Moves"
msgstr "Ursprüngliche Buchung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin_returned_move_id
msgid "Origin return move"
msgstr "Ursprüngliche Rücksendung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_orig_ids
msgid "Original Move"
msgstr "Ursprüngliche Buchung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__orderpoint_id
msgid "Original Reordering Rule"
msgstr "Ursprüngliche Nachbestellregel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Other Information"
msgstr "Weitere Informationen"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Our invoices are payable within 21 working days, unless another payment "
"timeframe is indicated on either the invoice or the order. In the event of "
"non-payment by the due date, My Company (Chicago) reserves the right to "
"request a fixed interest payment amounting to 10% of the sum remaining due. "
"My Company (Chicago) will be authorized to suspend any provision of services"
" without prior warning in the event of late payment."
msgstr ""
"Unsere Rechnungen sind innerhalb von 21 Werktagen zahlbar, es sei denn, auf "
"der Rechnung oder der Bestellung ist eine andere Zahlungsfrist angegeben. "
"Bei nicht fristgerechter Zahlung behält sich My Company (Chicago) das Recht "
"vor, einen festen Zinssatz in Höhe von 10 % der noch ausstehenden Summe zu "
"verlangen. My Company (Chicago) ist berechtigt, bei Zahlungsverzug die "
"Erbringung von Dienstleistungen ohne Vorwarnung einzustellen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__out_type_id
msgid "Out Type"
msgstr "Auslieferung"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_product_product__outgoing_qty
#: model:ir.model.fields,field_description:stock.field_product_template__outgoing_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Outgoing"
msgstr "Ausgang"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Outgoing Draft Transfer"
msgstr "Entwurf des Ausgangstransfers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__outgoing_move_line_ids
msgid "Outgoing Move Line"
msgstr "Ausgangsbuchungszeile"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_steps
msgid "Outgoing Shipments"
msgstr "Warenausgänge"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Output"
msgstr "Ausgang"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_output_stock_loc_id
msgid "Output Location"
msgstr "Ausgangslager"

#. module: stock
#: model:ir.ui.menu,name:stock.stock_picking_type_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Overview"
msgstr "Übersicht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__owner_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Owner"
msgstr "Eigentümer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__restrict_partner_id
msgid "Owner "
msgstr "Eigentümer "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Owner:"
msgstr "Eigentümer:"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "P&L Qty"
msgstr "Anzahl G&V"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__package_label_to_print__pdf
msgid "PDF"
msgstr "PDF"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Pack"
msgstr "Verpacken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__pack_date
msgid "Pack Date"
msgstr "Verpackungsdatum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Pack Date Demo"
msgstr "Demo-Verpackungsdatum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Pack Date:"
msgstr "Verpackungsdatum:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pack_type_id
msgid "Pack Type"
msgstr "Verpackungstyp"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__package_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__package_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__package_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__package
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_tree
msgid "Package"
msgstr "Paket"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Package A"
msgstr "Paket A"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Package B"
msgstr "Paket B"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode_small
msgid "Package Barcode (PDF)"
msgstr "Paketbarcode (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_package_template
msgid "Package Barcode (ZPL)"
msgstr "Paketbarcode (ZPL)"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode
msgid "Package Barcode with Contents"
msgstr "Paketbarcode mit Inhalten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__package_capacity_ids
msgid "Package Capacity"
msgstr "Paketkapazität"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_package_level.py:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Package Content"
msgstr "Paketinhalt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Package Label"
msgstr "Paketetikett"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__package_label_to_print
msgid "Package Label to Print"
msgstr "Zu druckendes Paketetikett"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids
msgid "Package Level"
msgstr "Paketlevel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids_details
msgid "Package Level Ids Details"
msgstr "Details zu den Paketlevel-IDs"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Package Name"
msgstr "Paketname"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__name
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Reference"
msgstr "Paketreferenz"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Transfers"
msgstr "Pakettransfers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_packaging__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__package_type_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__package_type_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Package Type"
msgstr "Pakettyp"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Package Type Demo"
msgstr "Demo-Pakettyp"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Package Type:"
msgstr "Pakettyp:"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_type_view
#: model:ir.ui.menu,name:stock.menu_packaging_types
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_tree
msgid "Package Types"
msgstr "Pakettypen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_use
msgid "Package Use"
msgstr "Paketverwendung"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Package manually relocated"
msgstr "Paket manuell umgelagert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__valid_sscc
msgid "Package name is valid SSCC"
msgstr "Paketname ist gültiger SSCC"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Package type"
msgstr "Pakettyp"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_view
#: model:ir.actions.report,name:stock.action_report_picking_packages
#: model:ir.model,name:stock.model_stock_quant_package
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_lot
#: model:ir.ui.menu,name:stock.menu_package
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Packages"
msgstr "Pakete"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid ""
"Packages are usually created via transfers (during pack operation) and can contain different products.\n"
"                Once created, the whole package can be moved at once, or products can be unpacked and moved as single units again."
msgstr ""
"Pakete werden in der Regel durch Transfers (während des Packvorgangs) erstellt und können verschiedene Produkte enthalten.\n"
"Nach der Erstellung kann das gesamte Paket auf einmal befördert werden, oder die Produkte können ausgepackt und als einzelne Einheiten weiter transportiert werden."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_id
msgid "Packaging"
msgstr "Verpackung"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__height
msgid "Packaging Height"
msgstr "Pakethöhe"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__packaging_length
msgid "Packaging Length"
msgstr "Paketlänge"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__width
msgid "Packaging Width"
msgstr "Paketbreite"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__packaging_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Packagings"
msgstr "Verpackungen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_pack_stock_loc_id
msgid "Packing Location"
msgstr "Verpackungsort"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Packing Zone"
msgstr "Packzone"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Pallet"
msgstr "Palette"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__location_id
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Parent Location"
msgstr "Übergeordneter Lagerort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__parent_path
msgid "Parent Path"
msgstr "Übergeordneter Pfad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__parent_route_ids
msgid "Parent Routes"
msgstr "Übergeordnete Routen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__direct
msgid "Partial"
msgstr "Teilweise"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__partial_package_names
msgid "Partial Package Names"
msgstr "Namen der Teillieferungen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__partially_available
msgid "Partially Available"
msgstr "Teilweise verfügbar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__partner_id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Partner"
msgstr "Partner"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__partner_address_id
msgid "Partner Address"
msgstr "Partneradresse"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_action_inventory_tree
msgid "Physical Inventory"
msgstr "Bestandsaufnahme"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__pick_ids
msgid "Pick"
msgstr "Kommissionieren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quant_id
msgid "Pick From"
msgstr "Nehmen aus"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pick_type_id
msgid "Pick Type"
msgstr "Kommissionierart"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_ship
msgid "Pick then Deliver (2 steps)"
msgstr "Kommissionierung, dann Lieferung (2 Schritte)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_pack_ship
msgid "Pick, Pack, then Deliver (3 steps)"
msgstr "Kommissionierung, Verpackung, dann Lieferung (3 Schritte)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picked
msgid "Picked"
msgstr "Kommissioniert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_picking_label_type__picking_ids
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__picking_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Picking"
msgstr "Kommissionierung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking Lists"
msgstr "Kommissionierlisten"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking
msgid "Picking Operations"
msgstr "Kommissioniervorgänge"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__picking_properties_definition
msgid "Picking Properties"
msgstr "Kommissionierungseigenschaften"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking_type
msgid "Picking Type"
msgstr "Kommissionierart"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_code_domain
msgid "Picking Type Code Domain"
msgstr "Codebereich für Kommissionierarten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Picking list"
msgstr "Kommissionierliste"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "Planning Issue"
msgstr "Problem bei der Planung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Planning Issues"
msgstr "Probleme bei der Planung"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Please create a warehouse for company %s."
msgstr "Bitte erstellen Sie ein Lagerhaus für das Unternehmen %s."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid ""
"Please put this document inside your return parcel.<br/>\n"
"                                Your parcel must be sent to this address:"
msgstr ""
"Bitte legen Sie dieses Dokument in Ihr Retourenpaket.<br/>\n"
"                                Ihr Paket muss an diese Adresse geschickt werden:"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Please specify at least one non-zero quantity."
msgstr "Bitte geben Sie wenigstens eine Menge ungleich 0 an."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "Preceding operations"
msgstr "Vorangegangene Vorgänge"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__route_id
#: model:ir.model.fields,field_description:stock.field_stock_replenish_mixin__route_id
msgid "Preferred Route"
msgstr "Bevorzugte Route"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__route_ids
msgid "Preferred route"
msgstr "Bevorzugte Route"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Presence depends on the type of operation."
msgstr "Anwesenheit hängt von der Art des Vorgangs ab."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Press the \"New\" button to define the quantity for a product in your stock "
"or import quantities from a spreadsheet via the Actions menu"
msgstr ""
"Drücken Sie auf die Schaltfläche „Neu“, um die Mengen für ein Produkt in "
"Ihrem Lager zu definieren oder importieren Sie Mengen über eine "
"Tabellenkalkulation über das Aktionsmenü."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Print"
msgstr "Drucken"

#. module: stock
#: model:res.groups,name:stock.group_stock_lot_print_gs1
msgid "Print GS1 Barcodes for Lot & Serial Numbers"
msgstr "GS1-Barcodes für Los- & Seriennummern drucken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_lot_print_gs1
msgid "Print GS1 Barcodes for Lots & Serial Numbers"
msgstr "GS1-Barcodes für Los- & Seriennummern drucken"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Print Label"
msgstr "Etikett drucken"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
msgid "Print Labels"
msgstr "Etiketten drucken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print label as:"
msgstr "Etikett drucken als:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print on \"Put in Pack\""
msgstr "Bei „Verpacken“ drucken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print on Validation"
msgstr "Bei Validierung drucken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__printed
msgid "Printed"
msgstr "Gedruckt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__priority
#: model:ir.model.fields,field_description:stock.field_stock_picking__priority
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sequence
msgid "Priority"
msgstr "Priorität"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__delay_alert_date
msgid "Process at this date to be on time"
msgstr "An diesem Datum bearbeiten, um pünktlich zu sein"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations faster with barcodes"
msgstr "Führen Sie Vorgänge mit Barcodes schneller durch"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_procurement
msgid "Procurement"
msgstr "Beschaffung"

#. module: stock
#: model:ir.model,name:stock.model_procurement_group
#: model:ir.model.fields,field_description:stock.field_stock_move__group_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__group_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__group_id
msgid "Procurement Group"
msgstr "Beschaffungsgruppe"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
msgid "Procurement group"
msgstr "Beschaffungsgruppe"

#. module: stock
#: model:ir.actions.server,name:stock.ir_cron_scheduler_action_ir_actions_server
#: model:ir.ui.menu,name:stock.menu_procurement_compute
msgid "Procurement: run scheduler"
msgstr "Beschaffung: Planer starten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__produce_line_ids
msgid "Produce Line"
msgstr "Position erstellen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Produced Qty"
msgstr "Produzierte Menge"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model,name:stock.model_product_template
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__product_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__product_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__product_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_id
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Product"
msgstr "Produkt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability
msgid "Product Availability"
msgstr "Produktverfügbarkeit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__product_capacity_ids
msgid "Product Capacity"
msgstr "Produktkapazität"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__categ_ids
#: model:ir.ui.menu,name:stock.menu_product_category_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Product Categories"
msgstr "Produktkategorien"

#. module: stock
#: model:ir.model,name:stock.model_product_category
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_category_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_categ_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_category_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Product Category"
msgstr "Produktkategorie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_report_label
msgid "Product Display Name"
msgstr "Anzeigename des Produkts"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_product
msgid "Product Label (ZPL)"
msgstr "Produktetikett (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__product_label_format
msgid "Product Label Format to auto-print"
msgstr "Format der automatisch zu druckenden Produktetiketten"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_product_product_view
msgid "Product Label Report"
msgstr "Produktetikettenbericht"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__picking_label_type__label_type__products
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Product Labels"
msgstr "Produktetiketten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots Filter"
msgstr "Filter für Produktlose"

#. module: stock
#: model:ir.model,name:stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Produktbewegungen (Lagerbuchung)"

#. module: stock
#: model:ir.model,name:stock.model_product_packaging
msgid "Product Packaging"
msgstr "Produktverpackung"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_packaging
msgid "Product Packaging (ZPL)"
msgstr "Produktverpackung (ZPL)"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_product_packagings
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Product Packagings"
msgstr "Produktverpackungen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Product Quantity Confirmed"
msgstr "Produktmenge bestätigt"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Product Quantity Updated"
msgstr "Aktualisierte Produktmenge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "Product Relocated"
msgstr "Produkt umgelagert"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.js:0
#: model:ir.model,name:stock.model_product_replenish
msgid "Product Replenish"
msgstr "Produkt auffüllen"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "Mixin zur Produktauffüllung"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_stock_rule
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Product Routes Report"
msgstr "Bericht über Produktrouten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_tmpl_id
msgid "Product Template"
msgstr "Produktvorlage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_tmpl_id
msgid "Product Tmpl"
msgstr "Produktvorlage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__tracking
msgid "Product Tracking"
msgstr "Produktverfolgung"

#. module: stock
#: model:ir.model,name:stock.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Maßeinheit des Produkts"

#. module: stock
#: model:ir.model,name:stock.model_product_product
msgid "Product Variant"
msgstr "Produktvariante"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_product_normal_action
#: model:ir.ui.menu,name:stock.product_product_menu
msgid "Product Variants"
msgstr "Produktvarianten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Product barcode"
msgstr "Produktbarcode"

#. module: stock
#. odoo-python
#: code:addons/stock/report/product_label_report.py:0
msgid "Product model not defined, Please contact your administrator."
msgstr ""
"Produktmodell nicht definiert, bitte wenden Sie sich an Ihren Administrator."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid ""
"Product this lot/serial number contains. You cannot change it anymore if it "
"has already been moved."
msgstr ""
"Produkt, das diese Los-/Seriennummer enthält. Sie können das Produkt nicht "
"mehr ändern, wenn es bereits bewegt wurde."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom_name
msgid "Product unit of measure label"
msgstr "Maßeinheit des Produktetiketts"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__has_tracking
msgid "Product with Tracking"
msgstr "Produkt mit Verfolgung"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__production
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production"
msgstr "Produktion"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_production
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_production
msgid "Production Location"
msgstr "Produktionsstandort"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production Locations"
msgstr "Produktionsstandorte"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_quantity_history.py:0
#: model:ir.actions.act_window,name:stock.act_product_location_open
#: model:ir.actions.act_window,name:stock.product_template_action_product
#: model:ir.model.fields,field_description:stock.field_stock_route__product_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__product_ids
#: model:ir.ui.menu,name:stock.menu_product_in_config_stock
#: model:ir.ui.menu,name:stock.menu_product_variant_config_stock
#: model:ir.ui.menu,name:stock.menu_stock_inventory_control
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Products"
msgstr "Produkte"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability_state
msgid "Products Availability State"
msgstr "Status der Produktverfügbarkeit"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__priority
msgid ""
"Products will be reserved first for the transfers with the highest "
"priorities."
msgstr ""
"Die Produkte werden zuerst für die Transfers mit den höchsten Prioritäten "
"reserviert."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Products: %(location)s"
msgstr "Produkte: %(location)s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__propagate
msgid "Propagate"
msgstr "Übertragen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__propagate_cancel
msgid "Propagate cancel and split"
msgstr "Abbruch und Aufteilung übertragen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Propagation"
msgstr "Übertragung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_propagation_option
msgid "Propagation of Procurement Group"
msgstr "Übertragung der Beschaffungsgruppe"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_carrier
msgid "Propagation of carrier"
msgstr "Übertragung des Zustellers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__lot_properties
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_properties
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_properties
msgid "Properties"
msgstr "Eigenschaften"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull_push
msgid "Pull & Push"
msgstr "Pull & Push"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull
msgid "Pull From"
msgstr "Nehmen von"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Pull Rule"
msgstr "Pull-Regel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__push_domain
msgid "Push Applicability"
msgstr "Push-Anwendbarkeit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Push Rule"
msgstr "Push-Regel"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__push
msgid "Push To"
msgstr "Schieben nach"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_detailed_operation_tree
msgid "Put in Pack"
msgstr "Verpacken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Put your products in packs (e.g. parcels, boxes) and track them"
msgstr ""
"Packen Sie Ihre Produkte in Verpackungen (z. B. Pakete, Kisten) und "
"verfolgen Sie sie"

#. module: stock
#: model:ir.model,name:stock.model_stock_putaway_rule
msgid "Putaway Rule"
msgstr "Einlagerungsregel"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: model:ir.actions.act_window,name:stock.category_open_putaway
#: model:ir.actions.act_window,name:stock.location_open_putaway
#: model:ir.model.fields,field_description:stock.field_product_category__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_product_product__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__putaway_rule_ids
#: model:ir.ui.menu,name:stock.menu_putaway
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Putaway Rules"
msgstr "Einlagerungsregeln"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Putaway:"
msgstr "Einlagerung:"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_putaway_tree
msgid "Putaways Rules"
msgstr "Einlagerungsregeln"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_qty_multiple_check
msgid "Qty Multiple must be greater than or equal to zero."
msgstr "Das Vielfache der Menge muss größer oder gleich Null sein."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control
msgid "Quality"
msgstr "Qualität"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Quality Control"
msgstr "Qualitätskontrolle"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_qc_stock_loc_id
msgid "Quality Control Location"
msgstr "Qualitätskontrolle"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__qc_type_id
msgid "Quality Control Type"
msgstr "Qualitätskontrolltyp"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "Arbeitsblatt für Qualitätsprüfung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_request_count__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quant_ids
msgid "Quant"
msgstr "Quant"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quant's creation is restricted, you can't do this operation."
msgstr ""
"Quant-Erstellung ist eingeschränkt, Sie können diesen Vorgang nicht "
"durchführen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quant's editing is restricted, you can't do this operation."
msgstr ""
"Quant-Bearbeitung ist eingeschränkt, Sie können diesen Vorgang nicht "
"durchführen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities Already Set"
msgstr "Bereits bestimmte Mengen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities To Reset"
msgstr "Zurückzusetzende Menge"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities unpacked"
msgstr "Ausgepackte Mengen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_product_replenish__quantity
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__quantity
#: model:ir.model.fields,field_description:stock.field_stock_move__quantity
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_quant__quantity
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_qty
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quantity
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Quantity"
msgstr "Menge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Quantity Multiple"
msgstr "Mehrfache Menge"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__qty_available
#: model:ir.model.fields,field_description:stock.field_product_template__qty_available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity On Hand"
msgstr "Vorrätige Menge"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Quantity Received"
msgstr "Erhaltene Menge"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantity Relocated"
msgstr "Menge umgelagert"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity Reserved"
msgstr "Reservierter Bestand"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid "Quantity available too low"
msgstr "Verfügbare Menge zu niedrig"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_change_product_qty.py:0
msgid "Quantity cannot be negative."
msgstr "Menge darf nicht negativ sein"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_outdated
msgid "Quantity has been moved since last count"
msgstr "Menge wurde seit der letzten Zählung bewegt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quantity_product_uom
msgid "Quantity in Product UoM"
msgstr "Menge in Produkt-ME"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__availability
msgid "Quantity in stock that can still be reserved for this move"
msgstr "Bestand der noch für diese Lieferung reserviert werden kann"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_qty
msgid "Quantity in the default UoM of the product"
msgstr "Menge in der Standard-Maßeinheit des Produkts"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr ""
"Menge der geplanten eingehenden Produkte.\n"
"In einem Kontext mit einem einzigen Lagerort umfasst dies die Waren, die an diesem Lagerort oder untergeordneten Lagern ankommen.\n"
"In einem Kontext mit einem einzigen Lagerhaus umfasst dies die Waren, die am Lagerort dieses Lagerhauses oder untergeordneten Lagern ankommen.\n"
"Andernfalls umfasst dies die Waren, die an einem Lagerort mit „internem“ Typ ankommen."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr ""
"Menge der geplanten ausgehenden Produkte.\n"
"In einem Kontext mit einem einzigen Lagerort umfasst dies die Waren, die diesen Lagerort oder das untergeordnete Lager verlassen.\n"
"In einem Kontext mit einem einzigen Lagerhaus umfasst dies die Waren, die den Lagerort dieses Lagerhauses oder das untergeordnete Lager verlassen.\n"
"Andernfalls umfasst dies die Waren, die einen Lagerort mit „internem“ Typ verlassen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__quantity
msgid ""
"Quantity of products in this quant, in the default unit of measure of the "
"product"
msgstr "Produktmenge in diesem Quant, in der Standard-Maßeinheit des Produkts"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__reserved_quantity
msgid ""
"Quantity of reserved products in this quant, in the default unit of measure "
"of the product"
msgstr ""
"Menge der reservierten Produkte in diesem Quant, in der Standard-Maßeinheit "
"des Produkts"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantity or Reserved Quantity should be set."
msgstr "Menge oder reservierte Menge sollte eingestellt sein."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Quantity per Lot"
msgstr "Menge pro Los"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_positive_quantity
msgid "Quantity should be a positive number."
msgstr "Menge sollte ein positiver Wert sein."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__label_quantity
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_quantity
msgid "Quantity to print"
msgstr "Zu druckende Menge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Quantity:"
msgstr "Menge:"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_lot__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__quant_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Quants"
msgstr "Quanten"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Quants are auto-deleted when appropriate. If you must manually delete them, "
"please ask a stock manager to do it."
msgstr ""
"Quants werden bei Bedarf automatisch gelöscht. Wenn Sie sie manuell löschen "
"müssen, bitten Sie bitte einen Lagerverwalter, dies zu tun."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quants cannot be created for consumables or services."
msgstr ""
"Quanten können nicht für Verbrauchsartikel oder Dienstleistungen erstellt "
"werden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "RETURN OF"
msgstr "RETOURE VON"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__rating_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__rating_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__rating_ids
msgid "Ratings"
msgstr "Bewertungen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__assigned
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Ready"
msgstr "Bereit"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_ready_moves
msgid "Ready Moves"
msgstr "Bereite Bewegungen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_qty
msgid "Real Quantity"
msgstr "Tatsächliche Menge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Reason"
msgstr "Grund"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__message
msgid "Reason for relocation"
msgstr "Grund für Umlagerung"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__incoming
msgid "Receipt"
msgstr "Eingang"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_route_id
msgid "Receipt Route"
msgstr "Eingangsroute"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_incoming
#: model:ir.ui.menu,name:stock.in_picking
#: model:stock.picking.type,name:stock.picking_type_in
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Receipts"
msgstr "Eingänge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Receive From"
msgstr "Erhalten von"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__one_step
msgid "Receive and Store (1 step)"
msgstr "Erhalt und Lagerung (1 Schritt)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 1 step (stock)"
msgstr "In 1 Schritt erhalten (Lager)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 2 steps (input + stock)"
msgstr "In 2 Schritten erhalten (Eingang + Bestand)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 3 steps (input + quality + stock)"
msgstr "In 3 Schritten erhalten (Eingang + Qualität + Bestand)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__two_steps
msgid "Receive then Store (2 steps)"
msgstr "Erhalt, dann Lagerung (2 Schritte)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__three_steps
msgid "Receive, Quality Control, then Store (3 steps)"
msgstr "Erhalt, Qualitätskontrolle, dann Lagerung (3 Schritte)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Received Qty"
msgstr "Erhaltene Menge"

#. module: stock
#: model:ir.actions.client,name:stock.stock_reception_action
#: model:ir.actions.report,name:stock.stock_reception_report_action
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_reception_report
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reception Report"
msgstr "Empfangsbericht"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking
msgid "Reception Report Label"
msgstr "Empfangsberichtsetikett"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reception Report Labels"
msgstr "Empfangsberichtsetiketten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid ""
"Reduce stockouts with alerts, barcode app, replenishment propositions,\n"
"                        locations management traceability, quality control, etc."
msgstr ""
"Reduzieren Sie Fehlbestände mit Warnungen, Barcode-App, Auffüllvorschlägen,\n"
"                        Rückverfolgbarkeit der Standorte, Qualitätskontrolle usw."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_procurement_group__name
#: model:ir.model.fields,field_description:stock.field_stock_move__reference
#: model:ir.model.fields,field_description:stock.field_stock_move_line__reference
#: model:ir.model.fields,field_description:stock.field_stock_picking__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__name
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Reference"
msgstr "Referenz"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_id
msgid "Reference Sequence"
msgstr "Sequenz"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_picking_name_uniq
msgid "Reference must be unique per company!"
msgstr "Die Referenz muss pro Unternehmen eindeutig sein!"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__origin
msgid "Reference of the document"
msgstr "Referenz eines Dokuments"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Reference:"
msgstr "Referenz:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__stock_move_ids
msgid "Related Stock Moves"
msgstr "Zugehörige Lagerbuchungen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Relocate"
msgstr "Umlagern"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "Relocate your stock"
msgstr "Ihren Bestand umlagern"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Remaining parts of picking partially processed"
msgstr "Restmengen von teilweise bearbeiteten Kommissionierungen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_removal
msgid "Removal"
msgstr "Entnahme"

#. module: stock
#: model:ir.model,name:stock.model_product_removal
#: model:ir.model.fields,field_description:stock.field_stock_location__removal_strategy_id
msgid "Removal Strategy"
msgstr "Entnahmestrategie"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Removal strategy %s not implemented."
msgstr "Diese Entnahmestrategie %s ist noch nicht implementiert."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid ""
"Remove manually entered value and replace by the quantity to order based on "
"the forecasted quantities"
msgstr ""
"Entfernen Sie den manuell eingegebenen Wert und ersetzen Sie ihn durch die "
"zu bestellende Menge auf der Grundlage der prognostizierten Mengen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_max_qty
msgid "Reordering Max Qty"
msgstr "Maximale Nachbestellmenge"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_min_qty
msgid "Reordering Min Qty"
msgstr "Minimale Nachbestellmenge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rule"
msgstr "Nachbestellregel"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_orderpoint
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_reordering_rules
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_reordering_rules
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Reordering Rules"
msgstr "Nachbestellregeln"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules Search"
msgstr "Suche nach Nachbestellregeln"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.xml:0
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Replenish"
msgstr "Auffüllen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__replenish_location
msgid "Replenish Location"
msgstr "Standort auffüllen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__should_replenish
msgid "Replenish Quantities"
msgstr "Mengen auffüllen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.route,name:stock.route_warehouse0_mto
msgid "Replenish on Order (MTO)"
msgstr "Auffüllung nach Auftrag (Auftragsfertigung)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "Replenish wizard"
msgstr "Assistent zum Auffüllen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_replenish
#: model:ir.actions.server,name:stock.action_replenishment
#: model:ir.ui.menu,name:stock.menu_reordering_rules_replenish
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Replenishment"
msgstr "Auffüllung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__replenishment_info_id
msgid "Replenishment Info"
msgstr "Aufüllinfo"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_replenishment_info
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Replenishment Information"
msgstr "Auffüllinformationen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Replenishment Information for %(product)s in %(warehouse)s"
msgstr "Auffüllinformationen für %(product)s in %(warehouse)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Replenishment Report"
msgstr "Auffüllbericht"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Replenishment Report Search"
msgstr "Auffüllberichtssuche"

#. module: stock
#: model:ir.model,name:stock.model_ir_actions_report
msgid "Report Action"
msgstr "Berichtsaktion"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "Report Printing Error"
msgstr "Fehler bei Berichtsdruck"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_report
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Reporting"
msgstr "Berichtswesen"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_request_count
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Request a Count"
msgstr "Zählung anfordern"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Request your vendors to deliver to your customers"
msgstr "Bitten Sie Ihre Lieferanten, direkt an Ihre Kunden zu liefern"

#. module: stock
#: model:res.groups,name:stock.group_stock_sign_delivery
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Require a signature on your delivery orders"
msgstr "Verlangen Sie eine Unterschrift für Ihre Lieferaufträge"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_method
msgid "Reservation Method"
msgstr "Reservierungsmethode"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Reservations"
msgstr "Reservierungen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Reserve"
msgstr "Reservieren"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__full
msgid "Reserve Only Full Packagings"
msgstr "Nur volle Verpackungseinheiten reservieren"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__packaging_reserve_method
msgid ""
"Reserve Only Full Packagings: will not reserve partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then only 1000 will be reserved\n"
"Reserve Partial Packagings: allow reserving partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then 1600 will be reserved"
msgstr ""
"Nur volle Verpackungseinheiten reservieren: Teilverpackungen werden nicht reserviert. Wenn ein Kunde 2 Paletten mit je 1000 Einheiten bestellt und Sie nur 1600 auf Lager haben, werden nur 1000 reserviert.\n"
"Teilverpackungen reservieren: erlaubt die Reservierung von Teilverpackungen. Wenn der Kunde 2 Paletten zu je 1000 Stück bestellt und Sie nur 1600 auf Lager haben, werden 1600 reserviert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__packaging_reserve_method
msgid "Reserve Packagings"
msgstr "Verpackungen reservieren"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__partial
msgid "Reserve Partial Packagings"
msgstr "Teilverpackungen reservieren"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__assigned
msgid "Reserved"
msgstr "Reserviert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_qty
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_packaging_qty
msgid "Reserved Packaging Quantity"
msgstr "Menge der reservierten Verpackung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__reserved_quantity
msgid "Reserved Quantity"
msgstr "Reservierte Menge"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "Reserving a negative quantity is not allowed."
msgstr "Reservieren einer negative Menge ist nicht erlaubt."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__responsible_id
#: model:ir.model.fields,field_description:stock.field_product_template__responsible_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__user_id
msgid "Responsible"
msgstr "Verantwortlich"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_user_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_user_id
msgid "Responsible User"
msgstr "Verantwortlicher Benutzer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Resupply"
msgstr "Beschaffung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_wh_ids
msgid "Resupply From"
msgstr "Nachlieferung aus"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__warehouseinfo_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_route_ids
msgid "Resupply Routes"
msgstr "Beschaffungsroute"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_return_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return"
msgstr "Retoure ausführen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return All"
msgstr "Alle retournieren"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Rücksendung"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "Rücksendungsposten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Return Slip"
msgstr "Retourenschein"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return for Exchange"
msgstr "Retoure zum Austausch"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_id
msgid "Return of"
msgstr "Retoure von"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Return of %(picking_name)s"
msgstr "Retoure von %(picking_name)s"

#. module: stock
#: model:ir.actions.report,name:stock.return_label_report
msgid "Return slip"
msgstr "Retourenschein"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Returned Picking"
msgstr "Retournierte Sendung"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_ids
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Returns"
msgstr "Retouren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Returns Type"
msgstr "Retourentyp"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__reusable
msgid "Reusable Box"
msgstr "Wiederverwendbare Box"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__package_use
msgid ""
"Reusable boxes are used for batch picking and emptied afterwards to be reused. In the barcode application, scanning a reusable box will add the products in this box.\n"
"        Disposable boxes aren't reused, when scanning a disposable box in the barcode application, the contained products are added to the transfer."
msgstr ""
"Wiederverwendbare Boxen werden für die Stapelkommissionierung verwendet und anschließend geleert, um wiederverwendet zu werden. Wenn in der Barcode-Anwendung eine Mehrwegbox gescannt wird, werden die Produkte in dieser Box hinzugefügt.\n"
"Einwegboxen werden nicht wiederverwendet. Wenn Sie eine Einwegbox in der Barcode-Anwendung scannen, werden die darin enthaltenen Produkte dem Transfer hinzugefügt."

#. module: stock
#: model:ir.actions.server,name:stock.action_revert_inventory_adjustment
msgid "Revert Inventory Adjustment"
msgstr "Bestandsaufnahme zurücksetzen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__route_id
#: model:ir.model.fields,field_description:stock.field_stock_route__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__route_id
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Route"
msgstr "Route"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_company_id
msgid "Route Company"
msgstr "Route des Unternehmen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_sequence
msgid "Route Sequence"
msgstr "Routensequenz"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_routes_form
#: model:ir.actions.server,name:stock.action_open_routes
#: model:ir.model.fields,field_description:stock.field_product_category__route_ids
#: model:ir.model.fields,field_description:stock.field_product_packaging__route_ids
#: model:ir.model.fields,field_description:stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__route_ids
#: model:ir.ui.menu,name:stock.menu_routes_config
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Routes"
msgstr "Routen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__has_available_route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__has_available_route_ids
msgid "Routes can be selected on this product"
msgstr "Routen können für dieses Produkt ausgewählt werden."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_wh_ids
msgid ""
"Routes will be created automatically to resupply this warehouse from the "
"warehouses ticked"
msgstr ""
"Es werden automatisch Routen erstellt, um dieses Lagerhaus aus den "
"ausgewählten Lagerhäusern heraus zu versorgen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_replenishment_info__warehouseinfo_ids
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_route_ids
msgid ""
"Routes will be created for these resupply warehouses and you can select them"
" on products and product categories"
msgstr ""
"Routen werden auch für die Beschaffung aus eigenen Lagerhäusern definiert "
"und können bei Produkten oder Produktkategorien ausgewählt werden."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"Rule %(rule)s belongs to %(rule_company)s while the route belongs to "
"%(route_company)s."
msgstr ""
"Regel %(rule)s gehört zu %(rule_company)s während die Route zu "
"%(route_company)s gehört."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__rule_message
msgid "Rule Message"
msgstr "Regelnachricht"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_rules_form
#: model:ir.model.fields,field_description:stock.field_stock_route__rule_ids
#: model:ir.ui.menu,name:stock.menu_action_rules_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_tree
msgid "Rules"
msgstr "Regeln"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Categories"
msgstr "Regeln für Kategorien"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Products"
msgstr "Regeln für Produkte"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__rule_ids
msgid "Rules used"
msgstr "Angewendete Regeln"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "S0001"
msgstr "S0001"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_sms
msgid "SMS Confirmation"
msgstr "SMS-Bestätigung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-Zustellungsfehler"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "SSCC Demo"
msgstr "Demo-SSCC"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "SSCC:"
msgstr "SSCC:"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "STANDARD TERMS AND CONDITIONS OF SALE"
msgstr "ALLGEMEINE VERKAUFSBEDINGUNGEN"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Sales History"
msgstr "Verkaufshistorie"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Sample data"
msgstr "Beispieldaten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__date_planned
#: model:ir.model.fields,field_description:stock.field_stock_move_line__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Scheduled Date"
msgstr "Geplantes Datum"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date
#: model:ir.model.fields,help:stock.field_stock_move_line__scheduled_date
msgid "Scheduled date until move is done, then date of actual move processing"
msgstr ""
"Geplantes Datum bis zur Durchführung der Bewegung, dann Datum der "
"tatsächlichen Bewegungsabwicklung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Scheduled or processing date"
msgstr "Geplantes/tatsächliches Verarbeitungsdatum"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__scheduled_date
msgid ""
"Scheduled time for the first part of the shipment to be processed. Setting "
"manually a value here would set it as expected date for all the stock moves."
msgstr ""
"Geplantes Datum für die Auslieferung der ersten Lieferpositionen. Ein "
"manueller Wert überträgt dieses Datum auch auf alle anderen Lagerbuchungen."

#. module: stock
#: model:ir.actions.server,name:stock.action_scrap
#: model:ir.model,name:stock.model_stock_scrap
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__scrap_id
#: model:ir.ui.menu,name:stock.menu_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
msgid "Scrap"
msgstr "Ausschuss melden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Scrap Location"
msgstr "Lagerort für Ausschuss"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_scrap
msgid "Scrap Orders"
msgstr "Ausschuss melden"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
msgid "Scrap Products"
msgstr "Produkte ausrangieren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_reason_tag_ids
msgid "Scrap Reason"
msgstr "Ausschussgrund"

#. module: stock
#: model:ir.model,name:stock.model_stock_scrap_reason_tag
msgid "Scrap Reason Tag"
msgstr "Stichwort für Ausschussgrund"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrap_id
msgid "Scrap operation"
msgstr "Ausschussvorgang"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid "Scrap products"
msgstr "Produkte ausrangieren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrapped
msgid "Scrapped"
msgstr "Ausschuss gemeldet"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid ""
"Scrapping a product will remove it from your stock. The product will\n"
"                end up in a scrap location that can be used for reporting purpose."
msgstr ""
"Wenn Sie ein Produkt ausrangieren, wird es aus Ihrem Bestand entfernt. Das Produkt wird\n"
"                landet am Ausschusslagerort, der zu Berichtszwecken verwendet werden kann."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Scraps"
msgstr "Ausschuss"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Search Procurement"
msgstr "Beschaffung suchen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Search Scrap"
msgstr "Ausschuss suchen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
msgid "Search not supported without a value."
msgstr "Suche wird ohne Wert nicht unterstützt."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
msgid "Select Route"
msgstr "Route auswählen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Select the places where this route can be selected"
msgstr ""
"Wählen Sie aus, an welchen Stellen diese Route ausgewählt werden kann."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
msgid ""
"Selected storage category does not exist in the 'store to' location or any "
"of its sublocations"
msgstr ""
"Die gewählte Lagerkategorie existiert weder im Lagerort noch in einem seiner"
" Unterlagerorte"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__picking_warn
#: model:ir.model.fields,help:stock.field_res_users__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Die Auswahl „Warnung“ wird dem Benutzer die Meldung zeigen. Die Auswahl "
"„Blockierende Meldung“ wird die Meldung ausgeben und den Arbeitsablauf "
"blockieren. Die Meldung muss ins nächste Feld eingetragen werden."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Selection not supported."
msgstr "Auswahl nicht unterstützt."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "Verkaufen und kaufen Sie Produkte in verschiedenen Maßeinheiten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Send an automatic confirmation SMS Text Message when Delivery Orders are "
"done"
msgstr ""
"Senden Sie eine automatische Bestätigungs-SMS, wenn Lieferaufträge erledigt "
"sind"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Send an automatic confirmation email when Delivery Orders are done"
msgstr ""
"Senden Sie eine automatische Bestätigungs-E-Mail, wenn Lieferaufträge "
"erledigt sind"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lead_mass_mail
msgid "Send email"
msgstr "E-Mail versenden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_sendcloud
msgid "Sendcloud Connector"
msgstr "Sendcloud-Konnektor"

#. module: stock
#: model:mail.template,description:stock.mail_template_data_delivery_confirmation
msgid ""
"Sent to the customers when orders are delivered, if the setting is enabled"
msgstr ""
"Versand an Kunden, wenn Aufträge geliefert werden, wenn diese Einstellung "
"aktiviert ist"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__barcode_separator
msgid "Separator"
msgstr "Trennlinie"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__9
msgid "September"
msgstr "September"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__sequence
#: model:ir.model.fields,field_description:stock.field_stock_package_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_route__sequence
#: model:ir.model.fields,field_description:stock.field_stock_rule__sequence
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__sequence
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__sequence
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Sequence"
msgstr "Sequenz"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Sequence %(code)s"
msgstr "%(code)s der Sequenz"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_code
msgid "Sequence Prefix"
msgstr "Sequenz-Präfix"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__lot_ids
msgid "Serial Numbers"
msgstr "Seriennummern"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"Serial number (%(serial_number)s) already exists in location(s): "
"%(location_list)s. Please correct the serial number encoded."
msgstr ""
"Seriennummer (%(serial_number)s) existiert schon an folgenden Lagerorten: "
"%(location_list)s. Bitte korrigieren Sie die veschlüsselte Seriennummer."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Serial number (%(serial_number)s) is not located in %(source_location)s, but is located in location(s): %(other_locations)s.\n"
"\n"
"Please correct this to prevent inconsistent data."
msgstr ""
"Seriennummer (%(serial_number)s) befindet sich nicht in %(source_location)s, sondern hier: %(other_locations)s.\n"
"\n"
"Bitte korrigieren Sie dies, um inkonsistente Daten zu vermeiden."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Serial number (%(serial_number)s) is not located in %(source_location)s, but is located in location(s): %(other_locations)s.\n"
"\n"
"Source location for this move will be changed to %(recommended_location)s"
msgstr ""
"Seriennummer (%(serial_number)s) befindet sich nicht in %(source_location)s, sondern hier: %(other_locations)s.\n"
"\n"
"Quelllagerort für diese Bewegung wird auf %(recommended_location)s geändert"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Serial numbers"
msgstr "Seriennummern"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Set"
msgstr "Festlegen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__set
msgid "Set Current Value"
msgstr "Aktuellen Wert einstellen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set Warehouse Routes"
msgstr "Lagerrouten festlegen"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source location for this product category.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closest location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting).\n"
"Least Packages: FIFO but with the least number of packages possible when there are several packages containing the same product."
msgstr ""
"Legen Sie eine bestimmte Entnahmestrategie fest, die unabhängig vom Quellort für diese Produktkategorie verwendet wird.\n"
"\n"
"FIFO: Produkte/Lose, die zuerst eingelagert wurden, werden zuerst entnommen.\n"
"LIFO: Produkte/Lose, die zuletzt eingelagert wurden, werden zuerst entnommen.\n"
"Nächstgelegener Lagerort: Die Produkte/Lose, die dem Zielplatz am nächsten sind, werden zuerst entnommen.\n"
"FEFO: Produkte/Lose mit dem nächstgelegenen Entnahmedatum werden zuerst entnommen (die Verfügbarkeit dieser Methode hängt von der Einstellung „Verfallsdaten“ ab).\n"
"Wenigste Pakete: FIFO, aber mit der geringstmöglichen Anzahl von Paketen, wenn es mehrere Pakete mit demselben Produkt gibt."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set expiration dates on lots & serial numbers"
msgstr "Verfallsdaten auf Los- und Seriennummern festlegen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set owner on stored products"
msgstr "Legen Sie Eigentümer für gelagerte Produkte fest"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to manage variants"
msgstr ""
"Legen Sie Produktattribute fest (z. B. Farbe, Größe), um Varianten zu "
"verwalten"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_to_zero_quants_tree
msgid "Set to 0"
msgstr "Auf 0 setzen"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_quants_tree
msgid "Set to quantity on hand"
msgstr "Menge auf vorrätig setzen"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_config_settings
#: model:ir.ui.menu,name:stock.menu_stock_general_settings
msgid "Settings"
msgstr "Einstellungen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Shelf A"
msgstr "Regal A"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posy
msgid "Shelves (Y)"
msgstr "Regale (Y)"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_lot_report
msgid "Ship a lot to a customer."
msgstr "Versenden Sie ein Los an einen Kunden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Shipments"
msgstr "Sendungen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping"
msgstr "Versand"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping Connectors"
msgstr "Versandkonnektoren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_type
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__move_type
msgid "Shipping Policy"
msgstr "Versandbedingungen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__shipping_weight
msgid "Shipping Weight"
msgstr "Versandgewicht"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Shipping connectors allow to compute accurate shipping costs, print shipping"
" labels and request carrier picking at your warehouse to ship to the "
"customer. Apply shipping connector from delivery methods."
msgstr ""
"Mithilfe von Versandkonnektoren können Sie genaue Versandkosten berechnen, "
"Versandaufkleber ausdrucken und die Abholung durch den Zusteller in Ihrem "
"Lagerhaus beantragen, um die Ware an den Kunden zu versenden. Wenden Sie den"
" Versandkonnektor über Liefermethoden an."

#. module: stock
#: model:mail.template,name:stock.mail_template_data_delivery_confirmation
msgid "Shipping: Send by Email"
msgstr "Versand: Per E-Mail senden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_shiprocket
msgid "Shiprocket Connector"
msgstr "Shiprocket-Konnektor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__code
msgid "Short Name"
msgstr "Kurzname"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__code
msgid "Short name used to identify your warehouse"
msgstr "Kurzname zur Identifizierung des Lagerhauses"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_allocation
msgid "Show Allocation"
msgstr "Zuordnung anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_check_availability
msgid "Show Check Availability"
msgstr "„Verfügbarkeit prüfen“ anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_operations
msgid "Show Detailed Operations"
msgstr "Detaillierte Vorgänge anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_forecasted_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_forecasted_qty_status_button
msgid "Show Forecasted Qty Status Button"
msgstr "Schaltfläche „Status der prognostizierten Menge“ anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_m2o
msgid "Show Lots M2O"
msgstr "M2O-Lose anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_text
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_lots_text
msgid "Show Lots Text"
msgstr "Los-Text anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_next_pickings
msgid "Show Next Pickings"
msgstr "Neue Kommissionierungen anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_on_hand_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_on_hand_qty_status_button
msgid "Show On Hand Qty Status Button"
msgstr "Schaltfläche „Status der vorrätigen Menge“ anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__is_favorite
msgid "Show Operation in Overview"
msgstr "Vorgang in Übersicht anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_picking_type
msgid "Show Picking Type"
msgstr "Kommissionierungsart anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_quant
msgid "Show Quant"
msgstr "Quant anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_show_reception_report
msgid "Show Reception Report at Validation"
msgstr "Empfangsbericht bei der Validierung anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__show_transfers
msgid "Show Transfers"
msgstr "Transfers anzeigen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Show all records which has next action date is before today"
msgstr "Alle Datensätze mit vor heute geplanten Aktionen anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_lots_m2o
msgid "Show lot_id"
msgstr "lot_id anzeigen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_lots_text
msgid "Show lot_name"
msgstr "lot_name anzeigen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rules_report__warehouse_ids
msgid "Show the routes that apply on selected warehouses."
msgstr "Routen anzeigen, die auf dem ausgewählten Lagerhaus angewandt werden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Sign"
msgstr "E-Signatur"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_sign_delivery
#: model:ir.model.fields,field_description:stock.field_stock_picking__signature
#: model:ir.model.fields,help:stock.field_stock_picking__signature
msgid "Signature"
msgstr "Signatur"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Signed"
msgstr "Unterzeichnet"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size"
msgstr "Größe"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size: Length × Width × Height"
msgstr "Größe: Länge × Breite × Höhe"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Snooze"
msgstr "Schlummern"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__snoozed_until
msgid "Snooze Date"
msgstr "Schlummerdatum"

#. module: stock
#: model:ir.model,name:stock.model_stock_orderpoint_snooze
msgid "Snooze Orderpoint"
msgstr "Beschaffungspunkt schlummern lassen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__predefined_date
msgid "Snooze for"
msgstr "Schlummern für"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Snoozed"
msgstr "Schlummerfunktion"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Some selected lines already have quantities set, they will be ignored."
msgstr ""
"Für einige ausgewählte Zeilen sind bereits Mengen festgelegt, sie werden "
"ignoriert."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__origin
msgid "Source"
msgstr "Quelle"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin
#: model:ir.model.fields,field_description:stock.field_stock_picking__origin
#: model:ir.model.fields,field_description:stock.field_stock_scrap__origin
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Source Document"
msgstr "Referenzbeleg"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__location_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__location_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Source Location"
msgstr "Quelllagerort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_usage
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_usage
msgid "Source Location Type"
msgstr "Typ der Quellstandorts"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Location:"
msgstr "Quelllagerort:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_report_label
msgid "Source Name"
msgstr "Quellenbezeichnung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Source Package"
msgstr "Quellpaket"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Package:"
msgstr "Quellpaket:"

#. module: stock
#: model:ir.actions.server,name:stock.stock_split_picking
msgid "Split"
msgstr "Aufteilen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Starred"
msgstr "Mit Sternchen versehen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Starred Products"
msgstr "Mit Sternchen versehene Produkte"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_starshipit
msgid "Starshipit Connector"
msgstr "Starshipit-Konnektor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__state
#: model:ir.model.fields,field_description:stock.field_stock_package_level__state
msgid "State"
msgstr "Status"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__state
#: model:ir.model.fields,field_description:stock.field_stock_move_line__state
#: model:ir.model.fields,field_description:stock.field_stock_picking__state
#: model:ir.model.fields,field_description:stock.field_stock_scrap__state
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Status"
msgstr "Status"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_state
#: model:ir.model.fields,help:stock.field_stock_picking__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basierend auf Aktivitäten\n"
"Überfällig: Fälligkeitsdatum bereits überschritten\n"
"Heute: Aktivitätsdatum ist heute\n"
"Geplant: anstehende Aktivitäten."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_product_stock_view
#: model:ir.ui.menu,name:stock.menu_product_stock
msgid "Stock"
msgstr "Bestand"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode_barcodelookup
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Stock Barcode Database"
msgstr "Bestandsbarcode-Datenbank"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Stock In Transit"
msgstr "Bestand in Transit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
msgid "Stock Location"
msgstr "Lagerort"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Stock Locations"
msgstr "Lagerorte"

#. module: stock
#: model:ir.model,name:stock.model_stock_move
#: model:ir.model.fields,field_description:stock.field_product_product__stock_move_ids
msgid "Stock Move"
msgstr "Lagerbuchung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Stock Moves"
msgstr "Lagerbuchungen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_graph
#: model_terms:ir.ui.view,arch_db:stock.view_move_pivot
msgid "Stock Moves Analysis"
msgstr "Statistik zu Lagerbuchungen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__move_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Stock Operation"
msgstr "Lagervorgang"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_destination
msgid "Stock Package Destination"
msgstr "Bestand Paketziel"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_level
msgid "Stock Package Level"
msgstr "Bestand Paketlevel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn
msgid "Stock Picking"
msgstr "Bestand Kommissionierung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__stock_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_view_graph
msgid "Stock Quant"
msgstr "Bestandsquant"

#. module: stock
#: model:ir.model,name:stock.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "Historie der Bestandsmenge"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant_relocate
msgid "Stock Quantity Relocation"
msgstr "Umlagerung der Bestandsmenge"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_quantity
msgid "Stock Quantity Report"
msgstr "Bericht über Bestandsmenge"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "Bestandsempfangsbericht"

#. module: stock
#: model:ir.model,name:stock.model_stock_forecasted_product_product
#: model:ir.model,name:stock.model_stock_forecasted_product_template
msgid "Stock Replenishment Report"
msgstr "Bestandsauffüllbericht"

#. module: stock
#: model:ir.model,name:stock.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "Lager Bestandszählung anfragen"

#. module: stock
#: model:ir.model,name:stock.model_stock_rule
#: model:ir.model.fields,field_description:stock.field_stock_move__rule_id
msgid "Stock Rule"
msgstr "Bestandsregel"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_rules_report
msgid "Stock Rules Report"
msgstr "Bestandsregelbericht"

#. module: stock
#: model:ir.model,name:stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Bestandsregelbericht"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_confirmation
msgid "Stock Track Confirmation"
msgstr "Bestand Verfolgungsbestätigung"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_line
msgid "Stock Track Line"
msgstr "Bestand Verfolgungslinie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids_without_package
msgid "Stock move"
msgstr "Lagerbuchung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Available (Ready to process)"
msgstr "Verfügbare Lagerbuchungen (zur Verarbeitung)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Confirmed, Available or Waiting"
msgstr ""
"Lagerbuchungen, welche entweder im Status Bestätigt, Verfügbar oder Wartend "
"sind"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that have been processed"
msgstr "Verarbeitete Lagerbuchungen"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_type
msgid "Stock package type"
msgstr "Typ des Lagerpakets"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Bestandsregelbericht"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "Informationen über die Auffüllung der Lagerbestände von Lieferanten"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_option
msgid "Stock warehouse replenishment option"
msgstr "Option zur Bestandsauffüllung"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Storage"
msgstr "Speicherung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
msgid "Storage Capacities"
msgstr "Lagerkapazitäten"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category
#: model:ir.ui.menu,name:stock.menu_storage_categoty_config
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Storage Categories"
msgstr "Lagerkategorien"

#. module: stock
#: model:ir.model,name:stock.model_stock_storage_category
#: model:ir.model.fields,field_description:stock.field_stock_location__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__storage_category_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Storage Category"
msgstr "Lagerkategorie"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category_capacity
#: model:ir.model,name:stock.model_stock_storage_category_capacity
#: model:ir.model.fields,field_description:stock.field_product_product__storage_category_capacity_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_type__storage_category_capacity_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_capacity_tree
msgid "Storage Category Capacity"
msgstr "Kapazität der Lagerkategorie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_multi_locations
msgid "Storage Locations"
msgstr "Lagerorte"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__store_type_id
msgid "Storage Type"
msgstr "Lagerungsart"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Store To"
msgstr "Lagern in"

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_1
msgid ""
"Store and retrieve information regarding every Lot/Serial Number (condition,"
" product info, ...)."
msgstr ""
"Speichern Sie Informationen zu jeder Los-/Seriennummer und rufen Sie sie auf"
" (Zustand, Produktinfo ...)."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_multi_locations
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Store products in specific locations of your warehouse (e.g. bins, racks) "
"and to track inventory accordingly."
msgstr ""
"Lagern Sie Produkte an bestimmten Orten in Ihrem Lagerhaus (z. B. in "
"speziellen Schränken und Staufächern) und verfolgen Sie den Bestand "
"entsprechend."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Store to"
msgstr "Lagern in"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_out_id
msgid "Store to sublocation"
msgstr "Im Unterstandort lagern"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sublocation
msgid "Sublocation"
msgstr "Unterlagerort"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__supplied_wh_id
msgid "Supplied Warehouse"
msgstr "Beliefertes Lager"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__procure_method
#: model:ir.model.fields,field_description:stock.field_stock_rule__procure_method
msgid "Supply Method"
msgstr "Beschaffungsmethode"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_route__supplier_wh_id
msgid "Supplying Warehouse"
msgstr "Beschaffungslager"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_scrap_reason_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Stichwortbezeichnung existiert bereits!"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_stock
msgid "Take From Stock"
msgstr "Lagerentnahme"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__mts_else_mto
msgid "Take From Stock, if unavailable, Trigger Another Rule"
msgstr ""
"Lagerentnahme, falls nicht verfügbar, wird eine andere Regel ausgelöst"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__procure_method
msgid ""
"Take From Stock: the products will be taken from the available stock of the source location.\n"
"Trigger Another Rule: the system will try to find a stock rule to bring the products in the source location. The available stock will be ignored.\n"
"Take From Stock, if Unavailable, Trigger Another Rule: the products will be taken from the available stock of the source location.If there is no stock available, the system will try to find a  rule to bring the products in the source location."
msgstr ""
"Lagerentnahme: Die Produkte werden aus dem verfügbaren Bestand des Quelllagerortes entnommen.\n"
"Eine andere Regel auslösen: Das System wird versuchen, eine Bestandsregel zu finden, um die Produkte an den Quelllagerort zu bringen. Der verfügbare Bestand wird ignoriert.\n"
"Lagerentnahme, wenn nicht verfügbar, Andere Regel auslösen: Die Produkte werden aus dem verfügbaren Bestand des Quelllagerortes entnommen; wenn kein Bestand verfügbar ist, versucht das System, eine Regel zu finden, um die Produkte an den Quelllagerort zu bringen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr ""
"Technisches Feld, mit dem entschieden wird, ob die Schaltfläche „Zuteilung“ "
"angezeigt werden soll."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Technical Information"
msgstr "Technische Information"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_check_availability
msgid ""
"Technical field used to compute whether the button \"Check Availability\" "
"should be displayed."
msgstr ""
"Technisches Feld, über das bestimmt wird, ob die Schaltfläche „Verfügbarkeit"
" prüfen“ angezeigt werden soll."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_tmpl_id
msgid "Template"
msgstr "Vorlage"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The %s location is required by the Inventory app and cannot be deleted, but "
"you can archive it."
msgstr ""
"Der Standort %s wird von der Lagerapp benötigt und kann nicht gelöscht "
"werden, aber Sie können ihn archivieren."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__auto
msgid ""
"The 'Manual Operation' value will create a stock move after the current one."
" With 'Automatic No Step Added', the location is replaced in the original "
"move."
msgstr ""
"Der Wert „Manueller Vorgang“ erzeugt nach der aktuellen Lagerbuchung eine "
"weitere. Mit „Automatisch ohne Folgeschritt“ wird der Lagerort in "
"ursprünglicher Bewegung ersetzt."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "The Lot/Serial number (%s) is linked to another product."
msgstr "Die Los-/Seriennummer (%s) ist mit einem anderen Proukt verknüpft-"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"The Picking Operations report has been deleted so you cannot print at this "
"time unless the report is restored."
msgstr ""
"Der Bericht über Kommissioniervorgänge wurde gelöscht, sodass Sie derzeit "
"nicht drucken können, es sei denn, der Bericht wird wiederhergestellt."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"The Serial Number (%(serial_number)s) is already used in location(s): %(location_list)s.\n"
"\n"
"Is this expected? For example, this can occur if a delivery operation is validated before its corresponding receipt operation is validated. In this case the issue will be solved automatically once all steps are completed. Otherwise, the serial number should be corrected to prevent inconsistent data."
msgstr ""
"Die Seriennummer (%(serial_number)s) wird bereits an folgenden Orten verwendet: %(location_list)s.\n"
"\n"
"Ist dies zu erwarten? Dies kann zum Beispiel auftreten, wenn ein Liefervorgang validiert wird, bevor der zugehörige Empfangsvorgang validiert wird. In diesem Fall wird das Problem automatisch gelöst, sobald alle Schritte abgeschlossen sind. Andernfalls sollte die Seriennummer korrigiert werden, um inkonsistente Daten zu vermeiden."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "The backorder %s has been created."
msgstr "Der Lieferrückstand %s wurde erstellt."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_barcode_company_uniq
msgid "The barcode for a location must be unique per company!"
msgstr ""
"Der Barcode eines Unternehmen für einen Lagerort sollte eindeutig sein!"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"The client explicitly waives its own standard terms and conditions, even if "
"these were drawn up after these standard terms and conditions of sale. In "
"order to be valid, any derogation must be expressly agreed to in advance in "
"writing."
msgstr ""
"Der Kunde verzichtet ausdrücklich auf seine eigenen allgemeinen "
"Geschäftsbedingungen, auch wenn diese nach diesen allgemeinen "
"Verkaufsbedingungen erstellt wurden. Jede Abweichung muss, um gültig zu "
"sein, im Voraus ausdrücklich und schriftlich vereinbart werden."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"The combination of lot/serial number and product must be unique within a company including when no company is defined.\n"
"The following combinations contain duplicates:\n"
"%(error_lines)s"
msgstr ""
"Die Kombination von Los-/Seriennummer und Produkt muss in einem Unternehmen eindeutig sein, wenn kein Unternehmen definiert ist.\n"
"Die folgenden Kombinationen enthalten Duplikate:\n"
"%(error_lines)s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__company_id
msgid "The company is automatically set from your user preferences."
msgstr ""
"Das Unternehmen wird automatisch über Ihre Benutzereinstellungen "
"eingestellt."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__day_2
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "The day after tomorrow"
msgstr "Übermorgen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The deadline has been automatically updated due to a delay on %s."
msgstr ""
"Die Frist wurde automatisch wegen einer Verzögerung bei %s aktualisiert."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__delay
msgid ""
"The expected date of the created transfer will be computed based on this "
"lead time."
msgstr ""
"Das erwartete Datum des erzeugten Transfers wird auf der Grundlage dieser "
"Vorlaufzeit errechnet."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__sequence
msgid "The first in the sequence is the default one."
msgstr "Der Erste der Sequenz gilt als Standard."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "The following replenishment order have been generated"
msgstr "Der folgende Auffüllungsauftrag wurde generiert."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "The forecasted quantity of"
msgstr "Die prognostizierte Menge von"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "The forecasted stock on the"
msgstr "Der prognostizierte Bestand am"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "The inter-warehouse transfers have been generated"
msgstr "Die lagerübergreifenden Transfers wurden erzeugt"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "The inventory adjustments have been reverted."
msgstr "Die Bestandsaufnahmen wurden zurückgesetzt."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_inventory_freq_nonneg
msgid "The inventory frequency (days) for a location must be non-negative"
msgstr ""
"Die Inventurhäufigkeit (Tage) für einen Lagerort muss nicht negativ sein."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"The minimum quantity must be less than or equal to the maximum quantity."
msgstr "Die minimale Menge muss kleiner oder gleich der maximalen Menge sein."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_name_uniq
msgid "The name of the warehouse must be unique per company!"
msgstr "Der Name eines Lagerhauses muss pro Unternehmen eindeutig sein!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The number of Serial Numbers to generate must be greater than zero."
msgstr ""
"Die Anzahl der zu erzeugenden Seriennummern muss größer als Null sein."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_final_id
msgid ""
"The operation brings the products to the intermediate location.But this "
"operation is part of a chain of operations targeting the final location."
msgstr ""
"Der Vorgang bringt die Produkte an den Übergangsort, aber dieser Vorgang ist"
" Teil einer Kette von Vorgängen, die auf den endgültigen Standort abzielen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_id
msgid "The operation takes and suggests products from this location."
msgstr "Der Betrieb nimmt und empfiehlt Produkte von diesem Standort aus."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"            operation a specific type which will alter its views accordingly.\n"
"            On the operation type you could e.g. specify if packing is needed by default,\n"
"            if it should show the customer."
msgstr ""
"Mithilfe dieses Vorgangsartsystems können Sie jedem Lagervorgang\n"
"            einen spezifischen Typ zuweisen, der die Ansicht entsprechend ändert.\n"
"            In der Vorgangsart können Sie z. B. angeben, ob die Verpackung standardmäßig erforderlich ist und\n"
"            ob der Kunde darauf angegeben werden soll."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_id
msgid "The operations brings product to this location"
msgstr "Dieser Vorgang bringt Produkte zu diesem Standort"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__package_id
msgid "The package containing this quant"
msgstr "Das Paket beinhaltet dieses Quant"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__location_id
msgid ""
"The parent location that includes this location. Example : The 'Dispatch "
"Zone' is the 'Gate 1' parent location."
msgstr ""
"Der übergeordnete Lagerort, der diesen Lagerort enthält. Beispiel: Die "
"„Versandzone“ ist der übergeordnete Ort von „Tor 1“."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid ""
"The procurement quantity will be rounded up to a multiple of this field "
"quantity. If it is 0, it is not rounded."
msgstr ""
"Die Beschaffungsmenge wird auf ein Vielfaches dieser Feldmenge aufgerundet. "
"Wenn sie 0 ist, wird sie nicht gerundet."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "The product is not available in sufficient quantity"
msgstr "Das Produkt ist nicht in ausreichender Menge verfügbar"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_quantity
msgid "The product's counted quantity."
msgstr "Die gezählte Menge des Produkts."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid ""
"The quantities selected do not all belong to the same location.\n"
"                    You may not assign them a package without moving them to a common location."
msgstr ""
"Die ausgewählten Mengen gehören nicht alle zum selben Lagerort.\n"
"                    Sie können sie möglicherweisen keinem Paket zuweisen, ohne sie zu einem allgemeinen Lagerort zu bewegen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"The quantity done for the product \"%(product)s\" doesn't respect the "
"rounding precision defined on the unit of measure \"%(unit)s\". Please "
"change the quantity done or the rounding precision of your unit of measure."
msgstr ""
"Die Menge, die für das Produkt „%(product)s“ angegeben wurde, entspricht "
"nicht der Rundungsgenauigkeit, die für die Maßeinheit „%(unit)s“ definiert "
"wurde. Bitte ändern Sie die angegebene Menge oder die Rundungsgenauigkeit "
"Ihrer Maßeinheit."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The quantity per lot should always be a positive value."
msgstr "Die Menge pro Los sollte immer ein positiver Wert sein."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"The requested operation cannot be processed because of a programming error "
"setting the `product_qty` field instead of the `product_uom_qty`."
msgstr ""
"Der angeforderte Vorgang kann aufgrund eines Programmfehlers nicht "
"durchgeführt werden. Anstelle des Felds „product_uom_qty“ wurde "
"fälschlicherweise das Feld „product_uom“ eingetragen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The search does not support the %(operator)s operator or %(value)s value."
msgstr ""
"Die Suche unterstützt den %(operator)s-Operator oder %(value)s-Wert nicht."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The selected Inventory Frequency (Days) creates a date too far into the "
"future."
msgstr ""
"Die gewählte Inventurhäufigkeit (Tage) erzeugt ein Datum, das zu weit in der"
" Zukunft liegt."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"The serial number has already been assigned: \n"
" Product: %(product)s, Serial Number: %(serial_number)s"
msgstr ""
"Diese Seriennummer wurde bereits zugewiesen:\n"
"Produkt: %(product)s, Seriennummer: %(serial_number)s"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_code_uniq
msgid "The short name of the warehouse must be unique per company!"
msgstr ""
"Die Kurzbezeichnung des Lagerhauses muss pro Unternehmen eindeutig sein!"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,help:stock.field_res_users__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr ""
"Der Lagerort, der beim Versand von Waren an diesen Kontakt als Zielort "
"verwendet wird."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,help:stock.field_res_users__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr ""
"Der Lagerort, der beim Empfang von Waren von diesem Kontakt als Quelle "
"verwendet wird."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_id
msgid "The stock operation where the packing has been made"
msgstr "Der Lagervorgang, durch den die Waren verpackt wurden"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__rule_id
msgid "The stock rule that created this stock move"
msgstr "Die Bestandsregel, welche diese Lagerbuchung erstellt hat"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_warehouse_id
msgid ""
"The warehouse to propagate on the created move/procurement, which can be "
"different of the warehouse this rule is for (e.g for resupplying rules from "
"another warehouse)"
msgstr ""
"Das Lagerhaus, das bei der erstellten Bewegung/Beschaffung übertragen werden"
" soll, kann ein anderes sein als das Lagerhaus, für das diese Regel gilt (z."
" B. für Beschaffungsregeln aus einem anderen Lagerhaus)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "There are no inventory adjustments to revert."
msgstr "Es sind keine Bestandsaufnahmen rückgängig zu machen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"There is nothing eligible to put in a pack. Either there are no quantities "
"to put in a pack or all products are already in a pack."
msgstr ""
"Es gibt nichts zu verpacken. Entweder gibt es keine Mengen, die verpackt "
"werden können, oder alle Produkte sind bereits verpackt."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid "There's no product move yet"
msgstr "Es gibt noch keine Produktbewegung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
msgid "This SN is already in another location."
msgstr "Die SN befindet sich bereits an einem anderen Standort."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"This adds a dropshipping route to apply on products in order to request your"
" vendors to deliver to your customers. A product to dropship will generate a"
" purchase request for quotation once the sales order confirmed. This is a "
"on-demand flow. The requested delivery address will be the customer delivery"
" address and not your warehouse."
msgstr ""
"Dadurch wird eine Streckengeschäftsroute hinzugefügt, die auf Produkte "
"angewendet werden kann, um Ihre Lieferanten anzuweisen, an Ihre Kunden zu "
"liefern. Ein Produkt für das Streckengeschäft generiert eine "
"Angebotsanforderung, sobald der Verkaufsauftrag bestätigt wurde. Dies ist "
"ein Ablauf nach Bedarf. Die gewünschte Lieferadresse ist die Lieferadresse "
"des Kunden und nicht Ihr Lager."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"This analysis gives you an overview of the current stock level of your "
"products."
msgstr ""
"Diese Analyse gibt Ihnen einen Überblick über den aktuellen Lagerbestand "
"Ihrer Produkte."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picked
msgid ""
"This checkbox is just indicative, it doesn't validate or generate any "
"product moves."
msgstr ""
"Dieses Kontrollkästchen ist nur indikativ, es validiert oder erzeugt keine "
"Produktbewegungen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__name
msgid "This field will fill the packing origin and the name of its moves"
msgstr ""
"In dieses Feld werden die Herkunft des Pakets und der Name der Bewegung "
"eingetragen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_dest_id
msgid ""
"This is the default destination location when this operation is manually "
"created. However, it is possible to change it afterwards or that the routes "
"use another one by default."
msgstr ""
"Dies ist der Standardzielort, wenn dieser Vorgang manuell erstellt wird. Es "
"ist jedoch möglich, ihn nachträglich zu ändern oder dass die Routen "
"standardmäßig einen anderen verwenden."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_src_id
msgid ""
"This is the default source location when this operation is manually created."
" However, it is possible to change it afterwards or that the routes use "
"another one by default."
msgstr ""
"Dies ist der Standardquellort, wenn dieser Vorgang manuell erstellt wird. Es"
" ist jedoch möglich, ihn nachträglich zu ändern oder dass die Routen "
"standardmäßig einen anderen verwenden."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__owner_id
msgid "This is the owner of the quant"
msgstr "Dies ist der Eigentümer des Quants"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_qty
msgid ""
"This is the quantity of product that is planned to be moved.Lowering this "
"quantity does not generate a backorder.Changing this quantity on assigned "
"moves affects the product reservation, and should be done with care."
msgstr ""
"Dies ist die Menge eine Produkts, das bewegt werden soll. Eine Verringerung "
"dieser Menge führt nicht zu einem Lieferrückstand. Eine Änderung dieser "
"Menge bei zugewiesenen Bewegungen wirkt sich auf die Produktreservierung aus"
" und sollte daher mit Vorsicht vorgenommen werden."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__child_internal_location_ids
msgid ""
"This location (if it's internal) and all its descendants filtered by "
"type=Internal."
msgstr ""
"Dieser Standort (wenn er intern ist) und alle seine Nachkommen, gefiltert "
"nach type=Internal."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"This location's usage cannot be changed to view as it contains products."
msgstr ""
"Die Nutzung dieses Lagerorts kann nicht geändert werden, da er Produkte "
"enthält."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"This lot %(lot_name)s is incompatible with this product %(product_name)s"
msgstr ""
"Das Los %(lot_name)s ist mit diesem Produkt %(product_name)s nicht "
"kompatibel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "This lot/serial number is already in another location"
msgstr ""
"Die Los-/Seriennummer befindet sich bereits an einem anderen Lagerort."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid ""
"This menu gives you the full traceability of inventory\n"
"                operations on a specific product. You can filter on the product\n"
"                to see all the past or future movements for the product."
msgstr ""
"Durch diese Anwendung können Sie sämtliche Lieferungen und\n"
"                Bestandsänderungen Ihrer Produkte verfolgen.  Suchen sie einfach\n"
"                das Produkt und verfolgen Sie die gesamte Liefer- und \n"
"                Auftragshistorie der Vergangenheit und Zukunft."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                    You can filter on the product to see all the past movements for the product."
msgstr ""
"Dieses Menü ermöglicht Ihnen die vollständige Rückverfolgung der Bestandsvorgänge für ein bestimmtes Produkt.\n"
"Sie können nach dem Produkt filtern, um alle vergangenen Bewegungen für das Produkt zu sehen."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "This note is added to delivery orders."
msgstr "Dieser Hinweis wird bei Lieferaufträgen hinzugefügt."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to internal transfer orders (e.g. where to pick the "
"product in the warehouse)."
msgstr ""
"Diese Notiz wird zu internen Transferaufträgen hinzugefügt (z. B. wo das "
"Produkt im Lager entnommen werden soll)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to receipt orders (e.g. where to store the product in the"
" warehouse)."
msgstr ""
"Diese Notiz wird den Eingangsaufträgen hinzugefügt (z. B. wo das Produkt im "
"Lagerhaus gelagert werden soll)."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product has been used in at least one inventory movement. It is not "
"advised to change the Product Type since it can lead to inconsistencies. A "
"better solution could be to archive the product and create a new one "
"instead."
msgstr ""
"Dieses Produkt wurde in mindestens einer Lagerbewegung verwendet. Es wird "
"nicht empfohlen, die Produktart zu ändern, da dies zu Inkonsistenzen führen "
"kann. Eine bessere Lösung wäre es, das Produkt zu archivieren und "
"stattdessen ein neues Produkt anzulegen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product's company cannot be changed as long as there are quantities of "
"it belonging to another company."
msgstr ""
"Das Unternehmen dieses Produkts kann nicht geändert werden, solange es "
"Mengen dieses Produkts gibt, die einem anderen Unternehmen gehören."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product's company cannot be changed as long as there are stock moves of"
" it belonging to another company."
msgstr ""
"Das Unternehmen dieses Produkts kann nicht geändert werden, solange es "
"Lagerbuchungen gibt, die einem anderen Unternehmen gehören."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__new_quantity
msgid ""
"This quantity is expressed in the Default Unit of Measure of the product."
msgstr "Die Anzahl wird in der Standardmaßeinheit des Produkts dargestellt."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/list/inventory_report_list_model.js:0
msgid "This record already exists."
msgstr "Dieser Datensatz existiert bereits."

#. module: stock
#. odoo-python
#: code:addons/stock/report/report_stock_reception.py:0
msgid "This report cannot be used for done and not done %s at the same time"
msgstr ""
"Dieser Bericht kann nicht gleichzeitig für erledigte und nichterledigte %s "
"verwendet werden."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"This sequence prefix is already being used by another operation type. It is "
"recommended that you select a unique prefix to avoid issues and/or repeated "
"reference values or assign the existing reference sequence to this operation"
" type."
msgstr ""
"Dieses Sequenzpräfix wird bereits von einer anderen Vorgangsart verwendet. "
"Es wird empfohlen, ein eindeutiges Präfix zu wählen, um Probleme und/oder "
"wiederholte Referenzwerte zu vermeiden, oder die vorhandene Referenzfolge "
"dieser Vorgangsart zuzuweisen."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_production
#: model:ir.model.fields,help:stock.field_product_template__property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""
"Dieser Lagerort wird anstelle des Standardlagerortes als Quellort für "
"Lagerbuchungen verwendet, die durch Fertigungsaufträge erzeugt werden."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,help:stock.field_product_template__property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""
"Dieser Lagerort wird anstelle des Standardlagerortes als Quellort für "
"Lagerbuchungen verwendet, die bei einer Inventur erzeugt werden."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__responsible_id
#: model:ir.model.fields,help:stock.field_product_template__responsible_id
msgid ""
"This user will be responsible of the next activities related to logistic "
"operations for this product."
msgstr ""
"Dieser Benutzer wird für die nächsten Aktivitäten im Zusammenhang mit den "
"logistischen Abläufen für dieses Produkt verantwortlich sein."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
msgid "This will discard all unapplied counts, do you want to proceed?"
msgstr ""
"Dadurch werden alle nicht angewendeten Zählungen verworfen, möchten Sie "
"fortfahren?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid ""
"Those products you added are tracked but lots/serials were not defined. Once applied those can't be changed.<br/>\n"
"                    Apply anyway?"
msgstr ""
"Diese durch Sie hinzugefügten Produkte werden verfolgt, aber es wurden keine Los-/Seriennummern festgelegt. Einmal angewendet, können sie nicht mehr geändert werden.<br/>\n"
"                    Trotzdem anwenden?"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "Time Horizon"
msgstr "Zeithorizont"

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_1
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_1
msgid "Tip: Monitor Lot details"
msgstr "Tipp: Beobachten Sie Losdetails"

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_0
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid "Tip: Speed up inventory operations with barcodes"
msgstr "Tipp: Beschleunigen Sie Lagervorgänge mit Barcodes"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "To"
msgstr "Nach"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Apply"
msgstr "Anzuwenden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__to_backorder
msgid "To Backorder"
msgstr "In Lieferrückstand setzen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Count"
msgstr "Zu zählen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Deliver"
msgstr "Zu liefern"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_graph
#: model:ir.actions.act_window,name:stock.action_picking_tree_ready
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "To Do"
msgstr "To-do"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "To Location"
msgstr "Zum Lagerort"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order
msgid "To Order"
msgstr "Zu bestellen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order_computed
msgid "To Order Computed"
msgstr "Zu bestellende Menge berechnet"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order_manual
msgid "To Order Manual"
msgstr "Manuell zu bestellen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "To Order with Visibility Days"
msgstr "Mit Sichtbarkeitstagen zu bestellen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "To Package"
msgstr "Zum Paket"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Process"
msgstr "Zu verarbeiten"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Receive"
msgstr "Zu erhalten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "To Reorder"
msgstr "Nachzubestellen"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/widgets/json_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__today
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today"
msgstr "Heute"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today Activities"
msgstr "Heutige Aktivitäten"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__day_1
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Tomorrow"
msgstr "Morgen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total Available"
msgstr "Insgesamt verfügbar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "Total Demand"
msgstr "Gesamtbedarf"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Forecasted"
msgstr "Insgesamt prognostiziert"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Free to Use"
msgstr "Insgesamt verfügbar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Incoming"
msgstr "Gesamteingänge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total On Hand"
msgstr "Insgesamt vorrätig"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Outgoing"
msgstr "Gesamtausgänge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "Total Quantity"
msgstr "Gesamtmenge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total Reserved"
msgstr "Insgesamt reserviert"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__total_route_ids
msgid "Total routes"
msgstr "Anzahl Routen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__shipping_weight
msgid ""
"Total weight of packages and products not in a package. Packages with no "
"shipping weight specified will default to their products' total weight. This"
" is the weight used to compute the cost of the shipping."
msgstr ""
"Gesamtgewicht von Paketen und Produkten, die nicht in einem Paket enthalten "
"sind. Bei Paketen, für die kein Versandgewicht angegeben ist, wird "
"standardmäßig das Gesamtgewicht der Produkte verwendet. Dies ist das "
"Gewicht, das zur Berechnung der Versandkosten verwendet wird."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__weight_bulk
msgid "Total weight of products which are not in a package."
msgstr "Gesamtgewicht der Produkte, die sich nicht in einem Paket befinden."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__shipping_weight
msgid "Total weight of the package."
msgstr "Gesamtgewicht des Pakets."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Traceability"
msgstr "Rückverfolgbarkeit"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.js:0
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.actions.client,name:stock.action_stock_report
#: model:ir.model,name:stock.model_stock_traceability_report
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Traceability Report"
msgstr "Rückverfolgbarkeitsbericht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__is_storable
#: model:ir.model.fields,field_description:stock.field_product_template__is_storable
#: model:ir.model.fields,field_description:stock.field_stock_move__is_storable
msgid "Track Inventory"
msgstr "Bestand verfolgen"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__module_product_expiry
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of life, alert. \n"
" Such dates are set automatically at lot/serial number creation based on values set on the product (in days)."
msgstr ""
"Verfolgen Sie die folgenden Daten auf Los- und Seriennummern: Mindestens haltbar bis, Entnahme, Verwendbar bis, Warnung. \n"
"Diese Daten werden automatisch bei der Erstellung von Los-/Seriennummern festgelegt basierend auf für das Produkt festgelegten Werten (in Tagen)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of"
" life, alert. Such dates are set automatically at lot/serial number creation"
" based on values set on the product (in days)."
msgstr ""
"Verfolgen Sie die folgenden Daten auf Los- und Seriennummern: Mindestens "
"haltbar bis, Entnahme, Verwendbar bis, Warnung. Diese Daten werden "
"automatisch bei der Erstellung von Los-/Seriennummern festgelegt basierend "
"auf für das Produkt festgelegten Werten (in Tagen)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Track product location in your warehouse"
msgstr "Verfolgen Sie den Lagerort von Produkten in Ihrem Lagerhaus"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "Track your stock quantities by creating storable products."
msgstr ""
"Verfolgen Sie Ihre Lagerbestände, indem Sie lagerfähige Produkte anlegen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Tracked Products in Inventory Adjustment"
msgstr "Verfolgte Produkte in Bestandsaufnahme"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__tracking
#: model:ir.model.fields,field_description:stock.field_product_template__tracking
#: model:ir.model.fields,field_description:stock.field_stock_move_line__tracking
#: model:ir.model.fields,field_description:stock.field_stock_quant__tracking
#: model:ir.model.fields,field_description:stock.field_stock_track_line__tracking
msgid "Tracking"
msgstr "Nachverfolgung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__tracking_line_ids
msgid "Tracking Line"
msgstr "Verfolgungszeile"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Transfer"
msgstr "Transfer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Transfer to"
msgstr "Verschieben nach"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_all
#: model:ir.model.fields,field_description:stock.field_stock_lot__delivery_ids
#: model:ir.ui.menu,name:stock.menu_stock_transfers
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Transfers"
msgstr "Transfers"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Transfers %s: Please add some items to move."
msgstr "Transfers %s: Bitte fügen Sie einige zu bewegende Artikel hinzu."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "Transfers allow you to move products from one location to another."
msgstr ""
"Transfers erlauben es Ihnen, Produkte von einem zum anderen Lagerort zu "
"bewegen."

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_pickings
msgid "Transfers for Groups"
msgstr "Transfers für Gruppen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid ""
"Transfers that are late on scheduled time or one of pickings will be late"
msgstr ""
"Verspätete Transfers zur geplanten Zeit oder eine der Kommissionierungen "
"werden verspätet sein"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__transit
msgid "Transit Location"
msgstr "Umschlagslager"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Transit Locations"
msgstr "Umschlagslager"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Transport management: organize packs in your fleet, or carriers."
msgstr ""
"Transportmanagement: Organisieren Sie die Pakete in Ihrem Fuhrpark oder bei "
"Spediteuren."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__trigger
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Trigger"
msgstr "Auslöser"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_order
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule"
msgstr "Andere Regel auslösen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule If No Stock"
msgstr "Andere Regel auslösen falls nicht vorrätig"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Trigger Manual"
msgstr "Manuell auslösen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_scrap__should_replenish
msgid "Trigger replenishment for scrapped products"
msgstr "Auffüllung für Ausschussprodukte auslösen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/stock_forecasted.js:0
msgid "Try to add some incoming or outgoing transfers."
msgstr "Versuchen Sie, eingehende oder ausgehende Transfers hinzuzufügen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_barcode_rule__type
msgid "Type"
msgstr "Typ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Type a message..."
msgstr "Nachricht eingeben ..."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__code
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Type of Operation"
msgstr "Vorgangsart"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_exception_decoration
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ der Ausnahmeaktivität im Datensatz."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_ups
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "UPS Connector"
msgstr "UPS-Konnektor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_usps
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "USPS Connector"
msgstr "USPS-Konnektor"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Unassign"
msgstr "Zuweisung aufheben"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"Unavailable Serial numbers. Please correct the serial numbers encoded: "
"%(serial_numbers_to_locations)s"
msgstr ""
"Nichtverfügbare Seriennummern. Bitte korrigieren Sie die verschlüsselten "
"Seriennummern: %(serial_numbers_to_locations)s"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
msgid "Unfold"
msgstr "Ausklappen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__name
msgid "Unique Lot/Serial Number"
msgstr "Eindeutige Los/Seriennummer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "Unit"
msgstr "Einheit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__price_unit
msgid "Unit Price"
msgstr "Einzelpreis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__forecast_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_uom_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_uom_name
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Unit of Measure"
msgstr "Maßeinheit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__uom
msgid "Unit of Measure Name"
msgstr "Name der Maßeinheit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Units"
msgstr "Einheiten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Units Of Measure"
msgstr "Maßeinheiten"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_unit_measure_stock
msgid "Units of Measure"
msgstr "Maßeinheiten"

#. module: stock
#: model:ir.ui.menu,name:stock.product_uom_menu
msgid "Units of Measures"
msgstr "Maßeinheiten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_id
msgid "Unity of measure"
msgstr "Maßeinheit"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Unknown Pack"
msgstr "Unbekanntes Paket"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid ""
"Unless previously specified by the source document, this will be the default"
" picking policy for this operation type."
msgstr ""
"Sofern nicht bereits im Quelldokument festgelegt, ist dies die Standard-"
"Kommissionierrichtlinie für diesen Vorgangstyp."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Unpack"
msgstr "Entpacken"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.actions.server,name:stock.action_unreserve_picking
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Unreserve"
msgstr "Reservierung aufheben"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Unreturned"
msgstr "Nicht zurückgesendet"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Unsafe unit of measure"
msgstr "Unsichere Maßeinheit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__unwanted_replenish
msgid "Unwanted Replenish"
msgstr "Ungewollte Auffüllung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom
#: model:ir.ui.menu,name:stock.menu_stock_uom_form_action
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "UoM"
msgstr "Maßeinheit"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_categ_form_action
msgid "UoM Categories"
msgstr "ME-Kategorien"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__uom_id
msgid "Uom"
msgstr "Maßeinheit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "Update Product Quantity"
msgstr "Produktmenge aktualisieren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
msgid "Update Quantities"
msgstr "Mengen aktualisieren"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.xml:0
#: model:ir.actions.act_window,name:stock.dashboard_open_quants
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Update Quantity"
msgstr "Menge aktualisieren"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"Updating the location of this transfer will result in unreservation of the currently assigned items. An attempt to reserve items at the new location will be made and the link with preceding transfers will be discarded.\n"
"\n"
"To avoid this, please discard the source location change before saving."
msgstr ""
"Die Aktualisierung des Lagerortes dieses Transfers führt dazu, dass die Reservierung der derzeit zugewiesenen Artikel aufgehoben wird. Es wird versucht, Artikel am neuen Ort zu reservieren, und die Verknüpfung mit vorangegangenen Transfers wird verworfen.\n"
"\n"
"Um dies zu vermeiden, verwerfen Sie bitte die Änderung des Quelllagerortes vor dem Speichern."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__1
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__1
msgid "Urgent"
msgstr "Dringend"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_existing_lots
msgid "Use Existing Lots/Serial Numbers"
msgstr "Bestehende Los/Seriennummer verwenden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Use Existing ones"
msgstr "Bestehende verwenden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Use GS1 nomenclature datamatrix whenever barcodes are printed for lots and "
"serial numbers."
msgstr ""
"Verwenden Sie die GS1-Nomenklatur-Datamatrix, wenn Barcodes für Los- und "
"Seriennummern gedruckt werden."

#. module: stock
#: model:res.groups,name:stock.group_reception_report
msgid "Use Reception Report"
msgstr "Empfangsbericht verwenden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Use your own routes"
msgstr "Verwenden Sie Ihre eigenen Routen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Used by"
msgstr "Verwendet von"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__sequence
msgid "Used to order the 'All Operations' kanban view"
msgstr "Sortieren Sie hier die Kanban-Ansicht „Alle Vorgänge“"

#. module: stock
#: model:ir.model,name:stock.model_res_users
#: model:ir.model.fields,field_description:stock.field_stock_request_count__user_id
#: model:res.groups,name:stock.group_stock_user
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "User"
msgstr "Benutzer"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__user_id
msgid "User assigned to do product count."
msgstr "Benutzer, der für die Produktzählung zuständig ist."

#. module: stock
#: model:ir.actions.server,name:stock.action_validate_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Validate"
msgstr "Validieren"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/xml/inventory_lines.xml:0
msgid "Validate Inventory"
msgstr "Inventur validieren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_variant_count
msgid "Variant Count"
msgstr "Anzahl Varianten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor"
msgstr "Lieferant"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_supplier
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__supplier
msgid "Vendor Location"
msgstr "Lagerort des Lieferanten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor Locations"
msgstr "Lagerorte der Lieferanten"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__view
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "View"
msgstr "Ansicht"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_view_kanban_catalog
msgid "View Availability"
msgstr "Verfügbarkeit sehen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "View Diagram"
msgstr "Diagramm ansehen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__view_location_id
msgid "View Location"
msgstr "Ansichtslagerort"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "View and allocate received quantities."
msgstr "Zeigen Sie erhaltene Mengen an und ordnen Sie sie zu."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__visibility_days
msgid "Visibility Days"
msgstr "Sichtbarkeitstage"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Visibility days"
msgstr "Sichtbarkeitstage"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__shipping_volume
msgid "Volume for Shipping"
msgstr "Volumen für Versand"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/OUT/00001"
msgstr "WH/OUT/00001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "WH/OUT/0001"
msgstr "WH/OUT/0001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/Outgoing"
msgstr "WH/Outgoing"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_generic_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/Stock"
msgstr "WH/Stock"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__confirmed
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting"
msgstr "Wartend"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__waiting
msgid "Waiting Another Move"
msgstr "Wartet auf andere Bewegung"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__waiting
msgid "Waiting Another Operation"
msgstr "Wartet auf anderen Vorgang"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__confirmed
msgid "Waiting Availability"
msgstr "Wartet auf Verfügbarkeit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting Moves"
msgstr "Wartende Bewegungen"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_waiting
msgid "Waiting Transfers"
msgstr "Wartende Transfers"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse
#: model:ir.model.fields,field_description:stock.field_product_product__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_replenish__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_template__warehouse_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_move__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__warehouse_id
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Warehouse"
msgstr "Lagerhaus"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse Configuration"
msgstr "Lagerhauskonfiguration"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_domain_ids
msgid "Warehouse Domain"
msgstr "Bereich des Lagerhauses"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
msgid "Warehouse Location"
msgstr "Standort des Lagerhauses"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_config
msgid "Warehouse Management"
msgstr "Lagerverwaltung"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_view_ids
msgid "Warehouse View"
msgstr "Lagerhausansicht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_warehouse_id
msgid "Warehouse to Propagate"
msgstr "Zu übertragendes Lagerhaus"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse view location"
msgstr "Lagerhaus Ansichtslagerort"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Warehouse's Routes"
msgstr "Routen im Lagerhaus"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_warehouse_filter.xml:0
msgid "Warehouse:"
msgstr "Lagerhaus:"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_report_search_panel.xml:0
#: model:ir.actions.act_window,name:stock.action_warehouse_form
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_ids
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__warehouse_ids
#: model:ir.ui.menu,name:stock.menu_action_warehouse_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
msgid "Warehouses"
msgstr "Lagerhäuser"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty
msgid "Warn Insufficient Quantity"
msgstr "Vor unzureichender Menge warnen"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty_scrap
msgid "Warn Insufficient Scrap Quantity"
msgstr "Vor unzureichender Ausschussmenge warnen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_quant.py:0
#: code:addons/stock/models/stock_scrap.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__warning
msgid "Warning"
msgstr "Warnung"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Warning Duplicated SN"
msgstr "Warnung Duplizierte SN"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__warning_message
msgid "Warning Message"
msgstr "Warnmeldung"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Warning on the Picking"
msgstr "Warnung bei Kommissionierung"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Warning!"
msgstr "Warnung!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Warning: change source location"
msgstr "Warnung: Quelllagerort ändern"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Warnings"
msgstr "Warnungen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_warning_stock
msgid "Warnings for Stock"
msgstr "Warnungen fürs Lager"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__website_message_ids
msgid "Website Messages"
msgstr "Website-Nachrichten"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_scrap__website_message_ids
msgid "Website communication history"
msgstr "Website-Kommunikationsverlauf"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__base_weight
msgid "Weight"
msgstr "Gewicht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__shipping_weight
msgid "Weight for Shipping"
msgstr "Gewicht für Versand"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__base_weight
msgid "Weight of the package type"
msgstr "Gewicht des Pakettyps"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__weight_uom_name
msgid "Weight unit"
msgstr "Gewichtseinheit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Bezeichnung der Gewichtsmaßeinheit"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "Gewogenes Produkt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__wh_replenishment_option_ids
msgid "Wh Replenishment Option"
msgstr "Lagerauffülloptionen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__warehouse_selectable
msgid ""
"When a warehouse is selected for this route, this route should be seen as "
"the default route when products pass through this warehouse."
msgstr ""
"Wenn ein Lagerhaus für diese Route ausgewählt wird, sollte diese Route als "
"Standardroute angesehen werden, wenn die Produkte dieses Lagerhauses "
"passieren."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__one
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__move_type__one
msgid "When all products are ready"
msgstr "Wenn alle Produkte bereit sind"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__product_selectable
msgid ""
"When checked, the route will be selectable in the Inventory tab of the "
"Product form."
msgstr ""
"Wenn diese Option aktiviert ist, kann die Route auf der Registerkarte "
"„Lager“ des Produktformulars ausgewählt werden."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__product_categ_selectable
msgid "When checked, the route will be selectable on the Product Category."
msgstr ""
"Wenn diese Option aktiviert ist, kann die Route in der Produktkategorie "
"ausgewählt werden."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__packaging_selectable
msgid "When checked, the route will be selectable on the Product Packaging."
msgstr ""
"Wenn diese Option aktiviert ist, kann die Route für die Produktverpackung "
"ausgewählt werden."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_in_id
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "When product arrives in"
msgstr "Bei Produkteingang in"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"When products are needed in <b>%(destination)s</b>, <br> "
"<b>%(operation)s</b> are created from <b>%(source_location)s</b> to fulfill "
"the need. %(suffix)s"
msgstr ""
"Wenn Produkte in <b>%(destination)s</b> benötigt werden, <br> "
"werden<b>%(operation)s</b> aus <b>%(source_location)s</b> erstellt, um den "
"Bedarf zu decken. %(suffix)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"When products arrive in <b>%(source_location)s</b>, <br> "
"<b>%(operation)s</b> are created to send them to <b>%(destination)s</b>."
msgstr ""
"Wenn Produkte in <b>%(source_location)s</b> eingehen, <br> werden "
"<b>%(operation)s</b> erstellt, um sie in <b>%(destination)s</b> zu "
"versenden."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__location_dest_from_rule
msgid ""
"When set to True the destination location of the stock.move will be the "
"rule.Otherwise, it takes it from the picking type."
msgstr ""
"Wenn diese Option auf Wahr gesetzt ist, ist der Zielort der stock.move die "
"Regel, andernfalls wird sie vom Kommissioniertyp übernommen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__is_locked
msgid ""
"When the picking is not done this allows changing the initial demand. When "
"the picking is done this allows changing the done quantities."
msgstr ""
"Wenn die Kommissionierung nicht erfolgt, kann die Anfangsnachfrage geändert "
"werden. Wenn die Kommissionierung vorgenommen wird, können Sie die "
"erledigten Mengen ändern."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid ""
"When the virtual stock goes below the Min Quantity specified for this field,"
" Odoo generates a procurement to bring the forecasted quantity above of this"
" Min Quantity."
msgstr ""
"Wenn der virtuelle Bestand unter die für dieses Feld angegebene Mindestmenge"
" fällt, erzeugt Odoo einen Beschaffungsvorschlag, um die prognostizierte "
"Menge über die minimale Menge zu bringen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid ""
"When the virtual stock goes below the Min Quantity, Odoo generates a "
"procurement to bring the forecasted quantity up to (or near to) the Max "
"Quantity specified for this field (or to Min Quantity, whichever is bigger)."
msgstr ""
"Wenn der virtuelle Bestand unter die minimale Menge fällt, generiert Odoo "
"eine Beschaffung, um die prognostizierte Menge auf die für dieses Feld "
"angegebene maximale Menge (oder auf die minimale Menge, je nachdem, welche "
"größer ist) zu bringen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_carrier
msgid "When ticked, carrier of shipment will be propagated."
msgstr ""
"Wenn dieses Kästchen angekreuzt ist, wird das Versandunternehmen angegeben."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_cancel
msgid ""
"When ticked, if the move created by this rule is cancelled, the next move "
"will be cancelled too."
msgstr ""
"Wenn dieses Kontrollkästchen aktiviert ist, wird die nächste Aktion "
"ebenfalls abgebrochen, wenn der durch diese Regel erzeugte Vorgang "
"abgebrochen wird."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__create_backorder
msgid ""
"When validating a transfer:\n"
" * Ask: users are asked to choose if they want to make a backorder for remaining products\n"
" * Always: a backorder is automatically created for the remaining products\n"
" * Never: remaining products are cancelled"
msgstr ""
"Bei der Validierung eines Transfers:\n"
" * Fragen: Benutzer werden gebeten, zu wählen, ob sie ein Lieferrückstand für die restlichen Produkte erstellen möchten\n"
" * Immer: Ein Lieferrückstand wird automatisch für die restlichen Produkte erstellt\n"
" * Nie: Restliche Produkte werden storniert"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__owner_id
msgid ""
"When validating the transfer, the products will be assigned to this owner."
msgstr ""
"Bei der Validierung des Transfers werden die Produkte diesem Eigentümer "
"zugewiesen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__owner_id
msgid ""
"When validating the transfer, the products will be taken from this owner."
msgstr ""
"Bei der Validierung des Transfers werden die Produkte von diesem Eigentümer "
"genommen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__additional
msgid "Whether the move was added after the picking's confirmation"
msgstr ""
"Ob die Buchung nach der Bestätigung der Kommissionierung hinzugefügt wurde"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__width
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Width"
msgstr "Breite"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_width
msgid "Width must be positive"
msgstr "Breite muss positiv sein"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__wizard_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__wizard_id
msgid "Wizard"
msgstr "Assistent"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Write one lot/serial name per line, followed by the quantity."
msgstr ""
"Schreiben Sie einen Los-/Seriennamen pro Zeile, gefolgt von der Menge."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__yesterday
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Yesterday"
msgstr "Gestern"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid ""
"You are about to move quantities in a package without moving the full package.\n"
"                    Those quantities will be removed from the following package(s):"
msgstr ""
"Sie sind im Begriff, Mengen in einem Paket zu verschieben, ohne das gesamte Paket zu verschieben.\n"
"                    Diese Mengen werden aus dem/den folgenden Paket(en) entfernt:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid ""
"You are going to pick products that are not referenced\n"
"in this location. That leads to a negative stock."
msgstr ""
"Sie werden Produkte kommissionieren, die sich nicht an diesem Lagerort "
"befinden. Dies führt zu einem negativen Bestand."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid "You are good, no replenishment to perform!"
msgstr "Alles in Ordnung, keine Auffüllung nötig!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You are not allowed to change the product linked to a serial or lot number "
"if some stock moves have already been created with that number. This would "
"lead to inconsistencies in your stock."
msgstr ""
"Sie dürfen das mit einer Serien- oder Losnummer verknüpfte Produkt nicht "
"ändern, wenn bereits einige Lagerbewegungen mit dieser Nummer angelegt "
"wurden. Dies würde zu Inkonsistenzen in Ihrem Bestand führen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You are not allowed to create a lot or serial number with this operation "
"type. To change this, go on the operation type and tick the box \"Create New"
" Lots/Serial Numbers\"."
msgstr ""
"Mit dieser Vorgangsart können Sie keine Los- oder Seriennummern anlegen. Um "
"dies zu ändern, gehen Sie auf die Vorgangsart und markieren Sie das Kästchen"
" „Neue Los-/Seriennummern erstellen\"."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid ""
"You are trying to put products going to different locations into the same "
"package"
msgstr ""
"Sie versuchen, Produkte, die sich an verschiedenen Orten befinden, in "
"dasselbe Paket zu packen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You are using a unit of measure smaller than the one you are using in order "
"to stock your product. This can lead to rounding problem on reserved "
"quantity. You should use the smaller unit of measure possible in order to "
"valuate your stock or change its rounding precision to a smaller value "
"(example: 0.00001)."
msgstr ""
"Sie verwenden eine kleinere Maßeinheit als die, die Sie für die Lagerung "
"Ihres Produkts verwenden. Dies kann zu Rundungsproblemen bei der "
"reservierten Menge führen. Sie sollten die kleinstmögliche Maßeinheit "
"verwenden, um Ihren Bestand zu ermitteln, oder die Rundungsgenauigkeit auf "
"einen kleineren Wert ändern (z. B. 0,00001)."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid ""
"You can define here the main routes that run through\n"
"                your warehouses and that define the flows of your products. These\n"
"                routes can be assigned to a product, a product category or be fixed\n"
"                on procurement or sales order."
msgstr ""
"Hier können Sie die Hauptrouten definieren, die durch\n"
"Ihre Lagerhäuser laufen und die die Warenströme bestimmen. Diese\n"
"Routen können einem Produkt oder einer Produktkategorie zugeordnet werden oder\n"
"auf einem Beschaffungs- oder Verkaufsauftrag festgelegt werden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "You can either :"
msgstr "Sie können entweder: "

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You can not change the inventory tracking of a product that is currently "
"reserved on a stock move. If you need to change the inventory tracking, you "
"should first unreserve the stock move."
msgstr ""
"Sie können die Bestandsverfolgung eines Produkts nicht ändern, das derzeit "
"in einer Lagerbuchung reserviert ist. Wenn Sie die Bestandsverfolgung ändern"
" müssen, sollten Sie zunächst die Reservierung für die Lagerbuchung "
"aufheben."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You can not change the inventory tracking of a product that was already "
"used."
msgstr ""
"Sie können nicht die Bestandsverfolgung eines Produkts ändern, das bereits "
"benutzt wurde."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You can not create a snoozed orderpoint that is not manually triggered."
msgstr ""
"Sie können keinen schlummernden Beschaffungspunkt erstellen, der nicht "
"manuell ausgelöst wird."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You can not delete moves linked to another operation"
msgstr ""
"Sie können keine Buchungen löschen, die mit einem anderen Vorgang verknüpft "
"sind"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You can not delete product moves if the picking is done. You can only "
"correct the done quantities."
msgstr ""
"Sie können keine Produktbuchungen löschen, wenn die Kommissionierung "
"stattfindet. Sie können nur die erledigten Mengen korrigieren."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "You can not enter negative quantities."
msgstr "Sie können keine negativen Mengen eingeben."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "You can only enter positive quantities."
msgstr "Sie können nur positive Mengen eingeben."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You can only move a lot/serial to a new location if it exists in a single "
"location."
msgstr ""
"Sie können ein Los/eine Serie nur an einen neuen Ort verschieben, wenn sie "
"an einem einzigen Ort existiert."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"You can only move positive quantities stored in locations used by a single "
"company per relocation."
msgstr ""
"Sie können pro Umlagerung nur positive Mengen bewegen, die an Orten gelagert"
" sind, die von einem einzigem Unternehmen genutzt werden."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "You can only process 1.0 %s of products with unique serial number."
msgstr ""
"Sie können nur 1,0 %s Produkte mit eindeutiger Seriennummer verarbeiten."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You can only snooze manual orderpoints. You should rather archive 'auto-"
"trigger' orderpoints if you do not want them to be triggered."
msgstr ""
"Sie können nur manuelle Beschaffungspunkte schlummern lassen. Sie sollte "
"Beschaffungspunkte mit „Automatischem Auslöser“ eher archivieren, wenn Sie "
"nicht möchten, dass sie ausgelöst werden."

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_config_settings.py:0
msgid ""
"You can't deactivate the multi-location if you have more than once warehouse"
" by company"
msgstr ""
"Sie können den Mehrfachstandort nicht deaktivieren, wenn Sie mehr als ein "
"Lagerhaus pro Unternehmen haben."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid "You can't disable locations %s because they still contain products."
msgstr ""
"Sie können die Standorte %s nicht deaktivieren, da sie immer noch Produkte "
"enthalten."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"You cannot archive location %(location)s because it is used by warehouse "
"%(warehouse)s"
msgstr ""
"Sie können den Lagerort %(location)s nicht archivieren, da er von Ihrem "
"Lagerhaus %(warehouse)s verwendet wird."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot cancel a stock move that has been set to 'Done'. Create a return "
"in order to reverse the moves which took place."
msgstr ""
"Sie können keine Lagerbewegungen stornieren, die als „Erledigt“ markiert "
"wurden. Erstellen Sie eine Retoure, um die erfolgten Buchungen rückgängig zu"
" machen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot change a cancelled stock move, create a new line instead."
msgstr ""
"Sie können eine stornierte Lagerbuchung nicht ändern, erstellen Sie "
"stattessen eine neue Zeile."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "You cannot change the Scheduled Date on a done or cancelled transfer."
msgstr ""
"Sie können das geplante Datum eines durchgeführten oder stornierten "
"Transfers nicht ändern."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot change the UoM for a stock move that has been set to 'Done'."
msgstr ""
"Sie können die Maßeinheit für eine Lagerbuchung, die auf „Erledigt“ gesetzt "
"wurde, nicht ändern."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You cannot change the company of a lot/serial number currently in a location"
" belonging to another company."
msgstr ""
"Sie können das Unternehmen einer Los-/Seriennummer, die sich an einem Ort "
"befindet, der einem anderen Unternehmen gehört, nicht ändern."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You cannot change the ratio of this unit of measure as some products with "
"this UoM have already been moved or are currently reserved."
msgstr ""
"Sie können das Verhältnis dieser Maßeinheit nicht ändern, da einige Produkte"
" mit dieser Maßeinheit bereits bewegt wurden oder derzeit reserviert sind."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You cannot change the unit of measure as there are already stock moves for "
"this product. If you want to change the unit of measure, you should rather "
"archive this product and create a new one."
msgstr ""
"Sie können die Maßeinheit nicht ändern, da es bereits Lagerbuchungen für "
"dieses Produkt gibt. Wenn Sie die Maßeinheit ändern möchten, sollten Sie "
"dieses Produkt lieber archivieren und ein neues Produkt anlegen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "You cannot delete a scrap which is done."
msgstr "Sie können keinen Ausschuss löschen, der erledigt ist."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You cannot directly pack quantities from different transfers into the same "
"package through this view. Try adding them to a batch picking and pack it "
"there."
msgstr ""
"Sie können über diese Ansicht nicht direkt Mengen aus unterschiedlichen "
"Transfers im selben Paket verpacken. Fügen Sie sie zu einer "
"Stapelkommissionierung hinzu und verpacken Sie sie dort."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "You cannot duplicate stock quants."
msgstr "Sie können Bestandsquanten nicht duplizieren."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "You cannot modify inventory loss quantity"
msgstr "Sie können die Bestandsschwundmenge nicht ändern"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot move the same package content more than once in the same transfer"
" or split the same package into two location."
msgstr ""
"Es ist nicht möglich, denselben Paketinhalt mehr als einmal in derselben "
"Bewegung zu verschieben oder dasselbe Paket auf zwei Orte aufzuteilen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot pack products into the same package when they are from different "
"transfers with different operation types."
msgstr ""
"Sie können keine Produkte in ein und dasselbe Paket packen, wenn sie aus "
"verschiedenen Transfers mit unterschiedlichen Vorgangsarten stammen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot perform moves because their unit of measure has a different "
"category from their product unit of measure."
msgstr ""
"Sie können die Lagerbuchungen nicht durchführen, da die Maßeinheit eine "
"andere Kategorie als die Produktmaßeinheit hat."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"You cannot set a location as a scrap location when it is assigned as a "
"destination location for a manufacturing type operation."
msgstr ""
"Sie können einen Standort nicht als Ausschussort festlegen, wenn er als "
"Zielstandort für einen Vorgang vom Typ Fertigung zugeordnet ist."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot set a scrap location as the destination location for a "
"manufacturing type operation."
msgstr ""
"Sie können einen Ausschussort nicht als Zielstandort für einen Vorgang vom "
"Typ Fertigung festlegen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot split a draft move. It needs to be confirmed first."
msgstr ""
"Sie können nicht einfach eine Buchung ohne vorherige Bestätigung aufteilen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot split a stock move that has been set to 'Done' or 'Cancel'."
msgstr ""
"Sie können eine auf „Erledigt“ oder „Abgebrochen“ gesetzte Lagerbuchung "
"nicht aufteilen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"You cannot take products from or deliver products to a location of type "
"\"view\" (%s)."
msgstr ""
"Sie können keine Produkte von einem Lagerort des Typs „Ansicht“ nehmen oder "
"dorthin liefern. (%s)."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot unreserve a stock move that has been set to 'Done'."
msgstr ""
"Sie können die Reservierung einer auf „Erledigt“ gesetzte Lagerbuchung nicht"
" aufteilen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You cannot use the same serial number twice. Please correct the serial "
"numbers encoded."
msgstr ""
"Sie können dieselbe Seriennummer nicht zweimal verwenden. Korrigieren Sie "
"die codierten Seriennummern."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot validate a transfer if no quantities are reserved. To force the "
"transfer, encode quantities."
msgstr ""
"Sie können einen Transfer nicht validieren, wenn keine Mengen reserviert "
"sind. Um den Transfer zu erzwingen, geben Sie die Mengen ein."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You can’t validate an empty transfer. Please add some products to move "
"before proceeding."
msgstr ""
"Sie können einen leeren Transfer nicht validieren. Bitte fügen Sie einige "
"Produkte hinzu, die Sie bewegen möchten, bevor Sie fortfahren."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "You have processed less products than the initial demand."
msgstr "Sie haben weniger Produkte verarbeitet als ursprünglich angefragt:"

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_config_settings.py:0
msgid ""
"You have product(s) in stock that have lot/serial number tracking enabled. \n"
"Switch off tracking on all the products before switching off this setting."
msgstr ""
"Sie haben Produkte auf Lager, für die die Nachverfolgung der Los-/Seriennummer aktiviert ist. \n"
"Schalten Sie die Nachverfolgung bei allen Produkten aus, bevor Sie diese Einstellung deaktivieren."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You have product(s) in stock that have no lot/serial number. You can assign "
"lot/serial numbers by doing an inventory adjustment."
msgstr ""
"Sie haben ein oder mehrere Produkt(e) auf Lager, die keine Los-/Seriennummer"
" haben. Sie können Los-/Seriennummern zuordnen, indem Sie eine "
"Bestandsaufnahme vornehmen."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You have to select a product unit of measure that is in the same category as"
" the default unit of measure of the product"
msgstr ""
"Sie müssen eine Maßeinheit des Produkts auswählen, die in dieselbe Kategorie"
" fällt wie die Standardmaßeinheit des Produkts."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "You may only return Done pickings."
msgstr "Sie können nur erledigte Kommissionierungen zurückgeben."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "You may only return one picking at a time."
msgstr "Sie können jeweils nur eine Kommissionierung zurückgeben."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You need to activate storage locations to be able to do internal operation "
"types."
msgstr ""
"Um interne Vorgangsarten durchführen zu können, müssen Sie Lagerorte "
"aktivieren."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "You need to select a route to replenish your products"
msgstr "Sie müssen eine Route auswählen, um Ihre Produkte aufzufüllen"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You need to supply a Lot/Serial Number for product:\n"
"%(products)s"
msgstr ""
"Sie müssen eine Los-/Seriennummer für folgende Produkte angeben:\n"
"%(products)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "You need to supply a Lot/Serial number for products %s."
msgstr "Sie müssen eine Los-/Seriennummer für Produkte %s angeben."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "You should update this document to reflect your T&amp;C."
msgstr ""
"Sie sollten dieses Dokument aktualisieren, damit es Ihre "
"Geschäftsbedingungen widerspiegelt."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"You still have ongoing operations for operation types %(operations)s in "
"warehouse %(warehouse)s"
msgstr ""
"Sie haben noch laufende Vorgänge für Kommissionierart(en) %(operations)s im "
"Lagerhaus %(warehouse)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You still have some active reordering rules on this product. Please archive "
"or delete them first."
msgstr ""
"Für dieses Produkt sind Nachbestellregeln aktiviert. Archivieren Sie diese "
"oder löschen Sie sie."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/list/inventory_report_list_model.js:0
msgid ""
"You tried to create a record that already exists. The existing record was "
"modified instead."
msgstr ""
"Sie haben versucht, einen Datensatz zu erstellen, der bereits existiert. Der"
" bestehende Datensatz wurde stattdessen geändert."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid ""
"You'll find here smart replenishment propositions based on inventory forecasts.\n"
"            Choose the quantity to buy or manufacture and launch orders in a click.\n"
"            To save time in the future, set the rules as \"automated\"."
msgstr ""
"Hier finden Sie intelligente Vorschläge für die Auffüllung auf der Grundlage von Bestandsprognosen.\n"
"Wählen Sie die zu kaufende oder zu fertigende Menge und starten Sie die Bestellung mit einem Klick.\n"
"Um in Zukunft Zeit zu sparen, können Sie die Regeln automatisieren."

#. module: stock
#: model_terms:res.company,lunch_notify_message:stock.res_company_1
msgid ""
"Your lunch has been delivered.\n"
"Enjoy your meal!"
msgstr ""
"Ihr Essen wurde geliefert.\n"
"Guten Appetit!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Your stock is currently empty"
msgstr "Ihr Bestand ist derzeit leer"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__package_label_to_print__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__print_format__zpl
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zpl
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__zpl
msgid "ZPL Labels"
msgstr "ZPL-Etiketten"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__zpl_lots
msgid "ZPL Labels - One per lot/SN"
msgstr "ZPL-Etiketten - Eins pro Los/SN"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__zpl_units
msgid "ZPL Labels - One per unit"
msgstr "ZPL-Etiketten - Eins pro Einheit"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zplxprice
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__zplxprice
msgid "ZPL Labels with price"
msgstr "ZPL-Etiketten mit Preis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "]<br/>min:"
msgstr "]<br/>Minuten:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "barcodelookup.com"
msgstr "barcodelookup.com"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "below the inventory"
msgstr "unter dem Bestand"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "bpost-Konnektor"

#. module: stock
#: model:product.removal,method:stock.removal_closest
msgid "closest"
msgstr "Nächstgelegen"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_orderpoint_search_panel.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "days"
msgstr "Tage"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before when starred"
msgstr "Tage vorher, falls mit Sternchen versehen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before/"
msgstr "Tage vorher /"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. CW"
msgstr "z. B. CW"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. Central Warehouse"
msgstr "z. B. Zentrales Lager"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "e.g. LOT-PR-00012"
msgstr "z. B. LOT-PR-00012"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "e.g. LOT/0001/20121"
msgstr "z. B. LOS/0001/20121"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. Lumber Inc"
msgstr "z. B. Lumber Inc"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "e.g. PACK0000007"
msgstr "z. B. PACK0000007"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. PO0032"
msgstr "z. B. PO0032"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Physical Locations"
msgstr "z. B. Physischer Standort"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "e.g. Receptions"
msgstr "z. B. Zugänge"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "e.g. SN000001"
msgstr "z. B. SN000001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Spare Stock"
msgstr "z. B. Ersatzlager"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "e.g. Two-steps reception"
msgstr "z. B. Zweistufiger Empfang"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "e.g. d7vctmiv2rwgenebha8bxq7irooudn"
msgstr "z. B. d7vctmiv2rwgenebha8bxq7irooudn"

#. module: stock
#: model:product.removal,method:stock.removal_fifo
msgid "fifo"
msgstr "fifo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "from location"
msgstr "vom Lagerort"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "in"
msgstr "in"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "in barcode."
msgstr "im Barcode."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "is"
msgstr "ist"

#. module: stock
#: model:product.removal,method:stock.removal_least_packages
msgid "least_packages"
msgstr "least_packages"

#. module: stock
#: model:product.removal,method:stock.removal_lifo
msgid "lifo"
msgstr "lifo"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "minimum of"
msgstr "Minimum von"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "of"
msgstr "von"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "planned on"
msgstr "geplant am"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "processed instead of"
msgstr "verarbeitet statt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_graph
msgid "report_stock_quantity_graph"
msgstr "report_stock_quantity_graph"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "reserved"
msgstr "reserviert"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "should be replenished"
msgstr "sollte aufgefüllt werden"

#. module: stock
#: model:ir.actions.server,name:stock.click_dashboard_graph
msgid "stock.click_dashboard_graph"
msgstr "stock.click_dashboard_graph"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_incoming
msgid "stock.method_action_picking_tree_incoming"
msgstr "stock.method_action_picking_tree_incoming"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_internal
msgid "stock.method_action_picking_tree_internal"
msgstr "stock.method_action_picking_tree_internal"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_outgoing
msgid "stock.method_action_picking_tree_outgoing"
msgstr "stock.method_action_picking_tree_outgoing"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__filter_for_stock_putaway_rule
msgid "stock.putaway.rule"
msgstr "stock.putaway.rule"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "the barcode app"
msgstr "die Barcode-App"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__warehouse_id
msgid ""
"the warehouse to consider for the route selection on the next procurement "
"(if any)."
msgstr ""
"das Lagerhaus, das für die Routenauswahl bei der nächsten Beschaffung (falls"
" diese durchgeführt wird) berücksichtigt wird."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "to reach the maximum of"
msgstr "zur Erreichung des Maximums von"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "units"
msgstr "Einheiten"

#. module: stock
#: model:mail.template,subject:stock.mail_template_data_delivery_confirmation
msgid ""
"{{ object.company_id.name }} Delivery Order (Ref {{ object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} Lieferauftrag (Ref {{ object.name or 'n/a' }})"
