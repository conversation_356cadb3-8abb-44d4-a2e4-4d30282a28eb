<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data>
    <!--
    Partners Extension
  -->

    <record id="view_partner_stock_form" model="ir.ui.view">
        <field name="name">res.partner.stock.property.form.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="mail.res_partner_view_form_inherit_mail"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='sales_purchases']/group" position="inside">
                    <group name="container_row_stock" groups="base.group_no_one" priority="6">
                        <group string="Inventory" name="inventory" colspan="2">
                            <field name="property_stock_customer" />
                            <field name="property_stock_supplier" />
                        </group>
                    </group>
            </xpath>
        </field>
    </record>

    <record id="view_partner_stock_warnings_form" model="ir.ui.view">
        <field name="name">res.partner.stock.warning</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <page name="internal_notes" position="inside">
                <group groups="stock.group_warning_stock">
                    <group col="2">
                        <separator string="Warning on the Picking" colspan="2"/>
                        <field name="picking_warn" nolabel="1" colspan="2" required="1"/>
                        <field name="picking_warn_msg" placeholder="Type a message..." colspan="2" nolabel="1"
                            invisible="picking_warn in (False, 'no-message')"
                            required="picking_warn and picking_warn != 'no-message'"/>
                    </group>
                </group>
            </page>

            <xpath expr="//div[@name='button_box']" position="inside">
                <button type="object"
                    name="action_view_stock_lots"
                    class="oe_stat_button" icon="fa-bars" groups="stock.group_production_lot">
                    <div class="o_stat_info">
                        <span class="o_stat_text">Lot/Serial Numbers</span>
                    </div>
                </button>
            </xpath>
        </field>
    </record>

  </data>
</odoo>
