<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mrp.BomOverviewControlPanel">
        <ControlPanel display="controlPanelDisplay">
            <t t-set-slot="control-panel-create-button">
                <button t-on-click="manufactureFromBoM" type="button" class="btn btn-primary">Manufacture</button>
            </t>
            <t t-set-slot="control-panel-always-buttons">
                <div class="o_cp_buttons">
                    <div class="o_list_buttons o_mrp_bom_report_buttons d-xl-flex gap-xl-1">
                        <t t-if="props.showVariants">
                            <button t-on-click="() => this.props.print(true)" type="button" class="btn btn-secondary text-nowrap">Print All Variants</button>
                        </t>
                        <button t-on-click="clickTogglefold" type="button" class="btn btn-secondary"><t t-esc="props.allFolded ? 'Unfold' : 'Fold'"/></button>
                    </div>
                </div>
            </t>

            <t t-set-slot="layout-actions">
            <div class="d-flex gap-1 w-100">
                <form class="d-flex flex-grow-1 gap-3 flex-column flex-md-row">
                    <label class="visually-hidden" for="bom_quantity">Quantity</label>
                    <div t-attf-class="input-group align-items-center">
                        <div class="col-4 col-md-auto pe-2 fw-bold">Quantity</div>
                        <input id="bom_quantity" type="number" step="any" t-on-change="ev => this.updateQuantity(ev)" t-on-keypress="ev => this.onKeyPress(ev)" t-att-value="props.bomQuantity" min="1" class="o_input form-control rounded-0"/>
                        <div t-if="props.showOptions.uom" t-out="props.uomName" class="d-flex align-items-center text-muted small lh-sm">Quantity</div>
                    </div>
                    <t t-if="props.showVariants">
                        <div class="input-group align-items-center">
                            <div class="col-4 col-md-auto pe-2 fw-bold">Variant</div>
                            <div class="col">
                                <Many2XAutocomplete
                                    value="props.data.name"
                                    getDomain.bind="getDomain"
                                    resModel="'product.product'"
                                    fieldString="props.data.name"
                                    activeActions="{}"
                                    update.bind="(ev) => this.props.changeVariant(ev[0]?.id)"
                                />
                            </div>
                        </div>
                    </t>
                </form>
                </div>
            </t>
            <t t-set-slot="control-panel-navigation-additional">
                <div class="d-flex gap-1 w-100 w-sm-auto">
                    <div class="col d-flex gap-1">
                        <t t-set-slot="control-panel-create-button">
                            <button t-on-click="() => this.props.print()" type="button" class="btn btn-primary">Print</button>
                        </t>
                        <div t-if="props.warehouses.length > 1" class="btn-group flex-grow-1 flex-md-grow-0">
                            <Dropdown items="warehousesItems">
                                <button class="btn btn-secondary o-dropdown-caret">
                                    <span class="fa fa-home"/> Warehouse
                                </button>
                            </Dropdown>
                        </div>
                        <BomOverviewDisplayFilter
                            showOptions="props.showOptions"
                            changeDisplay.bind="props.changeDisplay"/>
                    </div>
                </div>
            </t>
        </ControlPanel>
    </t>

</templates>
