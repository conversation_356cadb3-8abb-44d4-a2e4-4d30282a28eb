# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2025
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__state
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_state
msgid ""
" * Draft: The MO is not confirmed yet.\n"
" * Confirmed: The MO is confirmed, the stock rules and the reordering of the components are trigerred.\n"
" * In Progress: The production has started (on the MO or on the WO).\n"
" * To Close: The production is done, the MO has to be closed.\n"
" * Done: The MO is closed, the stock moves are posted. \n"
" * Cancelled: The MO has been cancelled, can't be confirmed anymore."
msgstr ""
" * Návrh: Výrobná objednávka nie je potvrdená.\n"
" * Potvrdené: Výrobná objednávka je potvrdená, skladové pravidlá a objednávanie komponentov je spustené..\n"
" * Spracováva sa: Výroba (na výrobobnej objenávke alebo pracovnej začala objendávke) začala..\n"
" * K zavretie:  Výroba je dokončená, vyrobnú objednávku je treba ukončiť .\n"
" * Hotové: Výrobná objednávka je dokončená, rovnako skladové pohyby. \n"
" * Zrušené: Výrobná objednávka bola zrušená a nemôže byť znovu potvrdená."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid " <br/><br/> The components will be taken from <b>%s</b>."
msgstr " <br/><br/> Komponenty budú spotrebované z <b>%s</b>."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid ""
" <br/><br/> The manufactured products will be moved towards "
"<b>%(destination)s</b>, <br/> as specified from <b>%(operation)s</b> "
"destination."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__all_available
msgid " When all components are available"
msgstr "Keď sú k dispozícii všetky komponenty"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_count
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_count
msgid "# Bill of Material"
msgstr "# Kusovník"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__used_in_bom_count
msgid "# BoM Where Used"
msgstr "# BoM, kde sa používa"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_ready_count
msgid "# Ready Work Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_count
msgid "# Work Orders"
msgstr "# Pracovné objednávky"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_template__used_in_bom_count
msgid "# of BoM Where is Used"
msgstr "# BoM Kde sa používa"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "%(name)s Sequence picking before manufacturing"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "%(name)s Sequence production"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "%(name)s Sequence stock after manufacturing"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "%(producible_qty)s Ready"
msgstr "%(producible_qty)s Pripravené"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "%(product)s: Insufficient Quantity To Unbuild"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "%(product_name)s (new) %(number_of_boms)s"
msgstr "%(product_name)s (nové) %(number_of_boms)s"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "%(qty)s %(measure)s unbuilt in %(order)s"
msgstr "%(qty)s %(measure)s rozobraté v %(order)s"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
msgid "%i work orders"
msgstr "%i pracovné objednávky"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "%s Child MO's"
msgstr "%s podriadené MO"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "%s cannot be deleted. Try to cancel them before."
msgstr "%snie je možné vymazať. Skúste ich predtým zrušiť."

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_bom_structure
msgid "'Bom Overview - %s' % object.display_name"
msgstr "'Rozpad kusovníka - %s' % object.display_name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_finished_product
msgid "'Finished products - %s' % object.name"
msgstr "'Dokončené produkty - %s' % object.name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_mrp_mo_overview
msgid "'MO Overview - %s' % object.display_name"
msgstr "'Rozpad výrobnej objednávky- %s' % object.display_name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_production_order
msgid "'Production Order - %s' % object.name"
msgstr "'Výrobná objednávka - %s' % object.name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_workorder
msgid "'Work Order - %s' % object.name"
msgstr "'Pracovná objednávka - %s' % object.name"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "(in"
msgstr "(v"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+ %d deň(dni)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "- Overview"
msgstr "- Prehľad"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"            Môže byť potrebný ručný zásah ."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_steps
msgid ""
"1 Step: Consume components from stock and produce.\n"
"              2 Steps: Pick components from stock and then produce.\n"
"              3 Steps: Pick components from stock, produce, and then move final product(s) from production area to stock."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "100"
msgstr "100"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "12345678901"
msgstr "12345678901"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_leg_product_template
msgid "18″ x 2½″ Square Leg"
msgstr "18″ x 2½″ štvorcová noha"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "2023-09-15"
msgstr "15.9.2023"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "25"
msgstr "25"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "50"
msgstr "50"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "58"
msgstr "58"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "60"
msgstr "60"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "8 GB RAM"
msgstr "8 GB RAM"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "987654321098"
msgstr "987654321098"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/workcenter_dashboard_graph/workcenter_dashboard_graph_field.js:0
msgid ":  hours"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pause\" title=\"Pause\"/>"
msgstr "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pause\" title=\"Pause\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-play fs-6\" role=\"img\" aria-label=\"Run\" title=\"Run\"/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"Stop\" title=\"Stop\"/>"
msgstr "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"Stop\" title=\"Stop\"/>"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Upload files to your product\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Nahrajte súbory k svojim produktom.\n"
"                    </p><p>\n"
"                        Túto funkciu použite na ukladanie akýchkoľvek súborov, napríklad výkresov alebo špecifikácií.\n"
"                    </p>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">In Progress</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">Late</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">To Close</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">Waiting</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"fw-bold text-nowrap\">To Produce</span>"
msgstr "<span class=\"fw-bold text-nowrap\">K výrobe</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Backorders</span>"
msgstr "<span class=\"o_stat_text\">Predobjednávky</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "<span class=\"o_stat_text\">BoM Overview</span>"
msgstr "<span class=\"o_stat_text\">Rozpad kusovníka</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Child MO</span>"
msgstr "<span class=\"o_stat_text\">Dieťa MO</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Load</span>"
msgstr "<span class=\"o_stat_text\">Načítať</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Lost</span>"
msgstr "<span class=\"o_stat_text\">Strata</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "<span class=\"o_stat_text\">Manufactured</span>"
msgstr "<span class=\"o_stat_text\">Vyrobené</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_form_inherit_mrp
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">OEE</span>"
msgstr "<span class=\"o_stat_text\">OEE</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Operations</span>"
msgstr "<span class=\"o_stat_text\">Operácie</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "<span class=\"o_stat_text\">Operations<br/>Performance</span>"
msgstr "<span class=\"o_stat_text\">Výkonnosť<br/>operácií</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Overview</span>"
msgstr "<span class=\"o_stat_text\">Prehľad</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Performance</span>"
msgstr "<span class=\"o_stat_text\">Výkon</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "<span class=\"o_stat_text\">Product Moves</span>"
msgstr "<span class=\"o_stat_text\">Skladové pohyby produktu</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span class=\"o_stat_text\">Scraps</span>"
msgstr "<span class=\"o_stat_text\">Odrezky</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Source MO</span>"
msgstr "<span class=\"o_stat_text\">Zdroj MO</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Traceability</span>"
msgstr "<span class=\"o_stat_text\">Sledovateľnosť</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Unbuilds</span>"
msgstr "<span class=\"o_stat_text\">Rozobrávky</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid ""
"<span class=\"text-muted\">Modifying the quantity to produce will also "
"modify the quantities of components to consume for this manufacturing "
"order.</span>"
msgstr ""
"<span class=\"text-muted\">Úpravou množstva na výrobu sa zmení aj množstvo "
"komponentov, ktoré sa majú spotrebovať pre túto výrobnú objednávku.</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid ""
"<span>\n"
"            Components\n"
"        </span>"
msgstr ""
"<span>\n"
"            Komponenty\n"
"        </span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span>&amp;nbsp;(minutes)</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Actions</span>"
msgstr "<span>Akcie</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span>Generate BOM</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>New</span>"
msgstr "<span>Nové</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>Orders</span>"
msgstr "<span>Objednávky</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>PLAN ORDERS</span>"
msgstr "<span>PLÁNOVAŤ OBJEDNÁVKY</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_report_delivery_no_kit_section
msgid "<span>Products not associated with a kit</span>"
msgstr "<span>Výrobky, ktoré nie sú spojené so súpravou</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Prehľady</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>WORK ORDERS</span>"
msgstr "<span>PRACOVNÉ OBJEDNÁVKY</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span>minutes</span>"
msgstr "<span>minút</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<strong class=\"mr8 oe_inline\">to</strong>"
msgstr "<strong class=\"mr8 oe_inline\">do</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Actual Duration (minutes)</strong>"
msgstr "<strong>Pribežný čas (minúty)</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Barcode</strong>"
msgstr "<strong>Čiarový kód</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Deadline:</strong><br/>"
msgstr "<strong>Termín:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Description:</strong><br/>"
msgstr "<strong>Popis:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Duration (minutes)</strong>"
msgstr "<strong>Trvanie (minúty)</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Effectiveness Category: </strong>"
msgstr "<strong>Kategória účinnosti: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "<strong>Finished Product:</strong><br/>"
msgstr "<strong>Dokončený produkt:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Is a Blocking Reason? </strong>"
msgstr "<strong>Je dôvod blokovania? </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "<strong>Manufacturing Order:</strong><br/>"
msgstr "<strong>Výrobná objednávka:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Operation</strong>"
msgstr "<strong>Operácia</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Product:</strong><br/>"
msgstr "<strong>Produkt:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity Producing:</strong><br/>"
msgstr "<strong>Vyrábané množstvo:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity to Produce:</strong><br/>"
msgstr "<strong>Množstvo k výrobe:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Reason: </strong>"
msgstr "<strong>Dôvod: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Responsible:</strong><br/>"
msgstr "<strong>Zodpovedá:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Source:</strong><br/>"
msgstr "<strong>Zdroj:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "<strong>Unit Cost</strong>"
msgstr "<strong>Jednotkové náklady</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>WorkCenter</strong>"
msgstr "<strong>Pracovné centrum</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? Môže to viesť k nezrovnalostiam v inventári."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"A BoM of type Kit is not produced with a manufacturing order.<br/>\n"
"                                Instead, it is used to decompose a BoM into its components when:"
msgstr ""
"Kusovník typu Sada sa nevyrába pomocou výrobnej objednávky.<br/>\n"
"                                Namiesto toho sa používa na rozklad kusovníka na jeho komponenty keď:"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "A Manufacturing Order is already done or cancelled."
msgstr "Výrobná objednávka je už hotová alebo zrušená."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid ""
"A product with a kit-type bill of materials can not have a reordering rule."
msgstr "Výrobok s kusovníkom typu súprava nemôže mať pravidlo objednávania."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_rule__action
msgid "Action"
msgstr "Akcia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_needaction
msgid "Action Needed"
msgstr "Potrebná akcia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__active
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__active
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__active
msgid "Active"
msgstr "Aktívne"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_ids
msgid "Activities"
msgstr "Aktivity"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_exception_decoration
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Označenie výnimky v aktivite"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_state
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_state
msgid "Activity State"
msgstr "Stav aktivity"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_type_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona typu aktivity"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Add a description..."
msgstr "Pridať popis..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Add a line"
msgstr "Pridať riadok"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Add by-products to bills of materials. This can be used to get several "
"finished products as well. Without this option you only do: A + B = C. With "
"the option: A + B = C + D."
msgstr ""
"Pridávanie vedľajších produktov do kusovníkov. Takto môžete získať niekoľko "
"hotových výrobkov naraz. Bez tejto možnosti môžete robiť len: A + B = C. S "
"touto možnosťou: A + B = C + D."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Add quality checks to your work orders"
msgstr "Pridajte kontrolu kvality do pracovných objednávok"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_tag
msgid "Add tag for the workcenter"
msgstr "Pridanie značky pre pracovisko"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_manager
msgid "Administrator"
msgstr "Správca"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__after
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "After"
msgstr "Po"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "All"
msgstr "Všetko"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__all_move_ids
msgid "All Move"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__all_move_raw_ids
msgid "All Move Raw"
msgstr ""

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_line_bom_qty_zero
msgid ""
"All product quantities must be greater or equal to 0.\n"
"Lines with 0 quantities can be used as optional lines. \n"
"You should install the mrp_byproduct module if you want to manage extra products on BoMs!"
msgstr ""
"Všetky množstvá výrobkov musia byť väčšie alebo rovné 0.\n"
"Riadky s množstvom 0 sa môžu použiť ako voliteľné riadky. \n"
"Ak chcete spravovať vedľajšie produkty kusovníka, mali by ste si nainštalovať modul mrp_byproduct!"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Allocation"
msgstr "Pridelenie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Allocation Report"
msgstr "Prehľad pridelenia"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Allocation Report Labels"
msgstr "Štítky prehľadu pridelenia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_reception_report
msgid "Allocation Report for Manufacturing Orders"
msgstr "Prehľad pridelenia pre výrobné objednávky"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__allow_workorder_dependencies
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__allow_workorder_dependencies
msgid "Allow Work Order Dependencies"
msgstr "Povolenie závislostí pracovných objednávok"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Allow manufacturing users to modify quantities to consume, without the need "
"for prior approval"
msgstr ""
"Umožniť výrobným používateľom upravovať množstvo spotrebovaných komponentov "
"bez potreby predchádzajúceho schválenia"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,help:mrp.field_stock_picking_type__use_create_components_lots
msgid "Allow to create new lot/serial numbers for the components"
msgstr "Umožnite vytvoriť nové šarže / sériové čísla komponentov"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__flexible
msgid "Allowed"
msgstr "Povolené"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reserve_visible
msgid "Allowed to Reserve Production"
msgstr "Povolené na rezerváciu výroby"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unreserve_visible
msgid "Allowed to Unreserve Production"
msgstr "Povolené na rezerváciu výroby"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__warning
msgid "Allowed with warning"
msgstr "Povolené na rezerváciu výroby"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid "Alternative Workcenters"
msgstr "Alternatívne pracovné centrá"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid ""
"Alternative workcenters that can be substituted to this one in order to "
"dispatch production"
msgstr ""
"Alternatívne pracovné centrá, ktoré je možné nahradiť týmto za účelom "
"odoslania výroby"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid ""
"An unbuild order is used to break down a finished product into its "
"components."
msgstr ""
"Príkaz na nezostavenie sa používa na rozdelenie hotového výrobku na jeho "
"súčasti."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "Apply on Variants"
msgstr "Aplikujte na varianty"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Archive Operation"
msgstr "Archivovať operácie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Archived"
msgstr "Archivovaný"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Are you sure you want to cancel this manufacturing order?"
msgstr "Ste si istí, že chcete zrušiť túto výrobnú objednávku?"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Assembling"
msgstr "Zostavovanie"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_attachment_count
msgid "Attachment Count"
msgstr "Počet príloh"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.js:0
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Attachments"
msgstr "Prílohy"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__attachments_count
msgid "Attachments Count"
msgstr "Počet príloh"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_variant_attributes
msgid "Attribute Values"
msgstr "Hodnoty atribútu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_mrp_reception_report
msgid "Auto Print Allocation Report"
msgstr "Automaticky tlačiť priradenie"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_mrp_reception_report_labels
msgid "Auto Print Allocation Report Labels"
msgstr "Automaticky tlačiť štítky pridelenia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_done_production_order
msgid "Auto Print Done Production Order"
msgstr "Automaticky tlačiť hotové výrobné objednávky"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_generated_mrp_lot
msgid "Auto Print Generated Lot/SN Label"
msgstr "Automaticky tlačiť štítky vygenerovaných šarží/SČ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_done_mrp_lot
msgid "Auto Print Produced Lot Label"
msgstr "Automaticky tlačiť štítok vyrobenej šarže"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_done_mrp_product_labels
msgid "Auto Print Produced Product Labels"
msgstr "Automaticky tlačiť štítok vyrobeného produktu"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_generated_mrp_lot
msgid ""
"Automatically print the lot/SN label when the \"Create a new serial/lot "
"number\" button is used."
msgstr ""
"Automatická tlač štítku s číslom šarže/SN pri použití tlačidla „Vytvoriť "
"nové sériové číslo/číslo šarže“."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Availabilities"
msgstr "Dostupnosť"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Availabilities on products."
msgstr "Dostupnosť produktov."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__availability
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Availability"
msgstr "Dostupnosť"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Availability Losses"
msgstr "Straty dostupnosti"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__available
msgid "Available"
msgstr "Dostupné"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "Avatar"
msgstr "Avatar"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__produce_delay
msgid ""
"Average lead time in days to manufacture this product. In the case of multi-"
"level BOM, the manufacturing lead times of the components will be added. In "
"case the product is subcontracted, this can be used to determine the date at"
" which components should be sent to the subcontractor."
msgstr ""
"Priemerný čas výroby tohto výrobku v dňoch. V prípade viacúrovňového "
"kusovníka sa pripočítajú pribežná doba výroby komponentov. V prípade, že je "
"výrobok zadaný subdodávateľovi, možno tento údaj použiť na určenie dátumu, "
"ku ktorému sa majú komponenty poslať subdodávateľovi."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Avg Cost of Components per Unit"
msgstr "Priemerné náklady na komponenty na jednotku"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Avg Cost of Operations per Unit"
msgstr "Priemerné prevádzkové náklady na jednotku"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Avg Total Cost per Unit"
msgstr "Priemerné celkové náklady na jednotku"

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Rozpad kusovníka"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__variant_bom_ids
msgid "BOM Product Variants"
msgstr "Varianty produktu kusovníka"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "BOM Product Variants needed to apply this line."
msgstr "K použitiu tohto riadku sú potrebné produktové varianty kusovníka."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_line_ids
msgid "BOM lines of the referred bom"
msgstr "Riadku Kusovníka referovaného kusovníka"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/product_catalog/kanban_controller.js:0
msgid "Back to BoM"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/product_catalog/kanban_controller.js:0
msgid "Back to Production"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder_line
msgid "Backorder Confirmation Line"
msgstr "Riadok potvrdenia doobjednania"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_backorder_line_ids
msgid "Backorder Confirmation Lines"
msgstr "Riadky potvrdenia predobjednávky"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Backorder MO"
msgstr "Predobjednávka MO"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Backorder MO's"
msgstr "Backorder MO"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__backorder_sequence
msgid "Backorder Sequence"
msgstr "Postupnosť objednávok"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__backorder_sequence
msgid ""
"Backorder sequence, if equals to 0 means there is not related backorder"
msgstr ""
"Sekvencia predobjednávky, ak sa rovná 0, znamená to, že neexistuje súvisiaca"
" doobjednávka"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__barcode
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Barcode"
msgstr "Čiarový kód"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode_batch
msgid "Based on"
msgstr "Na základe"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_batch_produce
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__production_text
msgid "Batch Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__before
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Before"
msgstr "Predtým"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom
#: model:ir.model.fields,field_description:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_bom_id
#: model:ir.model.fields,field_description:mrp.field_product_replenish__bom_id
#: model:ir.model.fields,field_description:mrp.field_stock_replenish_mixin__bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Bill of Material"
msgstr "Kusovník materiálu"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "Rozpiska materiálu"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
msgid "Bill of Material line"
msgstr "Riadok kusovníka"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_bom_id
msgid "Bill of Material used on the Production Order"
msgstr "Kusovník použitý vo výrobnej zákazke"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_open_bom
#: model:ir.actions.act_window,name:mrp.template_open_bom
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_ids
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__bom_id
#: model:ir.model.fields.selection,name:mrp.selection__product_document__attached_on_mrp__bom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Bill of Materials"
msgstr "Kusovník materiálov"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_bom_form_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom_form_action
msgid "Bills of Materials"
msgstr "Kusovníky"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__mo_bom_id
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_bom_id
msgid ""
"Bills of Materials, also called recipes, are used to autocomplete components"
" and work order instructions."
msgstr ""
"Kusovníky nazývané aj receptúry, sa používajú na spotrebovanie komponentov a"
" zápis pokynov k pracovným príkazom."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid ""
"Bills of materials allow you to define the list of required raw\n"
"                materials used to make a finished product; through a manufacturing\n"
"                order or a pack of products."
msgstr ""
"Kusovník umoňuje zadanie potrebných materiálov\n"
"                použitých pre výrobu produktu; pomocou výrobných \n"
"                objednávok alebo balením produktov."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.js:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block"
msgstr "Blokovať"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter_wo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block Workcenter"
msgstr "Blokovať Workcenter"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__blocked
msgid "Blocked"
msgstr "Blokované"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__blocked_by_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__blocked_by_workorder_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Blocked By"
msgstr "Blokované používateľom"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked Time"
msgstr "Blokovaný čas"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked hours over the last month"
msgstr "Blokované hodiny za minulý mesiac"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__name
msgid "Blocking Reason"
msgstr "Dôvod blokovania"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__needed_by_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__needed_by_workorder_ids
msgid "Blocks"
msgstr "Bloky"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_id
msgid "BoM"
msgstr "Kusovník materiálu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_line_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_line_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "BoM Components"
msgstr "Komponenty BoM"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Cost"
msgstr "BoM náklady"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "BoM Costs"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__bom_line_id
msgid "BoM Line"
msgstr "BoM riadok"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__bom_line_ids
msgid "BoM Lines"
msgstr "Riadky kusovníka"

#. module: mrp
#: model:ir.actions.client,name:mrp.action_report_mrp_bom
#: model:ir.actions.report,name:mrp.action_report_bom_structure
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Overview"
msgstr "Rozpad kusovníka"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__type
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "BoM Type"
msgstr "Typ kusovníka"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_bolt_product_template
msgid "Bolt"
msgstr "Bolt"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_byproducts_block/mrp_mo_overview_byproducts_block.xml:0
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_byproducts
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_byproducts
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "By-Products"
msgstr "Vedľajšie produkty"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_id
msgid "By-product"
msgstr "Vedľajší produkt"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "By-product %s should not be the same as BoM product."
msgstr "Vedľajší produkt %s nemôže byť rovnaký ako vyrábaný produkt."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__byproduct_id
msgid "By-product line that generated the move in a manufacturing order"
msgstr "Vedľajší produktový rad, ktorý generoval presun vo výrobnej zákazke"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__byproduct_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__byproduct_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "By-products"
msgstr "Vedľajšie produkty"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
msgid "By-products cost shares must be positive."
msgstr "Podiely nákladov vedľajších produktov musí byť kladné"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_byproduct
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_byproduct_form_view
msgid "Byproduct"
msgstr "Vedľajší produkt"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
msgid "Byproducts"
msgstr "Vedľajšie produkty"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Can't find any production location."
msgstr "Nemôžem nájsť žiadne výrobné miesto."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Cancel"
msgstr "Zrušené"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__cancel
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__cancel
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Cancelled"
msgstr "Zrušené"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"Cannot compute days to prepare due to missing route info for at least 1 "
"component or for the final product."
msgstr ""
"Nie je možné vypočítať dni na prípravu z dôvodu chýbajúcich informácií o "
"trase pre aspoň 1 komponentu alebo pre konečný produkt."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Cannot delete a manufacturing order in done state."
msgstr "Výrobnú objednávku nie je možné odstrániť v hotovom stave."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__default_capacity
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__capacity
msgid "Capacity"
msgstr "Kapacita"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_capacity_positive_capacity
msgid "Capacity should be a positive number."
msgstr "Kapacita by mala byť kladné číslo."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_reported_from_previous_wo
msgid "Carried Quantity"
msgstr "Prenesené množstvo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Catalog"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__loss_type
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Category"
msgstr "Kategória"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Center A"
msgstr "Centrum A"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Change Product Qty"
msgstr "Zmeniť množ. produktu"

#. module: mrp
#: model:ir.model,name:mrp.model_change_production_qty
msgid "Change Production Qty"
msgstr "Zmena množstva výroby"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_change_production_qty
msgid "Change Quantity To Produce"
msgstr "Zmeňte množstvo na výrobu"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"Changing the product or variant will permanently reset all previously "
"encoded variant-related data."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Check availability"
msgstr "Skontrolovať dostupnosť"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/wizard/stock_label_type.py:0
msgid "Choose Labels Layout"
msgstr "Výberte rozloženie štítkov"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Choose Type of Labels To Print"
msgstr "Vyberte typ štítkov na tlač"

#. module: mrp
#: model:ir.model,name:mrp.model_picking_label_type
msgid "Choose whether to print product or lot/sn labels"
msgstr "Vyberte, či chcete tlačiť štítky produktu alebo šarže/SČ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_stop
msgid "Cleanup Time"
msgstr "Čas čistenia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__time_stop
msgid "Cleanup Time (minutes)"
msgstr "Čas čistenia (minúty)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__code
msgid "Code"
msgstr "Kód"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__color
msgid "Color"
msgstr "Farba"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__color
msgid "Color Index"
msgstr "Index farieb"

#. module: mrp
#: model:ir.model,name:mrp.model_res_company
msgid "Companies"
msgstr "Spoločnosti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__company_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Company"
msgstr "Spoločnost"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Component"
msgstr "Komponent"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability
msgid "Component Status"
msgstr "Stav komponentov"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
msgid "Component of Draft MO"
msgstr "Súčasť návrhu MO"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__component_separator
msgid "Component separator"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_raw_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Components"
msgstr "Komponenty"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability_state
msgid "Components Availability State"
msgstr "Stav dostupnosti komponentov"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Components Available"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_src_id
msgid "Components Location"
msgstr "Umiestnenie komponentov"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__priority
msgid ""
"Components will be reserved first for the MO with the highest priorities."
msgstr "Komponenty budú rezervované najskôr pre MO s najvyššími prioritami."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Compute"
msgstr "Vypočítať"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__auto
msgid "Compute based on tracked time"
msgstr "Výpočet založený na sledovanom čase"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"Compute the days required to resupply all components from BoM, by either "
"buying or manufacturing the components and/or subassemblies."
"                                                       Also note that "
"purchase security lead times will be added when appropriate."
msgstr ""
"Vypočítajte počet dní potrebných na doplnenie všetkých komponentov z "
"kusovníka, a to buď nákupom, alebo výrobou komponentov a/alebo podzostáv."
"                                               Všimnite si tiež, že v "
"prípade potreby sa pripočítajú bezpečnostná priebežná doba nákupu."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_computed_on
msgid "Computed on last"
msgstr "Vypočítané dňa"

#. module: mrp
#: model:ir.model,name:mrp.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_configuration
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Configuration"
msgstr "Konfigurácia"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Confirm"
msgstr "Potvrdiť"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Confirmed"
msgstr "Potvrdený"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_consumed_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Consumed"
msgstr "Spotrebované"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__consume_line_ids
msgid "Consumed Disassembly Lines"
msgstr "Spotrebované demontážne linky"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__consume_unbuild_id
msgid "Consumed Disassembly Order"
msgstr "Spotrebná objednávka na demontáž"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__operation_id
msgid "Consumed in Operation"
msgstr "Spotrebované v prevádzke"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_production__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__consumption
msgid "Consumption"
msgstr "Spotreba"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_consumption_warning
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Consumption Warning"
msgstr "Varovanie spotreby"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_production__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Ku konverzii medzi mernými jednotkami môže dôjsť len v prípade, že patria do"
" rovnakej kategórie. Konverzia bude spravená na základe pomerov."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Copy Existing Operations"
msgstr "Skopírovať existujúce operácie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_copy_to_bom_tree_view
msgid "Copy selected operations"
msgstr "Skopírovať vybrané operácie"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Cost Breakdown of Products"
msgstr "Rozpad nákladov produktov"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__cost_share
#: model:ir.model.fields,field_description:mrp.field_stock_move__cost_share
msgid "Cost Share (%)"
msgstr "Zdielanie nákladov (%)"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost as it is currently accumulated"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost based on cost projection"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost based on related replenishments. Otherwise cost from product form"
msgstr ""
"Náklady na základe súvisiacich doplnení. Inak náklady z formulára výrobku"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost based on the BoM"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Cost of Components per unit"
msgstr "Náklady komponentov na jednotku"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Cost of Operations per unit"
msgstr "Náklady na operácie na jednotku"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__costs_hour
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__costs_hour
msgid "Cost per hour"
msgstr "Cena za hodinu"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Costing Information"
msgstr "Cenová informácia"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
msgid "Costs"
msgstr "Náklady"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking__production_count
msgid "Count of MO generated"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_backorder_count
msgid "Count of linked backorder"
msgstr "Počet prepojených objednávok"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__use_create_components_lots
msgid "Create New Lots/Serial Numbers for Components"
msgstr "Vytvorte nové dávky / sériové čísla pre komponenty"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create a Backorder"
msgstr "Vytvorte predobjednávky"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid ""
"Create a backorder if you expect to process the remaining products later. Do"
" not create a backorder if you will not process the remaining products."
msgstr ""
"Ak plánujete neskoršie spracovanie zostávajúcich produktov, vytvorte si "
"nevybavenú objednávku. Nevyrábajte nevybavené objednávky, ak zostávajúce "
"produkty nespracujete."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid "Create a new operation"
msgstr "Vytvorte novú operáciu"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid "Create a new work center"
msgstr "Vytvorte nové pracovné centrum"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_workcenter_report
msgid "Create a new work orders performance"
msgstr "Vytvorte nový výkon pracovných príkazov"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__days_to_prepare_mo
msgid ""
"Create and confirm Manufacturing Orders this many days in advance, to have enough time to replenish components or manufacture semi-finished products.\n"
"Note that security lead times will also be considered when appropriate."
msgstr ""
"Vytvárajte a potvrdzujte výrobné objednávky toľko dní vopred, aby ste mali dostatok času na doplnenie komponentov alebo výrobu polotovarov.\n"
"Upozorňujeme, že v prípade potreby sa zohľadní aj bezpečnostná pribežná doba."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create backorder"
msgstr "Vytvorte nevybavené objednávky"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr "Vytvárajte prispôsobiteľné pracovné hárky pre kontroly kvality"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__allow_operation_dependencies
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__allow_operation_dependencies
msgid ""
"Create operation level dependencies that will influence both planning and "
"the status of work orders upon MO confirmation. If this feature is ticked, "
"and nothing is specified, Odoo will assume that all operations can be "
"started simultaneously."
msgstr ""
"Vytvorenie závislostí na úrovni operácií, ktoré ovplyvnia plánovanie aj stav"
" pracovných príkazov po potvrdení MO. Ak je táto funkcia začiarknutá a nie "
"je nič zadané, Odoo bude predpokladať, že všetky operácie môžu byť spustené "
"súčasne."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__created_production_id
msgid "Created Production Order"
msgstr "Vytvorená výrobná zákazka"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Creates a new serial/lot number"
msgstr "Vytvorí nové sériové číslo / číslo šarže"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__currency_id
msgid "Currency"
msgstr "Mena"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Aktuálne množstvo produktov.\n"
"\n"
"V kontexte s jedným Umiestneniením zásob, toto zahŕňa aj tovar uložené na tomto mieste, alebo hociktorom z jeho potomkov.\n"
"\n"
"V kontexte s jedným Skladom, toto zahŕňa aj tovar uložený v sortimente tohto skladu, alebo niektorom z jeho potomkov.\n"
"\n"
"uložené v Umiestnení zásob  Skladu tohoto Obchodu, alebo niektorom z jeho potomkov.\n"
"\n"
"V opačnom prípade, toto zahŕňa tovar uložený v ktoromkoľvek Umiestení zásob typu \"interné\""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_producing
msgid "Currently Produced Quantity"
msgstr "Momentálne vyrobené množstvo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_description_variants
msgid "Custom Description"
msgstr "Vlastný popis"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Date"
msgstr "Dátum"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__search_date_category
msgid "Date Category"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Date by Month"
msgstr "Dátum po mesiacoch"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_finished
msgid ""
"Date you expect to finish production or actual date you finished production."
msgstr ""
"Dátum, kedy očakávate ukončenie výroby, alebo skutočný dátum ukončenia "
"výroby."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_start
msgid ""
"Date you plan to start production or date you actually started production."
msgstr ""
"Dátum, kedy plánujete začať výrobu, alebo dátum, kedy ste výrobu skutočne "
"začali."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Date: Last 365 Days"
msgstr "Dátum: Posledných 365 dní"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_pdf_line
msgid "Days"
msgstr "Dni"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "Days to Supply Components"
msgstr "Dni na dodanie komponentov"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__days_to_prepare_mo
msgid "Days to prepare Manufacturing Order"
msgstr "Dni na prípravu výrobnej objednávky"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_deadline
msgid "Deadline"
msgstr "Uzávierka"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Default Duration"
msgstr "Predvolené trvanie"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__use_manufacturing_lead
msgid "Default Manufacturing Lead Time"
msgstr "Predvolená doba výroby"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__default_capacity
msgid ""
"Default number of pieces (in product UoM) that can be produced in parallel "
"(at the same time) at this work center. For example: the capacity is 5 and "
"you need to produce 10 units, then the operation time listed on the BOM will"
" be multiplied by two. However, note that both time before and after "
"production will only be counted once."
msgstr ""
"Predvolený počet kusov (v MJ výrobku), ktoré sa môžu vyrábať paralelne (v "
"rovnakom čase) na tomto pracovisku. Napríklad: kapacita je 5 a potrebujete "
"vyrobiť 10 kusov, potom sa čas operácie uvedený v kusovníku vynásobí dvoma. "
"Nezabudnite však, že čas pred výrobou aj po výrobe sa započíta len raz."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Predvolená merná jednotka používaná pre všetky skladové operácie."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid ""
"Define the components and finished products you wish to use in\n"
"                bill of materials and manufacturing orders."
msgstr ""
"Definujte komponenty a hotové výrobky, ktoré chcete použiť v\n"
"kusovníkov a výrobných objednávkach."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__resource_calendar_id
msgid ""
"Define the working schedule of the resource. If not set, the resource will "
"have fully flexible working hours."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__consumption
msgid ""
"Defines if you can consume more or less components than the quantity defined on the BoM:\n"
"  * Allowed: allowed for all manufacturing users.\n"
"  * Allowed with warning: allowed for all manufacturing users with summary of consumption differences when closing the manufacturing order.\n"
"  Note that in the case of component Highlight Consumption, where consumption is registered manually exclusively, consumption warnings will still be issued when appropriate also.\n"
"  * Blocked: only a manager can close a manufacturing order when the BoM consumption is not respected."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delay_alert_date
msgid "Delay Alert Date"
msgstr "Výstraha oneskorenia dátum"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Delayed Productions"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Delegate part of the production process to subcontractors"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delivery_count
msgid "Delivery Orders"
msgstr "Dodacie objednávky"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__description
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_note
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description"
msgstr "Popis"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description of the work center..."
msgstr "Opis pracovného centra ..."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_dest_id
msgid "Destination Location"
msgstr "Cieľová lokácia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unbuild_id
msgid "Disassembly Order"
msgstr "Objednávka na demontáž"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Discard"
msgstr "Zrušiť"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.xml:0
msgid "Display"
msgstr "Zobraziť"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__display_name
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__display_name
msgid "Display Name"
msgstr "Zobrazovaný Názov"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lot_ids
msgid "Display the serial number shortcut on the moves"
msgstr "Na ťahoch zobrazte skratku sériového čísla"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Displays the consumed Lot/Serial Numbers."
msgstr "Zobrazí spotrebované čísla šarží/sériové čísla."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Displays the produced Lot/Serial Numbers."
msgstr "Zobrazí vyrobené šarže/sériové čísla."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "Do you confirm you want to unbuild"
msgstr "Potvrdzujete, že chcete zrušiť stavbu"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Documentation"
msgstr "Dokumentácia"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.xml:0
#: model:ir.model.fields,field_description:mrp.field_stock_move__is_done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Done"
msgstr "Hotové"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__draft
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__draft
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Draft"
msgstr "Návrh"

#. module: mrp
#: model:product.template,name:mrp.product_product_drawer_drawer_product_template
msgid "Drawer Black"
msgstr "Čierna zásuvka "

#. module: mrp
#: model:product.template,name:mrp.product_product_drawer_case_product_template
msgid "Drawer Case Black"
msgstr "Puzdro zásuvky čierne"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_drawer_drawer_product_template
msgid "Drawer on casters for great usability."
msgstr "Zásuvka na kolieskach pre skvelú použiteľnosť."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__duration
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Duration"
msgstr "Obdobie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Duration (minutes)"
msgstr "Trvanie (minúty)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode
msgid "Duration Computation"
msgstr "Výpočet doby trvania"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_percent
msgid "Duration Deviation (%)"
msgstr "Odchýlka trvania (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_unit
msgid "Duration Per Unit"
msgstr "Trvanie na jednotku"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_type
msgid "Effectiveness"
msgstr "Efektivita"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_type
msgid "Effectiveness Category"
msgstr "Kategória účinnosti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_finished
msgid "End"
msgstr "Koniec"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_end
msgid "End Date"
msgstr "Dátum ukončenia"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__tracking
#: model:ir.model.fields,help:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,help:mrp.field_mrp_workorder__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Zabezpečte sledovateľnosť skladovateľného produktu vo vašom sklade."

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason1
msgid "Equipment Failure"
msgstr "Zlyhanie zariadenia"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Estimated %s"
msgstr "Odhadnuté %s"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s) occurred on the manufacturing order(s):"
msgstr "Výnimky nastali vo výrobných zákazkách:"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s):"
msgstr "Výnimka(y)"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Exp %s"
msgstr "Exp %s"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__expected
msgid "Expected"
msgstr "Očakávané"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Expected %s"
msgstr "Očakávané %s"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__duration_expected
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_expected
msgid "Expected Duration"
msgstr "Predpokladané trvanie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Expected Duration (minutes)"
msgstr "Predpokladané trvanie (minúty)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__production_text_help
msgid "Explanation for batch production"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Files attached to the product."
msgstr "Súbory priložené k produktu."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Filters"
msgstr "Filtre"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_final_id
msgid "Final Location from procurement"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__done
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Finished"
msgstr "Ukončené"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__order_finished_lot_id
msgid "Finished Lot/Serial Number"
msgstr "Hotová šarža / sériové číslo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_finished_ids
msgid "Finished Moves"
msgstr "Hotové pohyby"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__finished_move_line_ids
msgid "Finished Product"
msgstr "Dokončený produkt"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_finished_product
msgid "Finished Product Label (PDF)"
msgstr "Štítok hotového výrobku (PDF)"

#. module: mrp
#: model:ir.actions.report,name:mrp.label_manufacture_template
msgid "Finished Product Label (ZPL)"
msgstr "Štítok hotového výrobku (ZPL)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_finished_ids
msgid "Finished Products"
msgstr "Hotové výrobky"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_dest_id
msgid "Finished Products Location"
msgstr "Lokácia hotových produkov"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lot_name
msgid "First Lot/SN"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__consumption
msgid "Flexible Consumption"
msgstr "Flexibilná spotreba"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.js:0
msgid "Fold"
msgstr "Zložiť"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_follower_ids
msgid "Followers"
msgstr "Odberatelia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_partner_ids
msgid "Followers (Partners)"
msgstr "Odberatelia (partneri)"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_type_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Úžasná ikona fronty napr. fa-tasks"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Force"
msgstr "Sila"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Forecast"
msgstr "Odhad"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Forecast Report"
msgstr "Prognóza"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Odhadované množstvo (vyrátané ako Množstvo skladom - Odchádzajúce + Prichádzajúce)\n"
"\n"
"V kontexte s jedným Umiestnenením zásob, toto zahŕňa aj tovar uložený na tomto mieste, alebo hociktorom z jeho potomkov. \n"
"\n"
"V kontexte s jedným Skladom, toto zahŕňa aj tovar uložený v sortimente tohto skladu, alebo niektorým z jeho potomkov. \n"
"\n"
"V opačnom prípade toto zahŕňa tovar uložený v ktoromkoľvek Umiestení zásob typu \"interné\""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Forecasted"
msgstr "Prognózované"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__forecasted_issue
msgid "Forecasted Issue"
msgstr "Očakávaný problém"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Free to Use"
msgstr "Voľné k použitiu"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Free to Use / On Hand"
msgstr "Voľné k použitiu / skladom"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Free to use / On Hand"
msgstr "Voľné k použitiu / skladom"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "From"
msgstr "Od"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Fully Productive"
msgstr "Plne produktívny"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason7
msgid "Fully Productive Time"
msgstr "Plne produktívny čas"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Future Activities"
msgstr "Budúce aktivity"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "General Information"
msgstr "Všeobecné informácie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Generate"
msgstr "Všeobecné"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Generate Serial Numbers"
msgstr "Generujte sériová čísla"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Generate a new BoM from this Manufacturing Order"
msgstr "Vytvorte nový kusovník z tejto výrobnej objednávky"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__generated_mrp_lot_label_to_print
msgid "Generated Lot/SN Label to Print"
msgstr "Vytvorené šarže/SČ na tlač"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
msgid "Get statistics about the work orders duration related to this routing."
msgstr ""
"Získajte štatistiku o trvaní pracovných príkazov súvisiacich s týmto "
"smerovaním."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__sequence
msgid ""
"Gives the sequence order when displaying a list of routing Work Centers."
msgstr ""
"Dáva sekvenčné poradie pri zobrazovaní zoznamu trás pracovných centier."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__sequence
msgid "Gives the sequence order when displaying a list of work centers."
msgstr "Pri zobrazení zoznamu pracovných centier dáva poradie sekvencií."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__sequence
msgid "Gives the sequence order when displaying."
msgstr "Dáva sekvenčné poradie pri zobrazovaní."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__google_slide
msgid "Google Slide"
msgstr "Google slajdy"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Google Slide Link"
msgstr "Odkaz Google prezentácia"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Group By"
msgstr "Zoskupiť podľa"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Group By..."
msgstr "Zoskupiť podľa..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Group by..."
msgstr "Zoskupiť podľa..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Hardware"
msgstr "Hardware"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_produced
msgid "Has Been Produced"
msgstr "Bol vyrobený"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking__has_kits
msgid "Has Kits"
msgstr "Obsahuje súpravy"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_production__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__has_message
msgid "Has Message"
msgstr "Má správu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__has_routing_lines
msgid "Has Routing Lines"
msgstr "je použité v operáciách"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__has_worksheet
msgid "Has Worksheet"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__product_document__attached_on_mrp__hidden
msgid "Hidden"
msgstr "Skryté"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__manual_consumption
msgid "Highlight Consumption"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__costs_hour
msgid "Hourly processing cost."
msgstr "Hodinové výrobné náklady."

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Hours"
msgstr "Hodiny"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__id
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__id
msgid "ID"
msgstr "ID"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_exception_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_exception_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona indikujúca výnimočnú aktivitu."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_id
msgid ""
"If a product variant is defined the BOM is available only for this product."
msgstr ""
"Ak je definovaná produktová varianta, kusovník je dostupný ibe pre tento "
"produkt."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ak označené, potom nové správy vyžadujú vašu pozornosť."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ak označené, potom majú niektoré správy chybu dodania."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__propagate_cancel
msgid ""
"If checked, when the previous move of the move (which was generated by a "
"next procurement) is cancelled or split, the move generated by this move "
"will too"
msgstr ""
"Ak zaškrtnuté, keď predchádzajúci pohyb pohybu (ktorý bol genrovaný ďalším "
"obstarávaním) je zrušený alebo rozdelený, pohyb generovaný týmto pohybom "
"bude tiež"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Ak je aktívne pole nastavené na hodnotu False, umožní vám to skryť záznam "
"prostriedku bez jeho odstránenia."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_mrp_reception_report_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the allocation "
"report labels of a MO when it is done."
msgstr ""
"Ak je toto zaškrtávacie políčko zaškrtnuté, Odoo automaticky vytlačí štítky "
"pridelení výrobného príkazu po jeho dokončení."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_mrp_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically print the allocation "
"report of a MO when it is done and has assigned moves."
msgstr ""
"Ak je toto zaškrtávacie políčko zaškrtnuté, Odoo automaticky vytlačí "
"pridelení výrobného príkazu, keď je dokončený a má priradené skladové "
"pohyby."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_done_mrp_lot
msgid ""
"If this checkbox is ticked, Odoo will automatically print the lot/SN label "
"of a MO when it is done."
msgstr ""
"Ak je toto políčko zaškrtnuté, Odoo automaticky vytlačí štítok šarže/SN "
"výrobného príkazu po jeho dokončení."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_done_mrp_product_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the product labels"
" of a MO when it is done."
msgstr ""
"Ak je toto zaškrtávacie políčko zaškrtnuté, Odoo automaticky vytlačí štítky "
"produktov výrobného príkazu po jeho dokončení."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_done_production_order
msgid ""
"If this checkbox is ticked, Odoo will automatically print the production "
"order of a MO when it is done."
msgstr ""
"Ak je toto políčko zaškrtnuté, Odoo automaticky vytlačí výrobný príkaz po "
"jeho dokončení."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Impacted Transfer(s):"
msgstr "Ovplyvnený prenos(y):"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "Import Template for Bills of Materials"
msgstr "Import šablóny pre kusovníky"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"Impossible to plan the workorder. Please check the workcenter "
"availabilities."
msgstr ""
"Nie je možné naplánovať pracovný príkaz. Skontrolujte dostupnosť pracoviska."

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
msgid "Impossible to plan. Please check the workcenter availabilities."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__progress
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__progress
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "In Progress"
msgstr "Spracováva sa"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "In Transit"
msgstr "V presune"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_view_search_catalog
msgid "In the BoM"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_view_search_catalog
msgid "In the MO"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_deadline
msgid ""
"Informative date allowing to define when the manufacturing order should be "
"processed at the latest to fulfill delivery on time."
msgstr ""
"Informačný dátum umožňujúci definovať, kedy by sa mala výrobná zákazka "
"spracovať najneskôr, aby sa dodávka splnila včas."

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_moves
msgid "Inventory Moves"
msgstr "Inventúrne pohyby"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__move_line_ids
msgid ""
"Inventory moves for which you must scan a lot number at this work order"
msgstr ""
"Presuny inventára, pre ktoré musíte v tomto pracovnom poradí naskenovať "
"veľké množstvo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_delayed
msgid "Is Delayed"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_is_follower
msgid "Is Follower"
msgstr "Odberateľ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__is_kits
#: model:ir.model.fields,field_description:mrp.field_product_template__is_kits
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__product_is_kit
msgid "Is Kits"
msgstr "Je súpravou"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_locked
msgid "Is Locked"
msgstr "Uzamknutý"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__manual
msgid "Is a Blocking Reason"
msgstr "Je dôvod blokovania"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_user_working
msgid "Is the Current User Working"
msgstr "Pracuje súčasný používateľ"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "It has already been unblocked."
msgstr "To už bolo odblokované."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"It is not possible to unplan one single Work Order. You should unplan the "
"Manufacturing Order instead in order to unplan all the linked operations."
msgstr ""
"Nie je možné zrušiť plánovanie jednej pracovnej objednávky. Pre zrušenie "
"plánovania všetkých prepojených operácií by ste mali zrušiť plánovanie "
"výrobného príkazu."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_planned
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_planned
msgid "Its Operations are Planned"
msgstr "Jeho operácie sú plánované"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__json_popover
msgid "JSON data for the popover widget"
msgstr "Údaje JSON pre miniaplikáciu pre popover"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "John Doe"
msgstr "Janko Mrkvička"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "Riadiaci panel v kanbanovej grafike"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__description_bom_line
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__description_bom_line
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__bom_id
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__phantom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Kit"
msgstr "Súprava"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Laptop"
msgstr "Laptop"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Laptop Model X"
msgstr "Laptop Model X"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Laptop model X"
msgstr "Laptop model X"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Laptop with 16GB RAM"
msgstr "Laptop s 16GB RAM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__last_working_user_id
msgid "Last user that worked on this work order."
msgstr "Posledný užívateľ, ktorý pracoval na tejto pracovnej objednávke."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__late
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Late"
msgstr "Meškajúci"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Activities"
msgstr "Omeškané aktivity"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Availability"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late MO or Late delivery of components"
msgstr "Neskoré MO alebo neskoré dodanie komponentov"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__components_availability
msgid ""
"Latest component availability status for this MO. If green, then the MO's "
"readiness status is ready, as per BOM configuration."
msgstr ""
"Najnovší stav dostupnosti komponentov pre túto výrobnú objednávku. Ak je "
"zelený, potom stav pripravenosti komponentov zodpovedá konfigurácii "
"kusovníka."

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_wood_ply_product_template
msgid "Layers that are stick together to assemble wood panels."
msgstr "Vrstvy, ktoré sa navzájom spájajú a vytvárajú drevené panely."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Lead Time"
msgstr "Dodacia doba"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
msgid "Lead Times"
msgstr "Priebežné doby"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__leave_id
msgid "Leave"
msgstr "Voľno"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_product_document__attached_on_mrp
msgid ""
"Leave hidden if document only accessible on product form.\n"
"Select Bill of Materials to visualise this document as a product attachment when this product is in a bill of material."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning_line
msgid "Line of issue consumption"
msgstr "Riadok spotreby"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__location_id
msgid "Location"
msgstr "Miesto"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_id
msgid "Location where the product you want to unbuild is."
msgstr "Umiestnenie, kde sa nachádza produkt, ktorý chcete zrušiť."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_src_id
msgid "Location where the system will look for components."
msgstr "Lokácia kde bude systém hľadať komponenty. "

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "Lokácie kde systém uskladní hotové výrobky."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_dest_id
msgid ""
"Location where you want to send the components resulting from the unbuild "
"order."
msgstr ""
"Miesto, kam chcete odoslať komponenty pochádzajúce z objednávky obnovenia."

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_lock_unlock
msgid "Lock/Unlock"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_id
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Loss Reason"
msgstr "Dôvod straty"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid "Lot %s does not exist."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lots_quantity_separator
msgid "Lot quantity separator"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lots_separator
msgid "Lot separator"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Lot/SN Label"
msgstr "Štítok šarže/SČ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__done_mrp_lot_label_to_print
msgid "Lot/SN Label to Print"
msgstr "Štítok šarže/SČ k tlači"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Lot/SN Labels"
msgstr "Štítky šarží/SČ"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_lot
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Lot/Serial"
msgstr "Šarža/sériové"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__lot_producing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__lot_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__finished_lot_id
msgid "Lot/Serial Number"
msgstr "Šarža/sériové číslo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Lot/Serial Numbers"
msgstr "Šarže / sériové čísla"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_traceability
msgid "Lots/Serial Numbers"
msgstr "Šarže / sériové čísla"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_backorder_id
msgid "MO Backorder"
msgstr "Výrobná doobjednávka"

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_cancelled
#: model:mail.message.subtype,name:mrp.mrp_mo_in_cancelled
msgid "MO Cancelled"
msgstr ""

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_confirmed
#: model:mail.message.subtype,name:mrp.mrp_mo_in_confirmed
msgid "MO Confirmed"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "MO Cost"
msgstr "Náklady výrobnej objednávky"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "MO Costs"
msgstr "Náklady výrobnej objednávky"

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_done
#: model:mail.message.subtype,name:mrp.mrp_mo_in_done
msgid "MO Done"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "MO Generated by %s"
msgstr "Výrobné objednávky generované z %s"

#. module: mrp
#: model:ir.actions.client,name:mrp.action_report_mo_overview
#: model:ir.actions.report,name:mrp.action_report_mrp_mo_overview
msgid "MO Overview"
msgstr "Prehľad výrobnej objednávky"

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "Náklad výrobnej objednávky"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "MO Pending"
msgstr ""

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_progress
#: model:mail.message.subtype,name:mrp.mrp_mo_in_progress
msgid "MO Progress"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reservation_state
msgid "MO Readiness"
msgstr "Pripravenosť výrobnej objednávky"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "MO Ready"
msgstr "Výrobná objednávka pripravená"

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_to_close
#: model:mail.message.subtype,name:mrp.mrp_mo_in_to_close
msgid "MO To Close"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_document__attached_on_mrp
msgid "MRP : Visible at"
msgstr ""

#. module: mrp
#: model:ir.actions.client,name:mrp.mrp_reception_action
msgid "MRP Reception Report"
msgstr "Prehľad dokončenej výroby"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_routings
msgid "MRP Work Orders"
msgstr "Pracovné objednávky"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss_type
msgid "MRP Workorder productivity losses"
msgstr "Stráta produktivity pracovných objednávok"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "MRP-001"
msgstr "MRP-001"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_routings
msgid "Manage Work Order Operations"
msgstr "Spravujte operácie výrobných objednávok"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__manual_consumption
msgid "Manual Consumption"
msgstr "Ručná spotreba"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid "Manual Duration"
msgstr "Ručne vykázané trvanie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manuf. Lead Time"
msgstr "Priebežná doba výroby"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_steps
#: model:ir.model.fields.selection,name:mrp.selection__stock_rule__action__manufacture
#: model:stock.route,name:mrp.route_warehouse0_manufacture
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_stock_rule
msgid "Manufacture"
msgstr "Výroba"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__mrp_one_step
msgid "Manufacture (1 step)"
msgstr "Výroba (v 1 kroku)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_mto_pull_id
msgid "Manufacture MTO Rule"
msgstr "Pravidlo výroby na zákazku (MTO)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_pull_id
msgid "Manufacture Rule"
msgstr "Pravidlo výroby"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "Manufacture Security Lead Time"
msgstr "Bezpečnostná pribežná doba výroby"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__normal
msgid "Manufacture this product"
msgstr "Vyrobiť tento produkt"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_to_resupply
msgid "Manufacture to Resupply"
msgstr "Výroba podľa požiadaviek"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__mrp_product_qty
#: model:ir.model.fields,field_description:mrp.field_product_template__mrp_product_qty
msgid "Manufactured"
msgstr "Vyrobené"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "Manufactured Products"
msgstr "Vyrobené produkty"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Manufactured in the last 365 days"
msgstr "Vyrobené s posledných 365 dní"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.actions.client,name:mrp.action_mrp_display_fullscreen
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__code__mrp_operation
#: model:ir.ui.menu,name:mrp.menu_mrp_root
#: model_terms:ir.ui.view,arch_db:mrp.product_document_form
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Manufacturing"
msgstr "Výroba"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_buttons.xml:0
msgid "Manufacturing Forecast"
msgstr "Prognóza výroby"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__produce_delay
#: model:ir.model.fields,field_description:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__manufacturing_lead
msgid "Manufacturing Lead Time"
msgstr "Výrobná dodacia lehota"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manu_type_id
msgid "Manufacturing Operation Type"
msgstr "Typ operácie - výroba"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__production_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Manufacturing Order"
msgstr "Výrobná objednávka"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_product_mrp_production_workcenter
#: model:ir.actions.act_window,name:mrp.action_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_production_action
#: model:ir.actions.act_window,name:mrp.mrp_production_action_picking_deshboard
#: model:ir.ui.menu,name:mrp.menu_mrp_production_action
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_view_activity
#: model_terms:ir.ui.view,arch_db:mrp.view_production_calendar
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_production_pivot
msgid "Manufacturing Orders"
msgstr "Výrobné objednávky"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are in confirmed state."
msgstr "Potvrdené výrobné objednávky."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__ready_to_produce
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manufacturing Readiness"
msgstr "Pripravenosť výroby"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Manufacturing Reference"
msgstr "Výrobná referencia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__manufacturing_visibility_days
msgid "Manufacturing Visibility Days"
msgstr "Dni viditelnosti pre výrobu"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc."
msgstr ""
"Výrobné operácie pribehajú na pracoviskách. Pracovisko sa môže skladať z pracovníkov a/alebo \n"
"        strojov, používajú sa na kalkuláciu nákladov, rozvrhovanie, plánovanie kapacít atď."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc.\n"
"                They can be defined via the configuration menu."
msgstr ""
"Výrobné operácie prebehajú na pracoviskách. Pracovisko sa môže skladať z pracovníkov \n"
"                a/alebo strojov, používajú sa na kalkuláciu nákladov, rozvrhovanie, plánovanie kapacít atď.\n"
"                Možno ich definovať prostredníctvom konfiguračnej ponuky."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reservation_state
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_availability
msgid ""
"Manufacturing readiness for this MO, as per bill of material configuration:\n"
"            * Ready: The material is available to start the production.\n"
"            * Waiting: The material is not available to start the production.\n"
msgstr ""
"Manufacturing readiness for this MO, as per bill of material configuration:\n"
"          * Ready: The material is available to start the production.\n"
"          * Waiting: The material is not available to start the production.\n"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_picking_tree_mrp_operation
#: model:ir.actions.act_window,name:mrp.action_picking_tree_mrp_operation_graph
#: model:ir.ui.menu,name:mrp.mrp_operation_picking
msgid "Manufacturings"
msgstr "Výroba"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_mark_done
msgid "Mark as Done"
msgstr "Označiť ako hotovo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Mass Produce"
msgstr "Hromadná výroba"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_mps
msgid "Master Production Schedule"
msgstr "Hlavný harmonogram výroby"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason0
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Material Availability"
msgstr "Dostupnosť materiálu"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_merge
msgid "Merge"
msgstr "Zlúčiť"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_has_error
msgid "Message Delivery error"
msgstr "Chyba zobrazovania správ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_ids
msgid "Messages"
msgstr "Správy"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Pravidlo minimálneho inventára"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Minutes"
msgstr "Minúty"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Miscellaneous"
msgstr "Účtovné záznamy"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_byproduct_ids
msgid "Move Byproduct"
msgstr "Pohyb vedľajšieho produktu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr "Pohyby na sledovanie"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_consumption_warning_line_ids
msgid "Mrp Consumption Warning Line"
msgstr "Riadok s varovaním spotreby"

#. module: mrp
#: model:ir.actions.client,name:mrp.action_mrp_display
msgid "Mrp Display"
msgstr "Zobrazenie"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_procurement_group__mrp_production_ids
msgid "Mrp Production"
msgstr "Výroba"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_count
msgid "Mrp Production Count"
msgstr "Počítadlo výroby"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__my_activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__my_activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Termín mojej aktivity"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "My MOs"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__never_product_template_attribute_value_ids
msgid "Never attribute values"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/stock_rule.py:0
msgid "New"
msgstr "Nové"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "New BoM from %(mo_name)s"
msgstr "Nový kusovník z %(mo_name)s"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Next Activity"
msgstr "Ďalšia aktivita"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_calendar_event_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_calendar_event_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Kalendár ďalších aktivít eventu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Ďalší konečný termín aktivity"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_summary
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_summary
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_summary
msgid "Next Activity Summary"
msgstr "Zhrnutie ďalšej aktivity"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_id
msgid "Next Activity Type"
msgstr "Typ ďalšej aktivity"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "No Backorder"
msgstr "Žiadna pohľadávka"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid "No bill of materials found. Let's create one!"
msgstr "Nenašli sme žiadny kusovník. Poďme jeden vytvoriť!"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "No data available."
msgstr "Dáta nesú dostupné."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workcenter_load_report_graph
msgid "No data yet!"
msgstr "Zatiaľ žiadne údaje!"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "No manufacturing order found. Let's create one."
msgstr "Nenašla sa žiadna výrobná objednávka. Vytvorme nejakú!"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid "No product found. Let's create one!"
msgstr "Nenašli sa žiadne produkty. Vytvorte nový!"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_blocked
msgid "No productivity loss for this equipment"
msgstr "Žiadna strata produktivity tohto zariadenia"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid "No unbuild order found"
msgstr "Žiadne objednávky na rozobratie"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid "No work orders to do!"
msgstr "Žiadne pracovné objednávky!"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid ""
"No workorder currently in progress. Click to mark work center as blocked."
msgstr ""
"V súčasnosti sa nevykonáva žiadna pracovná objednávka. Kliknutím označíte "
"pracovisko ako zablokované."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__normal
msgid "Normal"
msgstr "Normálna"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__unavailable
msgid "Not Available"
msgstr "Nedostupné"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Not Ready"
msgstr "Nepripravené"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Note that another version of this BOM is available."
msgstr "Upozorňujeme, že je k dispozícii aj iná verzia tohto kusovníka."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid ""
"Note that archived work center(s): '%s' is/are still linked to active Bill "
"of Materials, which means that operations can still be planned on it/them. "
"To prevent this, deletion of the work center is recommended instead."
msgstr ""
"Všimnite si, že archivované pracovisko (pracoviská): '%s' je (sú) stále "
"prepojené s aktívnym kusovníkom materiálu, čo znamená, že na ňom (nich) "
"možno stále plánovať operácie. Aby ste tomu zabránili, odporúča sa namiesto "
"toho vymazať pracovné stredisko."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/product.py:0
msgid ""
"Note that product(s): '%s' is/are still linked to active Bill of Materials, "
"which means that the product can still be used on it/them."
msgstr ""
"Upozorňujeme, že produkt(-y): '%s' je stále prepojený na aktívne kusovníky, "
"čo znamená, že výrobok sa v nich stále môže používať."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcií"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_in_progress
msgid "Number of Manufacturing Orders In Progress"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_late
msgid "Number of Manufacturing Orders Late"
msgstr "Počet oneskorených výrobných objednávok"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_to_close
msgid "Number of Manufacturing Orders To Close"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_waiting
msgid "Number of Manufacturing Orders Waiting"
msgstr "Počet čakajúcich výrobných objednávok"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_todo
msgid "Number of Manufacturing Orders to Process"
msgstr "Počet výrobných objednávok na spracovanie"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lot_qty
msgid "Number of SN"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unbuild_count
msgid "Number of Unbuilds"
msgstr "Počet rozobratí"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chýb"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_child_count
msgid "Number of generated MO"
msgstr "Počet vytvorených výrobných objednávok"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Počet správ, ktoré vyžadujú akciu"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet doručených správ s chybou"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__capacity
msgid "Number of pieces that can be produced in parallel for this product."
msgstr "Počet kusov, ktoré možno pre tento výrobok vyrobiť paralelne."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_source_count
msgid "Number of source MO"
msgstr "Počet zdrojových výrobných objednávok"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "OEE"
msgstr "OEE"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee_target
msgid "OEE Target"
msgstr "Ciel OEE"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid ""
"Odoo opens a PDF preview by default. If you want to print instantly,\n"
"                                install the IoT App on a computer that is on the same local network as the\n"
"                                barcode operator and configure the routing of the reports."
msgstr ""
"Odoo štandardne otvára náhľad PDF. Ak chcete tlačiť okamžite,\n"
"                     nainštalujte aplikáciu IoT do počítača, ktorý je v rovnakej miestnej sieti ako\n"
"                     operátor čiarových kódov a nakonfigurujte smerovanie zostáv."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee
msgid "Oee"
msgstr "OEE"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "On Hand"
msgstr "Na sklade"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"Only manufacturing orders in either a draft or confirmed state can be %s."
msgstr "Iba výrobné objednávky v stave návrhu alebo potvrdené môžu byť %s."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Only manufacturing orders with a Bill of Materials can be %s."
msgstr "Iba výrobné objednávky s kusovníkom môžu byť %s."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Open"
msgstr "Otvorené"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view_mo_form
msgid "Open Work Order"
msgstr "Otvoriť pracovnú objednávku"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Operation"
msgstr "Operatíva"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__allow_operation_dependencies
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__allow_operation_dependencies
msgid "Operation Dependencies"
msgstr "Závislosti operácií"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__operation_id
msgid "Operation To Consume"
msgstr "Spotreba na operácii"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__picking_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_type_id
msgid "Operation Type"
msgstr "Typ operatívy "

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid ""
"Operation define that need to be done to realize a Work Order.\n"
"                Each operation is done at a specific Work Center and has a specific duration."
msgstr ""
"Definujte operácie, ktoré je potrebné vykonať na realizáciu pracovnej objednávky.\n"
"                 Každá operácia sa vykonáva na konkrétnom pracovisku a má určené trvanie."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/product.py:0
msgid "Operation not supported"
msgstr "Operácia nie je podporovaná"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_operations_block/mrp_mo_overview_operations_block.xml:0
#: model:ir.actions.act_window,name:mrp.mrp_routing_action
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__allowed_operation_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_manufacturing
#: model:ir.ui.menu,name:mrp.menu_mrp_routing_action
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_operations
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_calendar
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Operations"
msgstr "Operácie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Done"
msgstr "Dokončené operácie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Planned"
msgstr "Plánované operácie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Operations Search Filters"
msgstr "Filtre vyhľadávania operácií"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__needed_by_operation_ids
msgid "Operations that cannot start before this operation is completed."
msgstr "Operácie, ktoré sa nemôžu spustiť pred dokončením tejto operácie."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__blocked_by_operation_ids
msgid "Operations that need to be completed before this operation can start."
msgstr "Operácie, ktoré je potrebné dokončiť pred spustením tejto operácie."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__orderpoint_id
msgid "Orderpoint"
msgstr "Bod objednávky"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__order_ids
msgid "Orders"
msgstr "Objednávky"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_production
msgid "Original Production Quantity"
msgstr "Pôvodné množstvo výroby"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_outdated_bom
msgid "Outdated BoM"
msgstr "Zastaralý kusovník"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee_target
msgid "Overall Effective Efficiency Target in percentage"
msgstr "Cieľová hodnota celkovej efektívnosti zariadení v percentách"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_oee
#: model:ir.ui.menu,name:mrp.menu_mrp_workcenter_productivity_report
msgid "Overall Equipment Effectiveness"
msgstr "Celková efektivita zariadení"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee
msgid "Overall Equipment Effectiveness, based on the last month"
msgstr "Celková účinnosť zariadenia, založená na minulom mesiaci"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_oee
msgid "Overall Equipment Effectiveness: no working or blocked time"
msgstr "Celková účinnosť zariadení: nepracovný alebo blokovaný čas"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__pdf
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__done_mrp_lot_label_to_print__pdf
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__generated_mrp_lot_label_to_print__pdf
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__mrp_product_label_to_print__pdf
msgid "PDF"
msgstr "PDF"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Package barcode"
msgstr "Čiarový kód balenia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_id
msgid "Parent BoM"
msgstr "Nadradený kusovník"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__parent_product_tmpl_id
msgid "Parent Product Template"
msgstr "Nadradený produkt"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_consumption_warning_id
msgid "Parent Wizard"
msgstr "Parent Wizard"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields,help:mrp.field_mrp_workorder__worksheet_google_slide
msgid ""
"Paste the url of your Google Slide. Make sure the access to the document is "
"public."
msgstr ""
"Vložte url adresu svojej prezentácie Google. Uistite sa, že prístup k "
"dokumentu je verejný."

#. module: mrp
#: model:ir.actions.server,name:mrp.action_pause_workorders
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Pause"
msgstr "Pozastavenie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Pending"
msgstr "Nevykonané"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__performance
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__performance
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Performance"
msgstr "Výkon"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Performance Losses"
msgstr "Straty výkonu"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__performance
msgid "Performance over the last month"
msgstr "Výkonnosť za minulý mesiac"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick Components"
msgstr "Vyzdvihnúť komponenty"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick components and then manufacture"
msgstr "Vyzdvihnúť komponenty a potom výroba"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm
msgid "Pick components then manufacture (2 steps)"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick components, manufacture and then store products (3 steps)"
msgstr "Vyzdvihnúť komponenty, výroba a uskladnenie produktov (3 kroky)"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm_sam
msgid "Pick components, manufacture, then store products (3 steps)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_mto_pull_id
msgid "Picking Before Manufacturing MTO Rule"
msgstr "MTO pravidlo vyzdvihnutie pred výrobou"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_type_id
msgid "Picking Before Manufacturing Operation Type"
msgstr "Typ operácie - vyzdvihnutie pred výrobou"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_route_id
msgid "Picking Before Manufacturing Route"
msgstr "Trasa - vyzdvihnutie pred výrobou"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking_type
msgid "Picking Type"
msgstr "Typ výberu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_ids
msgid "Picking associated to this manufacturing order"
msgstr "Vychystávanie súvisiace s touto výrobnou objednávkou"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_loc_id
msgid "Picking before Manufacturing Location"
msgstr "Lokácia vychystania pred výrobou"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Pieces"
msgstr "Kusy"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Plan"
msgstr "Plán"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Plan Orders"
msgstr "Plánovať objednávky"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_plan_with_components_availability
msgid "Plan based on Components Availability"
msgstr "Plánovať podľa dostupnosti komponentov"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Plan manufacturing or purchase orders based on forecasts"
msgstr "Plánovať výrobné alebo nákupné objednávky na základe prognózy"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planned"
msgstr "Plánované"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Planned at the same time as other workorder(s) at %s"
msgstr "Plánované na ten istý čas ako iné pracovné objednávky v čase %s"

#. module: mrp
#: model:ir.ui.menu,name:mrp.mrp_planning_menu_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Planning"
msgstr "Plánovanie"

#. module: mrp
#: model:product.template,name:mrp.product_product_plastic_laminate_product_template
msgid "Plastic Laminate"
msgstr "Umelohmotný laminát"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_move.py:0
msgid "Please enter a positive quantity."
msgstr "Zadajte, prosím, kladné množstvo."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Please set the first Serial Number or a default sequence"
msgstr "Nastavte prvé sériové číslo alebo predvolenú postupnosť"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid "Please specify the first serial number you would like to use."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid "Please specify the serial number you would like to use."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Please unblock the work center to start the work order."
msgstr "Odblokujte pracovisko, aby ste mohli spustiť pracovnú objednávku."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Please unblock the work center to validate the work order"
msgstr "Odblokujte pracovisko, aby ste mohli potvrdiť pracovnú objednávku"

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_ply_product_template
msgid "Ply Layer"
msgstr "Vrstva Layer"

#. module: mrp
#: model:product.template,name:mrp.product_product_ply_veneer_product_template
msgid "Ply Veneer"
msgstr "Vrstva Veneer"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__json_popover
msgid "Popover Data JSON"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__possible_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__possible_bom_product_template_attribute_value_ids
msgid "Possible Product Template Attribute Value"
msgstr "Možná hodnota atribútu šablóny produktu"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Post-Production"
msgstr "Post produkcia"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pre-Production"
msgstr "Pred produkcia"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Prepare MO"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Print"
msgstr "Tlač"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
msgid "Print All Variants"
msgstr "Tlačiť všetky varianty"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_print_labels
msgid "Print Labels"
msgstr "Tlačiť štítky"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.xml:0
msgid "Print Work Order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Print labels as:"
msgstr "Tlačiť štítky ako:"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Print when \"Create new Lot/SN\""
msgstr "Tlačiť ak \"Vytvoriť novú šaržu/SČ\""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Print when done"
msgstr "Tlačiť pri dokončení"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__priority
msgid "Priority"
msgstr "Priority"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason5
msgid "Process Defect"
msgstr "Chyba procesu"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Process manufacturing orders from the barcode application"
msgstr "Spracovanie výrobných objednávok z aplikácie čiarových kódov"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Process operations at specific work centers"
msgstr "Spracovávať operácie na konkrétnych pracoviskách"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__produce_line_ids
msgid "Processed Disassembly Lines"
msgstr "Spracované riadky rozmontovania"

#. module: mrp
#: model:ir.model,name:mrp.model_procurement_group
#: model:ir.model.fields,field_description:mrp.field_mrp_production__procurement_group_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Procurement Group"
msgstr "Skupina obstarávania"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_procurement_compute_mrp
msgid "Procurement: run scheduler"
msgstr "Obstarávanie: spusť plánovač"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Produce"
msgstr "Vyprodukovať"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Produce All"
msgstr "Vyrobiť všetko"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_batch_produce
msgid "Produce a batch of production order"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_byproducts
msgid "Produce residual products"
msgstr "Vyrobiť vedľajšie produkty"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Produce residual products (A + B -> C + D)"
msgstr "Vyrobiť vedľajšie produkty (A + B -> C + D)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "Produced"
msgstr "Vyrobené"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__operation_id
msgid "Produced in Operation"
msgstr "Vyrobené na operácii"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model,name:mrp.model_product_template
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Product"
msgstr "Produkt"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Product Attachments"
msgstr "Prílohy produktu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__capacity_ids
msgid "Product Capacities"
msgstr "Kapacity produktu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__product_catalog_product_is_in_bom
msgid "Product Catalog Product Is In Bom"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__product_catalog_product_is_in_mo
msgid "Product Catalog Product Is In Mo"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Product Cost"
msgstr "Náklady produktu"

#. module: mrp
#: model:ir.model,name:mrp.model_product_document
msgid "Product Document"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_virtual_available
msgid "Product Forecasted Quantity"
msgstr "Prognózované množstvo produktu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__mrp_product_label_to_print
msgid "Product Label to Print"
msgstr "Štítok produktu k tlači"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Product Labels"
msgstr "Štítky produktu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_plm
msgid "Product Lifecycle Management (PLM)"
msgstr "Správa životného cyklu produktu (PLM)"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Pohyby produktu (riadok skladového pohybu)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_qty_available
msgid "Product On Hand Quantity"
msgstr "Množstvo produktu skladom"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Product Quantity"
msgstr "Množstvo produktu"

#. module: mrp
#: model:ir.model,name:mrp.model_product_replenish
msgid "Product Replenish"
msgstr "Doplnenie produktu"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__product_template
msgid "Product Template"
msgstr "Šablóna produktu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_uom_id
msgid "Product Unit of Measure"
msgstr "Merná jednotka produktu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__product_uom_id
msgid "Product UoM"
msgstr "MJ produktu"

#. module: mrp
#: model:ir.model,name:mrp.model_product_product
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_id
msgid "Product Variant"
msgstr "Varianta produktu"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_product_variant_action
#: model:ir.ui.menu,name:mrp.product_variant_mrp
msgid "Product Variants"
msgstr "Varianty produktu"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_capacity_unique_product
msgid "Product capacity should be unique for each workcenter."
msgstr "Kapacita produktu by mala byť pre každé pracovisko jedinečná."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Product to build..."
msgstr "Produkt na zostavenie..."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__production_id
#: model:ir.model.fields,field_description:mrp.field_picking_label_type__production_ids
#: model:ir.model.fields,field_description:mrp.field_stock_picking__production_ids
msgid "Production"
msgstr "Výroba"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_capacity
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_capacity
msgid "Production Capacity"
msgstr "Kapacita výroby"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_date
msgid "Production Date"
msgstr "Dátum výroby"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Production Information"
msgstr "Informácie pre výrobu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_location_id
msgid "Production Location"
msgstr "Lokácia produktu"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_production_order
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__production_id
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Production Order"
msgstr "Výrobná objednávka"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__raw_material_production_id
msgid "Production Order for components"
msgstr "Výrobná objednávka pro komponenty"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__production_id
msgid "Production Order for finished products"
msgstr "Výrobná objednávka pro hotové produkty"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_state
msgid "Production State"
msgstr "Stav výroby"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Production Workcenter"
msgstr "Výrobné pracovisko"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
msgid "Production of Draft MO"
msgstr "Výroba z navrhovanej výrobnej objednávky"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Production started late"
msgstr "Výroba začala neskoro"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__production_ids
msgid "Productions To Split"
msgstr "Výroba k rozdeleniu"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__productive
msgid "Productive"
msgstr "Produktívne"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__productive_time
msgid "Productive Time"
msgstr "Produktívny čas"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__productive_time
msgid "Productive hours over the last month"
msgstr "Produktívne hodiny za minulý mesiac"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Productivity"
msgstr "Produktivita"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_blocked
msgid "Productivity Losses"
msgstr "Straty produktivity"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_template_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom
#: model:ir.ui.menu,name:mrp.menu_mrp_product_form
msgid "Products"
msgstr "Produkty"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__progress
msgid "Progress Done (%)"
msgstr "Dosiahnutý pokrok (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__propagate_cancel
msgid "Propagate cancel and split"
msgstr "Propagovať zrušenie a rozdelenie"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__quality
msgid "Quality"
msgstr "Kvalita"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Quality Losses"
msgstr "Straty kvality"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "Pracovný hárok kvality"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quant_ids
msgid "Quant"
msgstr "Množstvo"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_produced
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quantity
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Quantity"
msgstr "Množstvo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_produced
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view_mo_form
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Quantity Produced"
msgstr "Vyrobené množstvo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_producing
msgid "Quantity Producing"
msgstr "Vyrábané množstvo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Quantity Remaining"
msgstr "Zvyšné množstvo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_remaining
msgid "Quantity To Be Produced"
msgstr "Množstvo, ktoré sa má vyrábať"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__should_consume_qty
msgid "Quantity To Consume"
msgstr "Množstvo k spotrebe"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__quantity
msgid "Quantity To Produce"
msgstr "Množstvo k výrobe"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_capacity
#: model:ir.model.fields,help:mrp.field_mrp_production_split__production_capacity
msgid "Quantity that can be produced with the current stock of components"
msgstr "Množstvo, ktoré možno vyrobiť s aktuálnymi zásobami komponentov"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_quant
msgid "Quants"
msgstr "Kvanty"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "RAM"
msgstr "RAM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__rating_ids
msgid "Ratings"
msgstr "Hodnotenia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_raw_ids
msgid "Raw Moves"
msgstr "Pohyby"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__assigned
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__ready
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Ready"
msgstr "Pripravené"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Ready to Produce"
msgstr "Pripravené k výrobe"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Real Cost"
msgstr "Skutočné náklady"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Real Costs"
msgstr "Skutočné náklady"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__duration
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration
msgid "Real Duration"
msgstr "Skutočné trvanie"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Receipt"
msgstr "Potvrdenka"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Receipts"
msgstr "Príjemky"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Reception time estimation."
msgstr "Odhad času príjmu."

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason4
msgid "Reduced Speed"
msgstr "Znížená rýchlosť"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason6
msgid "Reduced Yield"
msgstr "Znížená výťažnosť"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__code
#: model:ir.model.fields,field_description:mrp.field_mrp_production__name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__name
msgid "Reference"
msgstr "Referencia"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_name_uniq
msgid "Reference must be unique per Company!"
msgstr "Referencia musí byť jedinečná pre spoločnosť!"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__origin
msgid ""
"Reference of the document that generated this production order request."
msgstr ""
"Odkaz na dokument, ktorý vytvoril túto požiadavku výrobnej objednávky."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Reference:"
msgstr "Referencia:"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_remaining_days_unformatted_field.js:0
msgid "Remaining Days"
msgstr "Zostávajúce dni"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_workorder_popover.xml:0
msgid "Replan"
msgstr "Preplánovať"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.xml:0
msgid "Replenish"
msgstr "Doplnenie"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Replenish on Order (MTO)"
msgstr "Doplnenie na zákazku (MTO)"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Replenishments"
msgstr "Doplnenie zásob"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_reporting
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Reporting"
msgstr "Prehľady"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Reserved"
msgstr "Rezervované"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_id
msgid "Resource"
msgstr "Prostriedok"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__user_id
msgid "Responsible"
msgstr "Zodpovedný"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_user_id
msgid "Responsible User"
msgstr "Zodpovedný užívateľ"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Resupply lead time."
msgstr "Priebežná doba doplnenia."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Route"
msgstr "Trasa"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Routing"
msgstr "Smerovanie"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__routing_line_ids
msgid "Routing Lines"
msgstr "Riadky smerovania"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
msgid "Routing Work Centers"
msgstr "Smerovanie pracovných centier"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Chyba doručenia SMS"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__date
msgid "Schedule Date"
msgstr "Naplánovať dátum"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Schedule manufacturing orders earlier to avoid delays"
msgstr "Naplánujte si výrobné objednávky skôr, aby ste predišli oneskoreniu"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Scheduled Date"
msgstr "Naplánovaný dátum"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Scheduled End"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"Scheduled before the previous work order, planned from %(start)s to %(end)s"
msgstr ""
"Naplánované pred predchádzajúcou pracovnou objednávkou, plánované od "
"%(start)s do %(end)s"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_workorder_popover.xml:0
msgid "Scheduling Information"
msgstr "Informácia plánovania"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_scrap
#: model:ir.model,name:mrp.model_stock_scrap
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_scrap
msgid "Scrap"
msgstr "Vyradené"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_count
msgid "Scrap Move"
msgstr "Vyraďovací pohyb"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Scrap Products"
msgstr "Zmetkové produkty"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_ids
msgid "Scraps"
msgstr "Vyradenia"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_screw_product_template
msgid "Screw"
msgstr "Screw"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Search"
msgstr "Vyhľadávanie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Search Bill Of Material"
msgstr "Vyhľadať kusovník"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Search Production"
msgstr "Vyhľadať výrobu"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Search Work Orders"
msgstr "Vyhľadať pracovné objednávky"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Search for mrp workcenter"
msgstr "Vyhľadať mrp pracovisko"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Security Lead Time"
msgstr "Bezpečnostný čas"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,help:mrp.field_res_config_settings__manufacturing_lead
msgid "Security days for each manufacturing operation."
msgstr "Bezpoečnostné dni pre každú výrobnú operáciu."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
msgid "Select Operations to Copy"
msgstr "Vyberte operácie na kopírovanie"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Selection not supported."
msgstr "Selection not supported."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__sequence
msgid "Sequence"
msgstr "Postupnosť"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Set Quantities & Validate"
msgstr "Nastaviť množstvá a potvrdiť"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Set Quantity"
msgstr "Nastaviť množstvo"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__manual
msgid "Set duration manually"
msgstr "Nastaviť trvanie ručne"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Set the order that work orders should be processed in. Activate the feature "
"within each BoM's Miscellaneous tab"
msgstr ""
"Nastavenie poradia, v akom sa majú pracovné objednávky spracovávať. Funkciu "
"aktivujte v rámci každého kusovníka na karte Rôzne"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_configuration
#: model:ir.ui.menu,name:mrp.menu_mrp_config
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Settings"
msgstr "Nastavenia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_start
msgid "Setup Time"
msgstr "Čas nastavenia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__time_start
msgid "Setup Time (minutes)"
msgstr "Čas nastavenia (minúty)"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason2
msgid "Setup and Adjustments"
msgstr "Nastavenie a prispôsobenie"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_allocation
msgid "Show Allocation"
msgstr "Zobraziť pridelenie"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__show_bom
msgid "Show BoM column"
msgstr "Zobraziť stĺpec kusovníka"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_replenish__show_bom
#: model:ir.model.fields,field_description:mrp.field_stock_replenish_mixin__show_bom
msgid "Show Bom"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_final_lots
msgid "Show Final Lots"
msgstr "Zobraziť konečné šarže"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lock
msgid "Show Lock/unlock buttons"
msgstr "Zobraziť tlačidla Zamknúť/Odomknúť"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__show_json_popover
msgid "Show Popover?"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_produce
msgid "Show Produce"
msgstr "Zobraziť Vyrobiť"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_produce_all
msgid "Show Produce All"
msgstr "Zobraziť Vyrobiť všetko"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Show all records which has next action date is before today"
msgstr "Zobraz všetky záznamy, ktorých následná aktivita je pred dnešným dňom"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__show_backorder_lines
msgid "Show backorder lines"
msgstr "Zobraziť riadky doobjednávok"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__leave_id
msgid "Slot into workcenter calendar once planned"
msgstr "Zaradenie do kalendára pracoviska po naplánovaní"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_head_product_template
msgid "Solid wood is a durable natural material."
msgstr "Masívne drevo je trvanlivý prírodný materiál."

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_product_template
msgid "Solid wood table."
msgstr "Stôl z masívneho dreva."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid ""
"Some of your byproducts are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct byproducts."
msgstr ""
"Niektoré z vašich vedľajších produktov sú sledované, musíte zadať výrobnú "
"objednávku, aby ste získali správne vedľajšie produkty."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid ""
"Some of your components are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct components."
msgstr ""
"Niektoré z vašich komponentov sú sledované šaržou/SČ, musíte zadať výrobnú "
"objednávku, aby ste získali správne komponenty."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"Some work orders are already done, so you cannot unplan this manufacturing order.\n"
"\n"
"It’d be a shame to waste all that progress, right?"
msgstr ""
"Niektoré pracovné objednávky sú už hotové, takže túto výrobnú objednávku nemôžete odplánovať.\n"
"\n"
"Bola by škoda premárniť všetok ten pokrok, však?"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"Some work orders have already started, so you cannot unplan this manufacturing order.\n"
"\n"
"It’d be a shame to waste all that progress, right?"
msgstr ""
"Niektoré pracovné objednávky sa už začali, takže túto výrobnú objednávku nemôžete odplánovať.\n"
"\n"
"Bola by škoda premárniť všetok ten pokrok, však?"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__origin
msgid "Source"
msgstr "Zdroj"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_id
msgid "Source Location"
msgstr "Zdrojová lokácia"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Specific Capacities"
msgstr "Špecifické kapacity"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__capacity_ids
msgid ""
"Specific number of pieces that can be produced in parallel per product."
msgstr ""
"Konkrétny počet kusov, ktoré sa môžu vyrábať paralelne na jeden výrobok."

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_split
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
msgid "Split"
msgstr "Rozdeliť"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__counter
msgid "Split #"
msgstr "Rozdeliť mn."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_detailed_vals_ids
msgid "Split Details"
msgstr "Detail rozdelenia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__mrp_production_split_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Split Production"
msgstr "Rozdeliť výrobu"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split_line
msgid "Split Production Detail"
msgstr "Detail rozdelenia výroby"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_split_multi_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Split Productions"
msgstr "Rozdeliť výrobu"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_split
msgid "Split production"
msgstr "Rozdeliť výrobu"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_split_multi
msgid "Split productions"
msgstr "Rozdeliť výrobu"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_screw_product_template
msgid "Stainless steel screw"
msgstr "Skrutka z nehrdzavejúcej ocele"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_bolt_product_template
msgid "Stainless steel screw full (dia - 5mm, Length - 10mm)"
msgstr "Skrutka z nehrdzavejúcej ocele plná (priem. - 5 mm, dĺžka - 10 mm) "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Starred"
msgstr "Obľúbené"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_start_workorders
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_start
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Start"
msgstr "Štart"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_start
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Start Date"
msgstr "Dátum začiatku"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__state
msgid "State"
msgstr "Štát"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__state
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Status"
msgstr "Stav"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_state
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status založený na aktivitách\n"
"Zmeškané: dátum už vypršal\n"
"Dnes: dátum aktivity je dnes\n"
"Plán: budúce aktivity"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_type_id
msgid "Stock After Manufacturing Operation Type"
msgstr "Zásoby po typu operácie Výroba"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_rule_id
msgid "Stock After Manufacturing Rule"
msgstr "Zásoby po Výrobnom pravidle"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_availability
msgid "Stock Availability"
msgstr "Dostupnosť zásob"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move
msgid "Stock Move"
msgstr "Pohyb skladu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_dest_ids
msgid "Stock Movements of Produced Goods"
msgstr "Skladové pohyby vyrobeného tovaru"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_unbuild_moves
msgid "Stock Moves"
msgstr "Skladové pohyby"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "Prehľad príjmov na sklad"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Prehľad doplnení zásob"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_rule
msgid "Stock Rule"
msgstr "Skladové pravidlo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_loc_id
msgid "Stock after Manufacturing Location"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Report skladového pravidla"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Store Finished Product"
msgstr "Uskladnenie hotového výrobku"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_bom_id
msgid "Sub BoM"
msgstr "Podriadený kusovník"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_subcontracting
msgid "Subcontracting"
msgstr "Kooperácia"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_product_template
msgid "Table"
msgstr "Stôl"

#. module: mrp
#: model:product.template,name:mrp.product_product_table_kit_product_template
msgid "Table Kit"
msgstr "Zostava stolu"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_leg_product_template
msgid "Table Leg"
msgstr "Noha stolu"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_head_product_template
msgid "Table Top"
msgstr "Vrch stola"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_table_kit_product_template
msgid "Table kit"
msgstr "Zostava stola"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tag_ids
msgid "Tag"
msgstr "Tag"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__name
msgid "Tag Name"
msgstr "Názov tagu"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr ""
"Technické pole, ktoré sa používa na rozhodnutie, či sa má zobraziť tlačidlo "
"„Pridelenie“."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__has_routing_lines
msgid "Technical field for workcenter views"
msgstr "Technické pole pro zobrazenia pracoviska"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_produce_all
msgid "Technical field to check if produce all button can be shown"
msgstr ""
"Technické pole na kontrolu, či je možné zobraziť tlačidlo Vyrobiť všetko"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_produce
msgid "Technical field to check if produce button can be shown"
msgstr "Technické pole na kontrolu, či je možné zobraziť tlačidlo Výroby"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reserve_visible
msgid "Technical field to check when we can reserve quantities"
msgstr "Technické pole na kontrolu, kedy môžeme rezervovať množstvo"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr "Technické pole na kontrolu, kedy môžeme zrušiť rezerváciu"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__text
msgid "Text"
msgstr "Text"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__is_outdated_bom
msgid "The BoM has been updated since creation of the MO"
msgstr "Kusovník bol aktualizovaný od vytvorenia výrobnej objednávky"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The Product Unit of Measure you chose has a different category than in the "
"product form."
msgstr ""
"Merná jednotka produku korú ste vybrali má inú kategíu ako v produktovom "
"formulári."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "The Workorder (%s) cannot be started twice!"
msgstr "Pracovnú objednávku (%s) nie je možné začať dvakrát!"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The attribute value %(attribute)s set on product %(product)s does not match "
"the BoM product %(bom_product)s."
msgstr ""
"Hodnota atribútu %(attribute)s nastavená na produkte %(product)s nezodpovedá"
" kusovníku produktu %(bom_product)s."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "The capacity must be strictly positive."
msgstr "Kapacita musí byť len pozitívna."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "The component %s should not be the same as the product to produce."
msgstr "Komponent %s by nemal byť rovnaký ako výrobok, ktorý sa má vyrobiť."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The current configuration is incorrect because it would create a cycle "
"between these products: %s."
msgstr ""
"Súčasná konfigurácia je nesprávna, pretože by vytvorila zacyklenie medzi "
"týmito produktmi: %s."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__day_2
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "The day after tomorrow"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_orderpoint.py:0
msgid "The following replenishment order has been generated"
msgstr "Bola vygenerovaná táto objednávka na doplnenie"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_produced
msgid "The number of products already handled by this work order"
msgstr ""
"Počet výrobkov, ktoré už boli spracované v rámci tejto pracovnej objednávky"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__operation_id
msgid ""
"The operation where the components are consumed, or the finished products "
"created."
msgstr ""
"Operácia, pri ktorej sa spotrebúvajú komponenty alebo vytvárajú hotové "
"výrobky."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__cost_share
msgid ""
"The percentage of the final production cost for this by-product line "
"(divided between the quantity produced).The total of all by-products' cost "
"share must be less than or equal to 100."
msgstr ""
"Percentuálny podiel konečných výrobných nákladov na tento vedľajší produkt "
"(vydelený vyrobeným množstvom).Celkový podiel nákladov všetkých vedľajších "
"produktov musí byť menší alebo rovný 100."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__cost_share
msgid ""
"The percentage of the final production cost for this by-product. The total "
"of all by-products' cost share must be smaller or equal to 100."
msgstr ""
"Percento konečných výrobných nákladov na tento vedľajší produkt. Celkový "
"podiel nákladov všetkých vedľajších produktov musí byť menší alebo rovný "
"100."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"The planned end date of the work order cannot be prior to the planned start "
"date, please correct this to save the work order."
msgstr ""
"Plánovaný dátum ukončenia pracovnej objednávky nemôže byť skorší ako "
"plánovaný dátum začatia, opravte ho a uložte pracovnú objednávku."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The product has already been used at least once, editing its structure may "
"lead to undesirable behaviours. You should rather archive the product and "
"create a new one with a new bill of materials."
msgstr ""
"Výrobok už bol minimálne raz použitý, úprava jeho štruktúry môže viesť k "
"nežiaducemu správaniu. Výrobok by ste mali radšej archivovať a vytvoriť nový"
" s novým kusovníkom."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_reported_from_previous_wo
msgid ""
"The quantity already produced awaiting allocation in the backorders chain."
msgstr ""
"Množstvo, ktoré už bolo vyrobené a čaká na pridelenie v reťazci "
"doobjednávok."

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_qty_positive
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_qty_positive
msgid "The quantity to produce must be positive!"
msgstr "Množstvo na výrobu musí byť kladné!"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_unbuild_qty_positive
msgid "The quantity to unbuild must be positive!"
msgstr "Množstvo k rozobratí musí byť kladné!"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"The serial number %(number)s used for byproduct %(product_name)s has already"
" been produced"
msgstr ""
"Sériové číslo %(number)s použité pre vedľajší produkt %(product_name)s už "
"bolo vyrobené"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"The serial number %(number)s used for component %(component)s has already "
"been consumed"
msgstr ""
"Sériové číslo %(number)s použité pre komponent %(component)s už bolo "
"spotrebované"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_tag_tag_name_unique
msgid "The tag name must be unique."
msgstr "Názov značky musí byť jedinečný."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "The total cost share for a BoM's by-products cannot exceed 100."
msgstr ""
"Celkový podiel nákladov na vedľajšie produkty kusovníka nesmie presiahnuť "
"100."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"The total cost share for a manufacturing order's by-products cannot exceed "
"100."
msgstr ""
"Celkový podiel nákladov na vedľajšie produkty výrobnej objednávky nesmie "
"presiahnuť 100."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
msgid "The total should be equal to the quantity to produce."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "The work order should have already been processed."
msgstr "Pracovný príkaz by už mal byť spracovaný."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"There are no components to consume. Are you still sure you want to continue?"
msgstr ""
"Neexistujú žiadne komponenty na spotrebu. Ste si stále istí, že chcete "
"pokračovať?"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
msgid "There is no defined calendar on workcenter %s."
msgstr "Pracovisko %s nemá definovaný kalendár."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid "There's no product move yet"
msgstr "Zatiaľ neexistuje pohyb produktu"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "This Week"
msgstr "Tento týždeň"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Toto pole sa používa na definovanie časového pásma, v ktorom budú zdroje "
"pracovať."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""
"Toto pole sa používa na výpočet očakávaného trvania pracovnej objednávky na "
"tomto pracovisku. Ak napríklad pracovná objednávka trvá jednu hodinu a "
"faktor účinnosti je 100 %, potom bude očakávané trvanie jedna hodina. Ak je "
"však faktor účinnosti 200 %, očakávané trvanie bude 30 minút."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
msgid "This is a BoM of type Kit!"
msgstr "Tento kusovník je typu Súprava!"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid ""
"This is the cost based on the BoM of the product. It is computed by summing "
"the costs of the components and operations needed to build the product."
msgstr ""
"Ide o náklad založený na kusovníku výrobku. Vypočíta sa súčtom nákladov na "
"komponenty a operácie potrebné na vytvorenie výrobku."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "This is the cost defined on the product."
msgstr "Toto je náklad definovaný na produkte."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                You can filter on the product to see all the past movements for the product."
msgstr ""
"Táto ponuka poskytuje úplnú sledovateľnosť skladových operácií konkrétného výrobku.\n"
"             Môžete si vyfiltrovať výrobok a zobraziť všetky jeho minulé pohyby."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This production has been merge in %s"
msgstr "Táto produkcia bola zlúčená do %s"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This production order has been created from Replenishment Report."
msgstr ""
"Tento výrobná objednávka výroby bola vytvorená z prehľadu doplnení zásob."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This serial number for product %s has already been produced"
msgstr "Toto sériové číslo výrobku %s už bolo vyrobené"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_qty
msgid ""
"This should be the smallest quantity that this product can be produced in. "
"If the BOM contains operations, make sure the work center capacity is "
"accurate."
msgstr ""
"Najmenšie množstvo, v akom sa tento výrobok môže vyrábať. Ak kusovník "
"obsahuje operácie, uistite sa, že kapacita pracoviska je primeraná."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__time_ids
msgid "Time"
msgstr "Čas"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_efficiency
msgid "Time Efficiency"
msgstr "Časová efektivita"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_ids
msgid "Time Logs"
msgstr "Časové záznamy"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Time Tracking"
msgstr "Sledovanie času"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Time Tracking: %(user)s"
msgstr "Sledovanie času: %(user)s"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__time_stop
msgid "Time in minutes for the cleaning."
msgstr "Čas čistenia v minútach."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__time_start
msgid "Time in minutes for the setup."
msgstr "Čas nastavenia v minútach."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid ""
"Time in minutes:- In manual mode, time used- In automatic mode, supposed "
"first time when there aren't any work orders yet"
msgstr ""
"Čas v minútach:- v manuálnom režime, použitý čas- v automatickom režime, "
"prvý predpoklad, keď ešte nie sú žiadne pracovné objednávky"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tz
msgid "Timezone"
msgstr "Časová zóna"

#. module: mrp
#: model:digest.tip,name:mrp.digest_tip_mrp_0
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid "Tip: Use tablets in the shop to control manufacturing"
msgstr "Tip: Pre riadenie výroby na dielni, požívajte tablety"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "To"
msgstr "Do"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__to_backorder
msgid "To Backorder"
msgstr "Na doobjednanie"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__to_close
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Close"
msgstr "Na zavretie"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_expected_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "To Consume"
msgstr "Na spotrebovanie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Do"
msgstr "Úlohy"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "To Launch"
msgstr "Na spustenie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "To Manufacture"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "To Order"
msgstr "Na objednanie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "To Produce"
msgstr "Na vyrobenie"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__today
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today"
msgstr "Dnes"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today Activities"
msgstr "Dnešné aktivity"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__day_1
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Tomorrow"
msgstr "Zajtra"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_wood_wear_product_template
msgid "Top layer of a wood panel."
msgstr "Vrchná vrstva dreveného panelu."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Total Cost of Components"
msgstr "Celkové náklady na komponenty"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Total Cost of Operations"
msgstr "Celkové náklady na operácie"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Total Cost of Production"
msgstr "Celkové náklady výroby"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Total Duration"
msgstr "Celkové trvanie"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_late_count
msgid "Total Late Orders"
msgstr "Počet oneskorených objednávok"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_pending_count
msgid "Total Pending Orders"
msgstr "Celkový počet čakajúcich objednávok"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total Qty"
msgstr "Množ. celkom"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_qty
msgid "Total Quantity"
msgstr "Celkové množstvo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_progress_count
msgid "Total Running Orders"
msgstr "Celkový počet spustených príkazov"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Total To Consume"
msgstr "Celkové množstvo na spotrebu"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total expected duration"
msgstr "Celkové očakávané trvanie"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__duration_expected
msgid "Total expected duration (in minutes)"
msgstr "Celkové očakávané trvanie ( v minútach)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total real duration"
msgstr "Celkové skutočné trvanie"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__duration
msgid "Total real duration (in minutes)"
msgstr "Celkové skutočné trvanie (v minútach)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Traceability"
msgstr "Sledovateľnosť"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_traceability_report
msgid "Traceability Report"
msgstr "Report sledovateľnosti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_tracking
msgid "Tracking"
msgstr "Sledovanie"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking
msgid "Transfer"
msgstr "Prevod"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Transfers"
msgstr "Prevody"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "Typ operácie"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_exception_decoration
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ výnimočnej aktivity v zázname."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Unable to split with more than the quantity to produce."
msgstr "Nie je možné rozdeliť viac ako je množstvo na výrobu."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.js:0
msgid "Unblock"
msgstr "Odblokovať"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__unbuild_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild"
msgstr "Rozobrať"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild Order"
msgstr "Objednávka rozobratia"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_unbuild
#: model:ir.ui.menu,name:mrp.menu_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Unbuild Orders"
msgstr "Objednávky rozobratí"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Unbuild: %s"
msgstr "Rozobrať: %s"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unbuild_ids
msgid "Unbuilds"
msgstr "Rozobratia"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Unbuilt"
msgstr "Rozobrané"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.js:0
msgid "Unfold"
msgstr "Rozvinúť"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Unit"
msgstr "Jednotka"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Unit Cost"
msgstr "Jednotkové náklady"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Unit Costs"
msgstr "Jednotkové náklady"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unit_factor
msgid "Unit Factor"
msgstr "Faktor jednotky"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_uom_name
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Unit of Measure"
msgstr "Merná jednotka"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_id
msgid ""
"Unit of Measure (Unit of Measure) is the unit of measurement for the "
"inventory control"
msgstr ""
"Merná jednotka (Merná jednotka) je jednotka merania pre inventarizáciu"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Units"
msgstr "Jednotky"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_unlocked_by_default
msgid "Unlock Manufacturing Orders"
msgstr "Odomknúť výrobné objednávky"

#. module: mrp
#: model:res.groups,name:mrp.group_unlocked_by_default
msgid "Unlocked by default"
msgstr "Odomknuté v predvolenom nastavení"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unplan"
msgstr "Odplánovať"

#. module: mrp
#: model:ir.actions.server,name:mrp.mrp_production_action_unreserve_tree
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unreserve"
msgstr "Odrezervovať"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "UoM"
msgstr "Merná jednotka"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Update BoM"
msgstr "Aktualizovať kusovník"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Upload your PDF file."
msgstr "Nahrajte svoj súbor PDF."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__1
msgid "Urgent"
msgstr "Súrne"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid ""
"Use Manufacturing Orders (MO) to build finished products while consuming "
"components: i.e. 1 Table = 4 Table Legs + 1 Table Top"
msgstr ""
"Používajte výrobné príkazy (VO) na výrobu hotových výrobkov pri súčasnom "
"spotrebovaní komponentov: t. j. 1 stôl = 4 nohy stola + 1 doska stola."

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_workorder_dependencies
msgid "Use Operation Dependencies"
msgstr "Použite závislosť operácií"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_reception_report
msgid "Use Reception Report with Manufacturing Orders"
msgstr "Používajte prehľad príjmov z výrobných objednávok"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Used In"
msgstr "Použité v"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__user_id
#: model:res.groups,name:mrp.group_mrp_user
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "User"
msgstr "Užívateľ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Using a MPS report to schedule your reordering and manufacturing operations "
"is useful if you have long lead time and if you produce based on sales "
"forecasts."
msgstr ""
"Použitie prehľadu MPS na naplánovanie vašich objednávacích a výrobných "
"operácií je užitočné, ak máte dlhý čas na dodanie a ak vyrábate na základe "
"prognóz predaja."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__valid_details
msgid "Valid"
msgstr "Platný"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr "Platné riadky atribútu produktu"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Validate"
msgstr "Potvrdiť"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_consumption_warning.py:0
msgid ""
"Values cannot be set and validated because a Lot/Serial Number needs to be "
"specified for a tracked product that is having its consumed amount "
"increased:%(products)s"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
msgid "Variant"
msgstr "Variant"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Vendor ABC"
msgstr "Dodávateľ ABC"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "View WorkOrder"
msgstr "Zobraziť pracovnú objednávku"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"View and allocate production quantities to customer orders or other "
"manufacturing orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse_orderpoint__manufacturing_visibility_days
msgid "Visibility Days applied on the manufacturing routes."
msgstr "Dni viditeľnosti použité pre výrobnu trasu."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Waiting"
msgstr "Čaká"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__waiting
msgid "Waiting Another Operation"
msgstr "Čaká na ďalšiu operáciu"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Waiting Availability"
msgstr "Čaká na dostupnosť"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__pending
msgid "Waiting for another WO"
msgstr "Čaká na ďalšiu pracovnú objednávku"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__waiting
msgid "Waiting for components"
msgstr "Čaká na komponenty"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Waiting the previous work order, planned from %(start)s to %(end)s"
msgstr ""
"Čaká na predchádzajúcu pracovnú objednávku, plánovanú od %(start)s do "
"%(end)s"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: model:ir.model,name:mrp.model_stock_warehouse
#: model:ir.model.fields,field_description:mrp.field_mrp_production__warehouse_id
msgid "Warehouse"
msgstr "Sklad"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warn_insufficient_qty_unbuild
msgid "Warn Insufficient Unbuild Quantity"
msgstr "Varovanie nedostatočného množstva na rozobratie"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/stock_scrap.py:0
msgid "Warning"
msgstr "Varovanie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Warnings"
msgstr "Varovania"

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_wear_product_template
msgid "Wear Layer"
msgstr "Nosná vrstva"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__website_message_ids
msgid "Website Messages"
msgstr "Správy webstránok"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__website_message_ids
msgid "Website communication history"
msgstr "História komunikácie webstránok"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__picking_type_id
msgid ""
"When a procurement has a ‘produce’ route with a operation type set, it will "
"try to create a Manufacturing Order for that product using a BoM of the same"
" operation type. That allows to define stock rules which trigger different "
"manufacturing orders with different BoMs."
msgstr ""
"Ak má obstarávanie trasu „vyrobiť“ s nastaveným typom operácie, pokúsi sa "
"pre tento výrobok vytvoriť výrobnú objednávku pomocou kusovníku s rovnakým "
"typom operácie. To umožňuje definovať skladové pravidlá, ktoré spúšťajú "
"rôzne výrobné objednávky s rôznymi kusovníkmi."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__manual_consumption
#: model:ir.model.fields,help:mrp.field_stock_move__manual_consumption
msgid ""
"When activated, then the registration of consumption for that component is recorded manually exclusively.\n"
"If not activated, and any of the components consumption is edited manually on the manufacturing order, Odoo assumes manual consumption also."
msgstr ""
"Ak je aktivované, potom sa evidencia spotreby pre tento komponent zaznamenáva výlučne manuálne.\n"
"Ak nie je aktivované a ak je niektorá spotreba komponentov editovaná na výrobnej objednávke ručne, potom Odoo predpokladá tiež manuálnu spotrebu."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__asap
msgid "When components for 1st operation are available"
msgstr "Ak sú k dispozícii komponenty pre 1. operáciu"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_to_resupply
msgid ""
"When products are manufactured, they can be manufactured in this warehouse."
msgstr "Ak sú výrobky vyrobené,  môžu byť vyrábané v tomto skladisku."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid ""
"When products are needed in <b>%s</b>, <br/> a manufacturing order is "
"created to fulfill the need."
msgstr ""
"Keď sú výrobky potrebné v <b>%s</b>, <br/> vytvorí sa výrobná objednávka na "
"splnenie potreby."

#. module: mrp
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid ""
"With the Odoo work center control panel, your worker can start work orders "
"in the shop and follow instructions of the worksheet. Quality tests are "
"perfectly integrated into the process. Workers can trigger feedback loops, "
"maintenance alerts, scrap products, etc."
msgstr ""
"Pomocou ovládacieho panela pracoviska Odoo, môže váš pracovník spúšťať "
"pracovné príkazy a postupovať podľa pokynov pracovného listu. Do tohto "
"procesu sú tiež integrované testy kvality. Pracovníci môžu priamo poskytnúť "
"spätnú väzbu, spúšťať upozornenia pre údržbu, nahlásiť nepodarené výrobky "
"atď."

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr ""
"Sprievodca v prípade spotreby vo výstražnom/prísnom režime, keď bolo vo "
"výrobnej objednávke použitých viac komponentov (ako je v použitom kusovníku)"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split_multi
msgid "Wizard to Split Multiple Productions"
msgstr "Sprievodca na rozdelenie viacerej výroby"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split
msgid "Wizard to Split a Production"
msgstr "Sprievodca na rozdelenie výrobnej objednávky"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder
msgid "Wizard to mark as done or create back order"
msgstr "Sprievodca na dokončenie ale vytvorenie doobjednávok"

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_panel_product_template
msgid "Wood Panel"
msgstr "Drevený panel"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Work Center"
msgstr "Pracovisko"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_capacity
msgid "Work Center Capacity"
msgstr "Kapacia pracoviska"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workcenter_load
msgid "Work Center Load"
msgstr "Záťaž pracoviska"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Work Center Loads"
msgstr "Záťaže pracoviska"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Work Center Name"
msgstr "Názov pracoviska"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Užívanie pracoviska"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
msgid "Work Center load"
msgstr "Záťaž pracoviska"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_action
#: model:ir.ui.menu,name:mrp.menu_view_resource_search_mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Centers"
msgstr "Pracoviská"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_kanban_action
msgid "Work Centers Overview"
msgstr "Prehľad pracovísk"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Work Instruction"
msgstr "Výrobné inštukcie"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_workorder
#: model:ir.model,name:mrp.model_mrp_workorder
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workorder_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__name
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__workorder_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__workorder_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Work Order"
msgstr "Pracovná objednávka"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_workorder_dependencies
msgid "Work Order Dependencies"
msgstr "Závislosti pracovných objednávok"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Work Order Operations allow you to create and manage the manufacturing "
"operations that should be followed within your work centers in order to "
"produce a product. They are attached to bills of materials that will define "
"the required components."
msgstr ""
"Operácie pracovných objednávok vám umožňujú vytvárať a spravovať výrobné "
"operácie, ktoré by sa mali vykonávať v rámci vašich pracovísk s cieľom "
"vyrobiť produkt. Sú spojené s kusovníkmi, ktoré definujú požadované "
"komponenty."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__workorder_id
msgid "Work Order To Consume"
msgstr "Spotrebovávaná pracovná objednávka"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_routing_time
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production_specific
#: model:ir.actions.act_window,name:mrp.action_work_orders
#: model:ir.actions.act_window,name:mrp.mrp_workorder_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_workorder_report
#: model:ir.actions.act_window,name:mrp.mrp_workorder_todo
#: model:ir.model.fields,field_description:mrp.field_mrp_production__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_work_order_report
#: model:ir.ui.menu,name:mrp.menu_mrp_workorder_todo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Orders"
msgstr "Pracovné objednávky"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workorder_workcenter_report
msgid "Work Orders Performance"
msgstr "Výkonnosť pracovnej objednávky"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_workcenter
msgid "Work Orders Planning"
msgstr "Plánovanie pracovných objednávok"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Work Sheet"
msgstr "Pracovný list"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Work center"
msgstr "Pracovisko"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"                    Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"Pracovné objednávky sú operácie, ktoré sa majú vykonať ako súčasť výrobnej objednávky.\n"
"                   Operácie sa definujú v kusovníku alebo sa pridávajú priamo do výrobného príkazu."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"Pracovné objednávky sú operácie, ktoré sa majú vykonať ako súčasť výrobnej objednávky.\n"
"              Operácie sa definujú v kusovníku alebo sa pridávajú priamo do výrobného príkazu."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Work orders in progress. Click to block work center."
msgstr "Prebiehajúce pracovné objednávky. Kliknutím zablokujete pracovisko."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Workcenter"
msgstr "Pracovisko"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "Workcenter %s cannot be an alternative of itself."
msgstr "Pracovisko %s nemôže byť samo pre seba alternatívnym."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Workcenter Productivity"
msgstr "Produktivita pracoviska"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "Záznamy produktivity pracoviska"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Workcenter Productivity Loss"
msgstr "Strata produktivity pracoviska"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss
msgid "Workcenter Productivity Losses"
msgstr "Straty produktivity pracoviska"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__working_state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_state
msgid "Workcenter Status"
msgstr "Stav pracoviska"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Workcenter blocked, click to unblock."
msgstr "Pracovisko je blokované, kliknite pre odblokovanie."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_calendar_id
msgid "Working Hours"
msgstr "Pracovný čas"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_user_ids
msgid "Working user on this work order."
msgstr "Používateľ pracujúci na tejto pracovnej objednávke."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_type
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet
msgid "Worksheet"
msgstr "Pracovný list"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_type
msgid "Worksheet Type"
msgstr "Typ pracovného listu"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_google_slide
msgid "Worksheet URL"
msgstr "URL pracovného listu"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid ""
"Write one line per finished product to produce, with serial numbers as "
"follows:\n"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__yesterday
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Yesterday"
msgstr "Včera"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_lot.py:0
msgid ""
"You are not allowed to create or edit a lot or serial number for the "
"components with the operation type \"Manufacturing\". To change this, go on "
"the operation type and tick the box \"Create New Lots/Serial Numbers for "
"Components\"."
msgstr ""
"Nie je povolené vytvárať ani upravovať šarže alebo sériové čísla pre "
"komponenty s typom operácie „Výroba“. Ak to chcete zmeniť, prejdite na typ "
"operácie a začiarknite políčko „Vytvoriť nové šarže/sériové čísla pre "
"komponenty“."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"You can not create a kit-type bill of materials for products that have at "
"least one reordering rule."
msgstr ""
"Pre výrobky, ktoré majú aspoň jedno pravidlo objednávania, nemôžete vytvoriť"
" kusovník typu súprava."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"You can not delete a Bill of Material with running manufacturing orders.\n"
"Please close or cancel it first."
msgstr ""
"Nemôžete zmazať kusovník s prebiehajúcimi výrobnými objednávkami.\n"
"Najskôr ju zavrite alebo zrušte."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"You can only merge manufacturing orders of identical products with same BoM."
msgstr ""
"Zlúčiť môžete len výrobné objednávky rovnakých produktov s rovnakým "
"kusovníkom."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"You can only merge manufacturing orders with no additional components or by-"
"products."
msgstr ""
"Zlúčiť môžete len výrobné objednávky bez ďalších komponentov alebo "
"vedľajších produktov."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You can only merge manufacturing with the same operation type"
msgstr ""
"Zlúčiť môžete len výrobné objednávky s rovnakým typom skladovej operácie"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You can only merge manufacturing with the same state."
msgstr "Zlúčiť môžete len výrobu s rovnakým stavom."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"You cannot change the workcenter of a work order that is in progress or "
"done."
msgstr ""
"Pracovisko rozpracovaného alebo dokončenej pracovnej objednávky nie je možné"
" zmeniť."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "You cannot create a new Bill of Material from here."
msgstr "Odtiaľto nie je možné vytvoriť nový kusovník."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot create cyclic dependency."
msgstr "Nemôžete vytvoriť cyklickú závislosť."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You cannot delete an unbuild order if the state is 'Done'."
msgstr "Dokončenú objednávku na rozobratie nie je možné zmazať."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You cannot have %s  as the finished product and in the Byproducts"
msgstr "Nemôžete mať %s ako hotový výrobok a rovnako vo vedľajších produktoch"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot link this work order to another manufacturing order."
msgstr ""
"Túto pracovnú objednávku nemôžete prepojiť s inou výrobnou objednávkou."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You cannot move a manufacturing order once it is cancelled or done."
msgstr "Výrobnú objednávku nemožno presunúť, keď je zrušená alebo hotová."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot produce the same serial number twice."
msgstr "To isté sériové číslo nemôžete vyrobiť dvakrát."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot start a work order that is already done or cancelled"
msgstr ""
"Nemôžete spustiť pracovnú objednávku, ktorá už bola dokončená alebo zrušená"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You cannot unbuild a undone manufacturing order."
msgstr "Nemôžete rozobrať nedokončenú výrobnú objednávku."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"You cannot use the 'Apply on Variant' functionality and simultaneously "
"create a BoM for a specific variant."
msgstr ""
"Nemôžete použiť funkciu „Použiť na variantu“ a zároveň vytvoriť kusovník pre"
" konkrétnu variantu."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid ""
"You consumed a different quantity than expected for the following products.\n"
"                        <b invisible=\"consumption == 'strict'\">\n"
"                            Please confirm it has been done on purpose.\n"
"                        </b>\n"
"                        <b invisible=\"consumption != 'strict'\">\n"
"                            Please review your component consumption or ask a manager to validate\n"
"                            <span invisible=\"mrp_production_count != 1\">this manufacturing order</span>\n"
"                            <span invisible=\"mrp_production_count == 1\">these manufacturing orders</span>.\n"
"                        </b>"
msgstr ""
"V prípade následujúcích produktov ste spotrebovali iné množstvo, ako bolo očakávané.\n"
"                 <b invisible=\"consumption == 'strict'\">\n"
"                   Potvrďte, prosím, že sa tak stalo zámerne.\n"
"                  </b>\n"
"                 <b invisible=\"consumption != 'strict'\">\n"
"                    Skontrolujte spotrebu komponentov alebo požiadajte manažéra o potvrdenie\n"
"                         <span invisible=\"mrp_production_count != 1\">tejto výrobnej objednávky </span> \n"
"                         <span invisible=\"mrp_production_count == 1\">týchto výrobných objednávok. </span>  \n"
"                    </b>"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You need at least two production orders to merge them."
msgstr "Na zlúčenie potrebujete aspoň dve výrobné objednávky."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"You need to define at least one productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""
"Musíte definovať aspoň jednu stratu produktivity v kategórii „Výkon“. "
"Vytvorte ju z aplikácie Výroba, ponuka: Konfigurácia / Straty produktivity."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"You need to define at least one productivity loss in the category "
"'Productivity'. Create one from the Manufacturing app, menu: Configuration /"
" Productivity Losses."
msgstr ""
"V kategórii „Produktivita“ musíte definovať aspoň jednu stratu produktivity."
" Vytvorte ju z aplikácie Výroba, ponuka: Konfigurácia / Straty produktivity."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid ""
"You need to define at least one unactive productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""
"V kategórii „Výkon“ musíte definovať aspoň jednu neaktívnu stratu "
"produktivity. Vytvorte ju z aplikácie Výroba, ponuka: Konfigurácia / Straty "
"produktivity."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You need to provide a lot for the finished product."
msgstr "Pre hotový výrobok musíte zapísať šaržu."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"You need to supply Lot/Serial Number for products and 'consume' them: "
"%(missing_products)s"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_backorder
msgid "You produced less than initial demand"
msgstr "Vyrobili ste menej, ako bol pôvodný dopyt"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You should provide a lot number for the final product."
msgstr "Mali by ste uviesť číslo šarže konečného výrobku."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_quant.py:0
msgid ""
"You should update the components quantity instead of directly updating the "
"quantity of the kit product."
msgstr ""
"Namiesto priamej aktualizácie množstva výrobku súpravy by ste mali "
"aktualizovať množstvo komponentov."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__done_mrp_lot_label_to_print__zpl
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__generated_mrp_lot_label_to_print__zpl
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__mrp_product_label_to_print__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "cancelled"
msgstr "zrušené"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "days"
msgstr "dni"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "days before"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "expected duration"
msgstr "očakávané trvanie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "from location"
msgstr "z pozície"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "it is added as a component in a manufacturing order"
msgstr "je pridaný ako komponent vo výrobnej objednávke."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"it is moved via a transfer, such as a receipt or a delivery order for "
"instance."
msgstr ""
"sa presúva prostredníctvom prevodu, ako je napríklad príjemka alebo príkaz "
"na dodanie."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "last"
msgstr "posledný"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "manufacturing order"
msgstr "výrobná objednávka"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "merged"
msgstr "zlúčené"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "minutes"
msgstr "minúty"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "of"
msgstr "z"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "ordered instead of"
msgstr "objednané namiesto"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "per workcenter"
msgstr "na pracovisko"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "quantity has been updated."
msgstr "množstvo bolo aktualizované."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "real duration"
msgstr "skutočné trvanie"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "split"
msgstr "rozdeliť"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "work orders"
msgstr "pracovné objednávky"
