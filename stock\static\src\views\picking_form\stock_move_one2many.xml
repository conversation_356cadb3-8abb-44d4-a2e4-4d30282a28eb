<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="stock.MovesListRenderer.RecordRow" t-inherit-mode="primary" t-inherit="web.ListRenderer.RecordRow">
        <xpath expr="//tr/t[1]" position="inside">
            <t t-if="column.type === 'opendetailsop'">
                <td class="o_data_cell cursor-pointer o_list_button">
                    <t t-if="record.resModel === 'stock.move' &amp;&amp; record.data?.show_details_visible">
                        <button name="Open Move" class="btn btn-link fa fa-list" t-on-click="() => props.openRecord(record)"/>
                    </t>
                </td>
            </t>
        </xpath>
    </t>

</templates>
