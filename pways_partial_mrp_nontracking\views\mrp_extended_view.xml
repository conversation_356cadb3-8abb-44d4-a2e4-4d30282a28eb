<odoo>
    <record id="mrp_production_form_view_inherited" model="ir.ui.view">
        <field name="name">mrp.production.inherited.form.view</field>
        <field name="model">mrp.production</field>
        <field name="inherit_id" ref="mrp.mrp_production_form_view"/>
        <field name="arch" type="xml">
            <button name='action_confirm' position="after">
                <button name="action_produced_continue" string="Partial Production" type="object" class="oe_highlight" invisible="(state in ('done', 'draft', 'cancel')) or (qty_produced == 'product_qty') or (product_tracking == 'serial')"/>
            </button>
            <label for="product_qty" position="before">
                <field name="qty_equal" invisible='1'/>
                <field name="qty_consuming" invisible="state in ('done', 'draft', 'cancel') or (product_tracking == 'serial')"/>
                <field name="over_prod_qty" invisible="(product_tracking == 'serial')"/>
            </label>
            <field name='qty_producing' position="replace">
                <field name="qty_producing" class="w-auto"  force_save="1" readonly="(state == 'cancel') and (state == 'done') and ('is_locked' == True)" invisible= " (state in ('draft', 'cancel','done')) or qty_equal"/>
            </field>
        </field>
    </record>
</odoo>