# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# שהאב <PERSON>ן <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# david <PERSON>, 2024
# <PERSON><PERSON><PERSON> <PERSON> <<EMAIL>>, 2024
# yael terner, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Ha <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# Lila<PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>aFarkash, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>adar, 2024
# Netta Waizer, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# Uri Segman, 2024
# <AUTHOR> <EMAIL>, 2025
# Yoram Lavi, 2025
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__state
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_state
msgid ""
" * Draft: The MO is not confirmed yet.\n"
" * Confirmed: The MO is confirmed, the stock rules and the reordering of the components are trigerred.\n"
" * In Progress: The production has started (on the MO or on the WO).\n"
" * To Close: The production is done, the MO has to be closed.\n"
" * Done: The MO is closed, the stock moves are posted. \n"
" * Cancelled: The MO has been cancelled, can't be confirmed anymore."
msgstr ""
"הפקודת הייצור (MO) עדיין לא אושרה.\n"
"\n"
"מאושרת: הפקודת ייצור אושרה, חוקי המלאי והזמנת הרכיבים מופעלים.\n"
"בתהליך: הייצור התחיל (על הפקודת ייצור או על פקודת העבודה - WO).\n"
"לסגירה: הייצור הושלם, הפקודת ייצור צריכה להיסגר.\n"
"בוצע: הפקודת ייצור נסגרה, תנועות המלאי נרשמו.\n"
"מבוטל: הפקודת ייצור בוטלה, לא ניתן לאשר אותה יותר."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid " <br/><br/> The components will be taken from <b>%s</b>."
msgstr " <br/><br/> הרכיבים יילקחו מ- <b>%s</b>."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid ""
" <br/><br/> The manufactured products will be moved towards "
"<b>%(destination)s</b>, <br/> as specified from <b>%(operation)s</b> "
"destination."
msgstr ""
"<br/><br/>המוצרים שיוצרו יועברו אל<b>%(destination)s</b> , <br/>כפי שצוין ב-"
" <b>%(operation)s</b>ההעברה."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__all_available
msgid " When all components are available"
msgstr "כאשר כל הרכיבים זמינים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_count
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_count
msgid "# Bill of Material"
msgstr "מס' עץ מוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__used_in_bom_count
msgid "# BoM Where Used"
msgstr "מס' עצי המוצר שהיו בשימוש"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_ready_count
msgid "# Ready Work Orders"
msgstr "מס׳ הוראות עבודה מוכנות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_count
msgid "# Work Orders"
msgstr "מס' הוראות עבודה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_template__used_in_bom_count
msgid "# of BoM Where is Used"
msgstr "מס' עצי המוצר שהיו בשימוש"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "%(name)s Sequence picking before manufacturing"
msgstr "סדר ליקוט לפני ייצור"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "%(name)s Sequence production"
msgstr "סדר ייצור"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "%(name)s Sequence stock after manufacturing"
msgstr "סדר מלאי לאחר ייצור"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "%(producible_qty)s Ready"
msgstr "מוכן"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "%(product)s: Insufficient Quantity To Unbuild"
msgstr "כמות בלתי מספקת לצורך ביצוע פירוק"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "%(product_name)s (new) %(number_of_boms)s"
msgstr "%(product_name)s (חדש) %(number_of_boms)s"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "%(qty)s %(measure)s unbuilt in %(order)s"
msgstr "%(qty)s %(measure)s פורק ב- %(order)s"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
msgid "%i work orders"
msgstr "%iהזמנות עבודה"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "%s Child MO's"
msgstr "%s הוראת ייצור בן"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "%s cannot be deleted. Try to cancel them before."
msgstr "%s לא ניתנת למחיקה. נסה לבטל אותה קודם."

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_bom_structure
msgid "'Bom Overview - %s' % object.display_name"
msgstr "'סקירה כללית - %s' % object.display_name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_finished_product
msgid "'Finished products - %s' % object.name"
msgstr "'מוצרים מוגמרים - %s' % object.name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_mrp_mo_overview
msgid "'MO Overview - %s' % object.display_name"
msgstr "'סקירה כוללת של הוראת ייצור - %s' % object.display_name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_production_order
msgid "'Production Order - %s' % object.name"
msgstr "'הוראת ייצור - %s' % object.name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_workorder
msgid "'Work Order - %s' % object.name"
msgstr "'הוראת עבודה - %s' % object.name"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "(in"
msgstr "(בתוך"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+%d יום(ימים)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "- Overview"
msgstr "- סקירה כללית"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"            ייתכן שיהיה צורך בפעולות ידניות."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_steps
msgid ""
"1 Step: Consume components from stock and produce.\n"
"              2 Steps: Pick components from stock and then produce.\n"
"              3 Steps: Pick components from stock, produce, and then move final product(s) from production area to stock."
msgstr ""
"1 שלב: צרוך רכיבים מהמלאי וייצר.\n"
"2 שלבים: אסוף רכיבים מהמלאי ואז ייצר.\n"
"3 שלבים: אסוף רכיבים מהמלאי, ייצר, ואז העבר את המוצר הסופי/ים מאזור הייצור למלאי."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "100"
msgstr "100"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "12345678901"
msgstr "12345678901"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_leg_product_template
msgid "18″ x 2½″ Square Leg"
msgstr "18″ x 2½″ Square Leg"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "2023-09-15"
msgstr "15/09/2023"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "25"
msgstr "25"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "50"
msgstr "50"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "58"
msgstr "58"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "60"
msgstr "60"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "8 GB RAM"
msgstr "8 GB RAM"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "987654321098"
msgstr ""
"987654321098\n"
" "

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/workcenter_dashboard_graph/workcenter_dashboard_graph_field.js:0
msgid ":  hours"
msgstr ": שעות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pause\" title=\"Pause\"/>"
msgstr "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pause\" title=\"Pause\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-play fs-6\" role=\"img\" aria-label=\"Run\" title=\"Run\"/>"
msgstr "<i class=\"fa fa-play fs-6\" role=\"img\" aria-label=\"Run\" title=\"Run\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"Stop\" title=\"Stop\"/>"
msgstr "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"Stop\" title=\"Stop\"/>"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Upload files to your product\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        העלה קבצים למוצר שלך\n"
"                    </p><p>\n"
"                        השתמש בתכונה זו כדי לאחסן קבצים, כגון שרטוטים או מפרטים.\n"
"                    </p>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">In Progress</span>"
msgstr "<span class=\"col-6\">בתהליך </span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">Late</span>"
msgstr "<span class=\"col-6\">מאחר</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">To Close</span>"
msgstr "<span class=\"col-6\">לסגירה</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">Waiting</span>"
msgstr "<span class=\"col-6\">ממתין</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"fw-bold text-nowrap\">To Produce</span>"
msgstr "<span class=\"fw-bold text-nowrap\">לייצור</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Backorders</span>"
msgstr "<span class=\"o_stat_text\">הזמנת החסר</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "<span class=\"o_stat_text\">BoM Overview</span>"
msgstr "<span class=\"o_stat_text\">סקירה כללית של עץ המוצר</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Child MO</span>"
msgstr "<span class=\"o_stat_text\">הוראת יצור - בן</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Load</span>"
msgstr "<span class=\"o_stat_text\">עומס</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Lost</span>"
msgstr "<span class=\"o_stat_text\">הפסד</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "<span class=\"o_stat_text\">Manufactured</span>"
msgstr "<span class=\"o_stat_text\">מיוצרים</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_form_inherit_mrp
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr "<span class=\"o_stat_text\">ייצור</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">OEE</span>"
msgstr "<span class=\"o_stat_text\">OEE</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Operations</span>"
msgstr "<span class=\"o_stat_text\">פעולות</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "<span class=\"o_stat_text\">Operations<br/>Performance</span>"
msgstr "<span class=\"o_stat_text\">פעולות<br/>ביצועים</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Overview</span>"
msgstr "<span class=\"o_stat_text\">סקירה כללית</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Performance</span>"
msgstr "<span class=\"o_stat_text\">ביצועים</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "<span class=\"o_stat_text\">Product Moves</span>"
msgstr "<span class=\"o_stat_text\">תנועות מוצר</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span class=\"o_stat_text\">Scraps</span>"
msgstr "<span class=\"o_stat_text\">פסולים</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Source MO</span>"
msgstr "<span class=\"o_stat_text\">הוראת ייצור - אב</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Traceability</span>"
msgstr "<span class=\"o_stat_text\">מעקב</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Unbuilds</span>"
msgstr "<span class=\"o_stat_text\">פירוקים</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid ""
"<span class=\"text-muted\">Modifying the quantity to produce will also "
"modify the quantities of components to consume for this manufacturing "
"order.</span>"
msgstr ""
"<span class=\"text-muted\">שינוי הכמות לייצור ישנה גם את הכמויות של רכיבים "
"לצריכה עבור הזמנה זו לייצור.</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid ""
"<span>\n"
"            Components\n"
"        </span>"
msgstr ""
"<span>\n"
"            רכיבים\n"
"        </span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span>&amp;nbsp;(minutes)</span>"
msgstr "<span>&amp;nbsp;(דקות)</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Actions</span>"
msgstr "<span>פעולות</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span>Generate BOM</span>"
msgstr "<span>צור עץ מוצר</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>New</span>"
msgstr "<span>חדש</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>Orders</span>"
msgstr "<span>הזמנות</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>PLAN ORDERS</span>"
msgstr "<span>תכנן הוראות</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_report_delivery_no_kit_section
msgid "<span>Products not associated with a kit</span>"
msgstr "<span>מוצרים נוספים</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Reporting</span>"
msgstr "<span>דו\"חות</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>WORK ORDERS</span>"
msgstr "<span>הוראות עבודה</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span>minutes</span>"
msgstr "<span>דקות</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<strong class=\"mr8 oe_inline\">to</strong>"
msgstr "<strong class=\"mr8 oe_inline\">אל</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Actual Duration (minutes)</strong>"
msgstr "<strong>זמן בפועל (דקות)</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Barcode</strong>"
msgstr "<strong>ברקוד</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Deadline:</strong><br/>"
msgstr "<strong>דדליין:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Description:</strong><br/>"
msgstr "<strong>תיאור:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Duration (minutes)</strong>"
msgstr "<strong>זמן (דקות)</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Effectiveness Category: </strong>"
msgstr "<strong>קטגוריית אפקטיביות: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "<strong>Finished Product:</strong><br/>"
msgstr "<strong>מוצר מוגמר:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Is a Blocking Reason? </strong>"
msgstr "<strong>האם זו סיבה לחסימה? </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "<strong>Manufacturing Order:</strong><br/>"
msgstr "<strong>הוראות ייצור</strong><br/> "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Operation</strong>"
msgstr "<strong>פעולה</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Product:</strong><br/>"
msgstr "<strong>מוצר:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity Producing:</strong><br/>"
msgstr "<strong>כמות בייצור:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity to Produce:</strong><br/>"
msgstr "<strong>כמות לייצור:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Reason: </strong>"
msgstr "<strong>סיבה: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Responsible:</strong><br/>"
msgstr "<strong>אחראי:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Source:</strong><br/>"
msgstr "<strong>מקור:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "<strong>Unit Cost</strong>"
msgstr "<strong>עלות ליחידה</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>WorkCenter</strong>"
msgstr "<strong>תחנת עבודה</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? זה עלול להוביל לחוסר עקביות במלאי שלך."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"A BoM of type Kit is not produced with a manufacturing order.<br/>\n"
"                                Instead, it is used to decompose a BoM into its components when:"
msgstr ""
"לא מייצרים מסמך הנדסי (BoM) מסוג ערכה באמצעות הזמנה לייצור.<br/>\n"
"במקום זאת, הוא משמש לפירוק מסמך הנדסי לרכיביו כאשר:"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "A Manufacturing Order is already done or cancelled."
msgstr "הוראת ייצור כבר בוצעה או בוטלה."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid ""
"A product with a kit-type bill of materials can not have a reordering rule."
msgstr " לא ניתן להגדיר כללי הזמנה אוטומטיים עבור מוצר עם עץ מוצר מסוג ערכה."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_rule__action
msgid "Action"
msgstr "פעולה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__active
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__active
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__active
msgid "Active"
msgstr "פעיל"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_ids
msgid "Activities"
msgstr "פעילויות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_exception_decoration
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_state
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_type_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Add a description..."
msgstr "הוסף תיאור..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Add a line"
msgstr "הוסף שורה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Add by-products to bills of materials. This can be used to get several "
"finished products as well. Without this option you only do: A + B = C. With "
"the option: A + B = C + D."
msgstr ""
"הוסף תוצרי לוואי לחשבונות של חומרים. זה יכול לשמש כדי לקבל כמה מוצרים "
"מוגמרים גם כן. ללא אפשרות זו אתה עושה רק: A + B = C. עם אפשרות: A + B = C + "
"D."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Add quality checks to your work orders"
msgstr "הוסף בדיקות איכות להוראות העבודה שלך"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_tag
msgid "Add tag for the workcenter"
msgstr "הוסף תגית לתחנת העבודה"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_manager
msgid "Administrator"
msgstr "מנהל מערכת"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__after
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "After"
msgstr "אחרי"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "All"
msgstr "הכל"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__all_move_ids
msgid "All Move"
msgstr "All Move"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__all_move_raw_ids
msgid "All Move Raw"
msgstr "All Move Raw"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_line_bom_qty_zero
msgid ""
"All product quantities must be greater or equal to 0.\n"
"Lines with 0 quantities can be used as optional lines. \n"
"You should install the mrp_byproduct module if you want to manage extra products on BoMs!"
msgstr ""
"כל הכמויות של המוצרים חייבות להיות גדולות או שוות ל-0.\n"
"שורות עם כמויות 0 יכולות לשמש כשורות אופציונליות.\n"
"יש להתקין את מודול mrp_byproduct אם אתה מעוניין לנהל מוצרים נוספים במסמכי הנדסה!"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Allocation"
msgstr "הקצאה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Allocation Report"
msgstr "דוח הקצאות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Allocation Report Labels"
msgstr "תוויות דוח הקצאה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_reception_report
msgid "Allocation Report for Manufacturing Orders"
msgstr "דוח הקצאה להזמנות ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__allow_workorder_dependencies
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__allow_workorder_dependencies
msgid "Allow Work Order Dependencies"
msgstr "אפשר תלות בין הוראות עבודה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Allow manufacturing users to modify quantities to consume, without the need "
"for prior approval"
msgstr "אפשר למשתמשים לשנות את כמויות צריכה בלי צורך באישור מראש "

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,help:mrp.field_stock_picking_type__use_create_components_lots
msgid "Allow to create new lot/serial numbers for the components"
msgstr "מאפשר ליצור אצוות/מספרים סידוריים לרכיבים"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__flexible
msgid "Allowed"
msgstr "מותר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reserve_visible
msgid "Allowed to Reserve Production"
msgstr "מותר לשמור ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unreserve_visible
msgid "Allowed to Unreserve Production"
msgstr "מותר לבטל שמירה של ייצור"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__warning
msgid "Allowed with warning"
msgstr "מותר עם אזהרה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid "Alternative Workcenters"
msgstr "תחנות עבודה חלופיות"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid ""
"Alternative workcenters that can be substituted to this one in order to "
"dispatch production"
msgstr "מרכזי עבודה חלופיים שניתן להחליף במרכז עבודה זה כדי לשלוח ייצור"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid ""
"An unbuild order is used to break down a finished product into its "
"components."
msgstr "הוראת פירוק משמשת לפירוק מוצר מוגמר לרכיביו."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "Apply on Variants"
msgstr "החל על וריאנטים"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Archive Operation"
msgstr "העבר פעולה לארכיון"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Archived"
msgstr "בארכיון"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Are you sure you want to cancel this manufacturing order?"
msgstr "האם אתה בטוח שאתה רוצה לבטל את הזמנה לייצור זו?"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Assembling"
msgstr "הרכבה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.js:0
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Attachments"
msgstr "קבצים מצורפים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__attachments_count
msgid "Attachments Count"
msgstr "כמות קבצים מצורפים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_variant_attributes
msgid "Attribute Values"
msgstr "ערכי תכונות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_mrp_reception_report
msgid "Auto Print Allocation Report"
msgstr "הדפסה אוטומטית של דוח הקצאה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_mrp_reception_report_labels
msgid "Auto Print Allocation Report Labels"
msgstr "הדפסה אוטומטית של תוויות דוח הקצאה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_done_production_order
msgid "Auto Print Done Production Order"
msgstr "הדפסה אוטומטית של הזמנת ייצור שהושלמה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_generated_mrp_lot
msgid "Auto Print Generated Lot/SN Label"
msgstr "הדפסה אוטומטית של תווית אצווה/מספר סידורי שנוצרה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_done_mrp_lot
msgid "Auto Print Produced Lot Label"
msgstr "תווית פריט שהופקה בהדפסה אוטומטית"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_done_mrp_product_labels
msgid "Auto Print Produced Product Labels"
msgstr "הדפסה אוטומטית של תוויות מוצרי ייצור"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_generated_mrp_lot
msgid ""
"Automatically print the lot/SN label when the \"Create a new serial/lot "
"number\" button is used."
msgstr ""
"הדפס אוטומטית את תווית האצווה/מספר הסידורי כאשר נעשה שימוש בכפתור \"צור מספר סידורי/אצווה חדש\".\n"
" \n"
" "

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Availabilities"
msgstr "זמינות"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Availabilities on products."
msgstr "זמינות על מוצרים."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__availability
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Availability"
msgstr "זמינות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Availability Losses"
msgstr "הפסדי זמינות"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__available
msgid "Available"
msgstr "זמין"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "Avatar"
msgstr "אווטר"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__produce_delay
msgid ""
"Average lead time in days to manufacture this product. In the case of multi-"
"level BOM, the manufacturing lead times of the components will be added. In "
"case the product is subcontracted, this can be used to determine the date at"
" which components should be sent to the subcontractor."
msgstr ""
"זמן אספקה ממוצע בימים לייצור מוצר זה. במקרה של עץ מוצר רב-שלבי, זמני האספקה "
"של רכיבי המשנה יתווספו. במקרה שהמוצר מיוצר בקבלנות משנה, ניתן להשתמש בפרמטר "
"זה כדי לקבוע את התאריך שבו יש לשלוח את הרכיבים לקבלן המשנה."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Avg Cost of Components per Unit"
msgstr "עלות ממוצעת של רכיבים ליחידה"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Avg Cost of Operations per Unit"
msgstr "עלות תפעול ממוצעת ליחידה"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Avg Total Cost per Unit"
msgstr "עלות כוללת ממוצעת ליחידה"

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "דו\"ח סקירה כללית של עץ מוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__variant_bom_ids
msgid "BOM Product Variants"
msgstr "וריאנטים של עץ המוצר"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "BOM Product Variants needed to apply this line."
msgstr "ווריאנטים של המוצר מעץ המוצר הנדרשים ליישום שורה זו."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_line_ids
msgid "BOM lines of the referred bom"
msgstr "שורות עץ מוצר של עץ המוצר המוזכר"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/product_catalog/kanban_controller.js:0
msgid "Back to BoM"
msgstr "בחזרה לעץ מוצר"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/product_catalog/kanban_controller.js:0
msgid "Back to Production"
msgstr "בחזרה לייצור "

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder_line
msgid "Backorder Confirmation Line"
msgstr "שורת הזמנה מאושרת (Backorder)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_backorder_line_ids
msgid "Backorder Confirmation Lines"
msgstr "שורות הזמנה מאושרת (Backorder)"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Backorder MO"
msgstr "הוראת עבודה חוזרת"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Backorder MO's"
msgstr "הזמנה חוזרת של הוראות ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__backorder_sequence
msgid "Backorder Sequence"
msgstr "רצף הזמנה חוזרת"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__backorder_sequence
msgid ""
"Backorder sequence, if equals to 0 means there is not related backorder"
msgstr ""
"רצף הזמנות חוזרות אם הערך שווה ל-0, המשמעות היא שאין הזמנה חוזרת קשורה."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__barcode
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Barcode"
msgstr "ברקוד"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode_batch
msgid "Based on"
msgstr "מבוסס על"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_batch_produce
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__production_text
msgid "Batch Production"
msgstr "ייצור אצווה"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__before
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Before"
msgstr "לפני "

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom
#: model:ir.model.fields,field_description:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_bom_id
#: model:ir.model.fields,field_description:mrp.field_product_replenish__bom_id
#: model:ir.model.fields,field_description:mrp.field_stock_replenish_mixin__bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Bill of Material"
msgstr "עץ מוצר"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "שורת עץ מוצר "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
msgid "Bill of Material line"
msgstr "שורת עץ מוצר "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_bom_id
msgid "Bill of Material used on the Production Order"
msgstr "עץ מוצר משמש בהוראות ייצור"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_open_bom
#: model:ir.actions.act_window,name:mrp.template_open_bom
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_ids
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__bom_id
#: model:ir.model.fields.selection,name:mrp.selection__product_document__attached_on_mrp__bom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Bill of Materials"
msgstr "עץ מוצר"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_bom_form_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom_form_action
msgid "Bills of Materials"
msgstr "עץ מוצר "

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__mo_bom_id
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_bom_id
msgid ""
"Bills of Materials, also called recipes, are used to autocomplete components"
" and work order instructions."
msgstr ""
"עץ מוצר, הנקראים גם מתכונים, משמשים להשלמה אוטומטית של רכיבים והוראות פקודת "
"עבודה."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid ""
"Bills of materials allow you to define the list of required raw\n"
"                materials used to make a finished product; through a manufacturing\n"
"                order or a pack of products."
msgstr ""
"עצי מוצר מאפשרים לך להגדיר את רשימת חומרי הגלם הנדרשים\n"
"ליצירת מוצר מוגמר וגם את הוראות הייצור הדרושות."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.js:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block"
msgstr "חסום"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter_wo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block Workcenter"
msgstr "חסום תחנת עבודה "

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__blocked
msgid "Blocked"
msgstr "חסום"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__blocked_by_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__blocked_by_workorder_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Blocked By"
msgstr "חסום על ידי"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked Time"
msgstr "זמן חסימה"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked hours over the last month"
msgstr "שעות חסומות במהלך החודש האחרון"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__name
msgid "Blocking Reason"
msgstr "סיבת חסימה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__needed_by_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__needed_by_workorder_ids
msgid "Blocks"
msgstr "בלוקים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_id
msgid "BoM"
msgstr "עץ מוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_line_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_line_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "BoM Components"
msgstr "רכיבי עץ המוצר"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Cost"
msgstr "עלות עץ המוצר"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "BoM Costs"
msgstr "עלויות עץ מוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__bom_line_id
msgid "BoM Line"
msgstr "שורת עץ המוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__bom_line_ids
msgid "BoM Lines"
msgstr "שורות עץ המוצר"

#. module: mrp
#: model:ir.actions.client,name:mrp.action_report_mrp_bom
#: model:ir.actions.report,name:mrp.action_report_bom_structure
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Overview"
msgstr "סקירה כללית של עץ מוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__type
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "BoM Type"
msgstr "סוג עץ מוצר"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_bolt_product_template
msgid "Bolt"
msgstr "בורג"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_byproducts_block/mrp_mo_overview_byproducts_block.xml:0
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_byproducts
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_byproducts
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "By-Products"
msgstr "מוצרי לוואי"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_id
msgid "By-product"
msgstr "מוצר לוואי"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "By-product %s should not be the same as BoM product."
msgstr "התוצר לוואי %s לא צריך להיות זהה למוצר שנמצא בעץ מוצר."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__byproduct_id
msgid "By-product line that generated the move in a manufacturing order"
msgstr "שורת מוצר לוואי שיצרה את התנועה בהוראת הייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__byproduct_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__byproduct_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "By-products"
msgstr "מוצרי לוואי"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
msgid "By-products cost shares must be positive."
msgstr " עלות תוצרי לוואי חייבת להיות חיובית."

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_byproduct
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_byproduct_form_view
msgid "Byproduct"
msgstr "מוצר לוואי"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
msgid "Byproducts"
msgstr "תוצרי לוואי"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Can't find any production location."
msgstr "לא ניתן למצוא מיקום ייצור כלשהו."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Cancel"
msgstr "בטל"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__cancel
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__cancel
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Cancelled"
msgstr "בוטל"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"Cannot compute days to prepare due to missing route info for at least 1 "
"component or for the final product."
msgstr ""
"לא ניתן לחשב ימים להכנה עקב חסר מידע על מסלול עבור רכיב אחד לפחות או עבור "
"המוצר הסופי."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Cannot delete a manufacturing order in done state."
msgstr "לא ניתן למחוק הוראת ייצור במצב בוצע."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__default_capacity
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__capacity
msgid "Capacity"
msgstr "קיבולת"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_capacity_positive_capacity
msgid "Capacity should be a positive number."
msgstr "הקיבולת צריכה להיות מספר חיובי."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_reported_from_previous_wo
msgid "Carried Quantity"
msgstr "כמות מועברת"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Catalog"
msgstr "קטלוג"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__loss_type
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Category"
msgstr "קטגוריה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Center A"
msgstr "תחנה A"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Change Product Qty"
msgstr "שנה כמות מוצר"

#. module: mrp
#: model:ir.model,name:mrp.model_change_production_qty
msgid "Change Production Qty"
msgstr "שנה כמות הייצור"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_change_production_qty
msgid "Change Quantity To Produce"
msgstr "שנה כמות לייצור"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"Changing the product or variant will permanently reset all previously "
"encoded variant-related data."
msgstr ""
"שינוי המוצר או הווריאנט יאפס לצמיתות את כל הנתונים הקודמים שהוזנו הקשורים "
"לווריאנט."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Check availability"
msgstr "בדוק זמינות"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/wizard/stock_label_type.py:0
msgid "Choose Labels Layout"
msgstr "בחר תבנית תוויות"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Choose Type of Labels To Print"
msgstr "בחר סוג תוויות להדפסה"

#. module: mrp
#: model:ir.model,name:mrp.model_picking_label_type
msgid "Choose whether to print product or lot/sn labels"
msgstr "בחר האם להדפיס את המוצר או את המספר האצווה/הסידורי שלו."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_stop
msgid "Cleanup Time"
msgstr "זמן ניקוי"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__time_stop
msgid "Cleanup Time (minutes)"
msgstr "זמן ניקוי (דקות)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__code
msgid "Code"
msgstr "קוד"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__color
msgid "Color"
msgstr "צבע"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__color
msgid "Color Index"
msgstr "אינדקס צבעים"

#. module: mrp
#: model:ir.model,name:mrp.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__company_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Company"
msgstr "חברה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Component"
msgstr "רכיב"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability
msgid "Component Status"
msgstr "סטטוס רכיב"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
msgid "Component of Draft MO"
msgstr "רכיב בטיוטת הוראת עבודה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__component_separator
msgid "Component separator"
msgstr "מפריד רכיבים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_raw_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Components"
msgstr "רכיבים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability_state
msgid "Components Availability State"
msgstr "מצב זמינות רכיבים"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Components Available"
msgstr "רכיבים זמינים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_src_id
msgid "Components Location"
msgstr "מיקום הרכיבים"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__priority
msgid ""
"Components will be reserved first for the MO with the highest priorities."
msgstr "שמירת רכיבים תתבצע תחילה לפי סדר עדיפויות של הוראות הייצור."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Compute"
msgstr "חשב"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__auto
msgid "Compute based on tracked time"
msgstr "חישוב מבוסס על זמן מעקב"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"Compute the days required to resupply all components from BoM, by either "
"buying or manufacturing the components and/or subassemblies."
"                                                       Also note that "
"purchase security lead times will be added when appropriate."
msgstr ""
"חשב את הימים הנדרשים כדי לספק מחדש את כל הרכיבים מהעץ מוצר, על ידי רכש או "
"ייצור של הרכיבים ו/או תת המכלולים. כמו כן, שימו לב כי זמני אספקת אבטחת רכישה"
" יתווספו במידת הצורך."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_computed_on
msgid "Computed on last"
msgstr "זמן שחושב בפעולה אחרונה"

#. module: mrp
#: model:ir.model,name:mrp.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_configuration
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Configuration"
msgstr "תצורה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Confirm"
msgstr "אשר"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Confirmed"
msgstr "מאושר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_consumed_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Consumed"
msgstr "בשימוש"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__consume_line_ids
msgid "Consumed Disassembly Lines"
msgstr "קווי פירוק נצרכים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__consume_unbuild_id
msgid "Consumed Disassembly Order"
msgstr "הוראת פירוק נצרכה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__operation_id
msgid "Consumed in Operation"
msgstr "בשימוש בפעולה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_production__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__consumption
msgid "Consumption"
msgstr "צריכה"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_consumption_warning
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Consumption Warning"
msgstr "אזהרת צריכה"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_production__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"המרה בין יחידות מידה יכולה להתרחש רק אם הן שייכות לאותה קטגוריה. ההמרה תיעשה"
" על בסיס היחס בניהן."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Copy Existing Operations"
msgstr "העתקת פעולות קיימות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_copy_to_bom_tree_view
msgid "Copy selected operations"
msgstr "העתקת פעולות קיימות"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Cost Breakdown of Products"
msgstr "פירוט עלויות של מוצרים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__cost_share
#: model:ir.model.fields,field_description:mrp.field_stock_move__cost_share
msgid "Cost Share (%)"
msgstr "נתח עלות (%)"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost as it is currently accumulated"
msgstr "העלות כפי שהיא נצברה עד כה"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost based on cost projection"
msgstr "עלות מבוססת על תחזית עלויות"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost based on related replenishments. Otherwise cost from product form"
msgstr "עלות מבוססת על חידושי מלאי קשורים. אחרת העלות נלקחת מהטופס מוצר עצמו"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost based on the BoM"
msgstr "עלות מבוססת על עץ מוצר"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Cost of Components per unit"
msgstr "עלות רכיבים ליחידה "

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Cost of Operations per unit"
msgstr "עלות תפעול ליחידה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__costs_hour
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__costs_hour
msgid "Cost per hour"
msgstr "עלות לשעה "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Costing Information"
msgstr "מידע על תמחיר"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
msgid "Costs"
msgstr "עלויות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking__production_count
msgid "Count of MO generated"
msgstr "ספירת הוראות ייצור שנוצרו"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_backorder_count
msgid "Count of linked backorder"
msgstr "כמות הזמנות חוזרות מקושרות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__use_create_components_lots
msgid "Create New Lots/Serial Numbers for Components"
msgstr "צור אצוות/ מספרים סידוריים חדשים עבור רכיבים"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create a Backorder"
msgstr "צור הזמנה חוזרת "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid ""
"Create a backorder if you expect to process the remaining products later. Do"
" not create a backorder if you will not process the remaining products."
msgstr ""
"צור הזמנה חוזרת אם אתה מצפה לעבד את המוצרים הנותרים מאוחר יותר. אל תיצור "
"הזמנה חוזרת אם לא תעבד את המוצרים הנותרים."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid "Create a new operation"
msgstr "יצירת פעולה חדשה"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid "Create a new work center"
msgstr "צור תחנת עבודה חדשה"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_workcenter_report
msgid "Create a new work orders performance"
msgstr "צור ביצועי הוראות עבודה חדשים"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__days_to_prepare_mo
msgid ""
"Create and confirm Manufacturing Orders this many days in advance, to have enough time to replenish components or manufacture semi-finished products.\n"
"Note that security lead times will also be considered when appropriate."
msgstr ""
"צור ואשר הוראות ייצור מספר ימים מראש, כדי לאפשר מספיק זמן להשלמת רכיבים או לייצור מוצרים חצי-גמורים.\n"
"שים לב שגם זמני ביטחון יילקחו בחשבון, כאשר הדבר רלוונטי."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create backorder"
msgstr "יצירת הזמנה חוזרת "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr "צור דפי עבודה הניתנים להתאמה אישית עבור בדיקות האיכות שלך"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__allow_operation_dependencies
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__allow_operation_dependencies
msgid ""
"Create operation level dependencies that will influence both planning and "
"the status of work orders upon MO confirmation. If this feature is ticked, "
"and nothing is specified, Odoo will assume that all operations can be "
"started simultaneously."
msgstr ""
"צור תלות בין פעולות שישפיעו על התכנון ועל הסטטוס של הוראות העבודה בפקודת "
"הייצור. אם הגדרה זו מוחלת אך לא החלת תלות בין פעולות, המערכת תניח שכל "
"הפעולות יכולות להתחיל במקביל"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__created_production_id
msgid "Created Production Order"
msgstr "צור הוראת ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Creates a new serial/lot number"
msgstr "צור מספר סידורי /מספר אצווה חדש"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"כמות מוצרים נוכחית.\n"
"בהקשר עם מיקום מלאי יחיד, זה כולל סחורות המאוחסנות במיקום זה, או כל אחד מהילדים שלו.\n"
"בהקשר של מחסן יחיד, זה כולל סחורות המאוחסנות במיקום המלאי של מחסן זה, או כל אחד מהילדים שלו.\n"
"מאוחסן במיקום המלאי של המחסן של חנות זו, או כל אחד מילדיה.\n"
"אחרת, זה כולל מוצרים המאוחסנים בכל מיקום מלאי עם סוג 'פנימי'."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_producing
msgid "Currently Produced Quantity"
msgstr "כמות נוכחית מיוצרת "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_description_variants
msgid "Custom Description"
msgstr "תיאור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Date"
msgstr "תאריך"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__search_date_category
msgid "Date Category"
msgstr "קטגוריית תאריך"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Date by Month"
msgstr "תאריך לפי חודש"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_finished
msgid ""
"Date you expect to finish production or actual date you finished production."
msgstr ""
"התאריך שבו אתה מצפה לסיים את הייצור או התאריך שבו סיימת את הייצור בפועל."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_start
msgid ""
"Date you plan to start production or date you actually started production."
msgstr ""
"התאריך שבו אתה מתכנן להתחיל את הייצור או התאריך שבו התחלת את הייצור בפועל."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Date: Last 365 Days"
msgstr "תאריך: 365 ימים אחרונים"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_pdf_line
msgid "Days"
msgstr "ימים"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "Days to Supply Components"
msgstr "ימים לאספקת רכיבים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__days_to_prepare_mo
msgid "Days to prepare Manufacturing Order"
msgstr "מספר ימים בכדי להתכונן להוראת ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_deadline
msgid "Deadline"
msgstr "תאריך יעד"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Default Duration"
msgstr "משך ברירת מחדל"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__use_manufacturing_lead
msgid "Default Manufacturing Lead Time"
msgstr "ברירת מחדל ליצור זמן הספקה "

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__default_capacity
msgid ""
"Default number of pieces (in product UoM) that can be produced in parallel "
"(at the same time) at this work center. For example: the capacity is 5 and "
"you need to produce 10 units, then the operation time listed on the BOM will"
" be multiplied by two. However, note that both time before and after "
"production will only be counted once."
msgstr ""
" מספר ברירת מחדל של יחידות (ביחידת המידה של המוצר) שניתן לייצר במקביל (בו-"
"זמנית) במרכז עבודה זה. לדוגמה: אם הקיבולת היא 5 ויש צורך לייצר 10 יחידות, "
"זמן הפעולה המופיע בעץ מוצר יוכפל פי שתיים. עם זאת, יש לשים לב שזמן ההכנה "
"לפני הייצור והזמן לאחר הייצור ייספרו רק פעם אחת."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "יחידת מידה ברירת מחדל המשמשת לביצוע כל פעולות המלאי."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid ""
"Define the components and finished products you wish to use in\n"
"                bill of materials and manufacturing orders."
msgstr ""
"הגדר את הרכיבים והמוצרים המוגמרים שבהם ברצונך להשתמש\n"
"בעץ מוצר והזמנות ייצור."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__resource_calendar_id
msgid ""
"Define the working schedule of the resource. If not set, the resource will "
"have fully flexible working hours."
msgstr ""
"הגדר את לוח הזמנים של העבודה של המשאב. אם לא מוגדר, למשאב יהיו שעות עבודה "
"גמישות לחלוטין."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__consumption
msgid ""
"Defines if you can consume more or less components than the quantity defined on the BoM:\n"
"  * Allowed: allowed for all manufacturing users.\n"
"  * Allowed with warning: allowed for all manufacturing users with summary of consumption differences when closing the manufacturing order.\n"
"  Note that in the case of component Highlight Consumption, where consumption is registered manually exclusively, consumption warnings will still be issued when appropriate also.\n"
"  * Blocked: only a manager can close a manufacturing order when the BoM consumption is not respected."
msgstr ""
" מגדיר אם ניתן לצרוך יותר או פחות רכיבים מהכמות שהוגדרה בעץ מוצר:\n"
"מותר – מותר לכל משתמשי הייצור.\n"
"מותר עם אזהרה – מותר לכל משתמשי הייצור, עם הצגת סיכום פערי הצריכה בעת סגירת הוראת הייצור.\n"
"שים לב: במקרה של רכיב עם הדגשת צריכה, שבו הצריכה נרשמת ידנית בלבד – תוצג אזהרת צריכה בעת הצורך גם כאן.\n"
"חסום – רק מנהל יכול לסגור הוראת ייצור כאשר צריכת רכיבים אינה תואמת לעץ מוצר."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delay_alert_date
msgid "Delay Alert Date"
msgstr "עיכוב תאריך התראה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Delayed Productions"
msgstr "ייצורים מושהים"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Delegate part of the production process to subcontractors"
msgstr "העברת חלק מתהליך הייצור לקבלני משנה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delivery_count
msgid "Delivery Orders"
msgstr "הזמנות משלוח"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__description
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_note
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description"
msgstr "תיאור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description of the work center..."
msgstr "תיאור תחנת העבודה..."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_dest_id
msgid "Destination Location"
msgstr "מיקום יעד"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unbuild_id
msgid "Disassembly Order"
msgstr "הוראת פירוק"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Discard"
msgstr "בטל"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.xml:0
msgid "Display"
msgstr "הצג"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__display_name
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lot_ids
msgid "Display the serial number shortcut on the moves"
msgstr "הצג את קיצור הדרך של המספר הסידורי על התנועות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Displays the consumed Lot/Serial Numbers."
msgstr "הצגה של מספרי אצווה/סידוריים שנעשה בהם שימוש."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Displays the produced Lot/Serial Numbers."
msgstr "הצגת את המספרי אצווה\\סידורים של המותר."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "Do you confirm you want to unbuild"
msgstr "האם אתה מאשר שאתה רוצה לפרק"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Documentation"
msgstr "תיעוד"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.xml:0
#: model:ir.model.fields,field_description:mrp.field_stock_move__is_done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Done"
msgstr "בוצע"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__draft
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__draft
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Draft"
msgstr "טיוטה"

#. module: mrp
#: model:product.template,name:mrp.product_product_drawer_drawer_product_template
msgid "Drawer Black"
msgstr "מגירה שחורה "

#. module: mrp
#: model:product.template,name:mrp.product_product_drawer_case_product_template
msgid "Drawer Case Black"
msgstr "מארז מגירה שחור"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_drawer_drawer_product_template
msgid "Drawer on casters for great usability."
msgstr "מגירה על גלגלים רב שימושית."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__duration
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Duration"
msgstr "משך זמן"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Duration (minutes)"
msgstr "משך זמן (דקות)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode
msgid "Duration Computation"
msgstr "חישוב משך זמן"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_percent
msgid "Duration Deviation (%)"
msgstr "סטיית משך זמן (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_unit
msgid "Duration Per Unit"
msgstr "משך זמן ליחידה "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_type
msgid "Effectiveness"
msgstr "יעילות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_type
msgid "Effectiveness Category"
msgstr "קטגוריה אפקטיבית"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_finished
msgid "End"
msgstr "סיום"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_end
msgid "End Date"
msgstr "תאריך סיום"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__tracking
#: model:ir.model.fields,help:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,help:mrp.field_mrp_workorder__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "וודא מעקב אחר מוצר מנוהל מלאי במחסן שלך."

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason1
msgid "Equipment Failure"
msgstr "תקלות בציוד "

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Estimated %s"
msgstr "משוער %s"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s) occurred on the manufacturing order(s):"
msgstr "חריגה(ות) התרחשו בהורא(ות) הייצור:"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s):"
msgstr "חריגה(ות):"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Exp %s"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__expected
msgid "Expected"
msgstr "צפוי"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Expected %s"
msgstr "צפוי %s"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__duration_expected
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_expected
msgid "Expected Duration"
msgstr "משך זמן צפוי"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Expected Duration (minutes)"
msgstr "משך זמן צפוי (בדקות)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__production_text_help
msgid "Explanation for batch production"
msgstr "הסבר לייצור אצווה"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Files attached to the product."
msgstr "קבצים מצורפים למוצר."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Filters"
msgstr "מסננים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_final_id
msgid "Final Location from procurement"
msgstr "המיקום הסופי לפי הרכש"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__done
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Finished"
msgstr "סיים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__order_finished_lot_id
msgid "Finished Lot/Serial Number"
msgstr "אצווה/ מספר סידורי מוכן"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_finished_ids
msgid "Finished Moves"
msgstr "תנועות שהסתיימו"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__finished_move_line_ids
msgid "Finished Product"
msgstr "מוצר מוגמר"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_finished_product
msgid "Finished Product Label (PDF)"
msgstr "תווית מוצר גמור (PDF)"

#. module: mrp
#: model:ir.actions.report,name:mrp.label_manufacture_template
msgid "Finished Product Label (ZPL)"
msgstr "תווית מוצר מוגמר (ZPL)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_finished_ids
msgid "Finished Products"
msgstr "מוצרים מוגמרים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_dest_id
msgid "Finished Products Location"
msgstr "מיקום המוצרים המוגמרים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lot_name
msgid "First Lot/SN"
msgstr "אצווה\\מספר סידורי ראשון"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__consumption
msgid "Flexible Consumption"
msgstr "צריכה גמישה"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.js:0
msgid "Fold"
msgstr "קפל"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_type_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Force"
msgstr "אלץ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Forecast"
msgstr "תחזית"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Forecast Report"
msgstr "דוח תחזית"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"כמות חיזוי (מחושב לפי : כמות זמינה  - כמות יוצאת + כמות נכנסת)\n"
"בהקשר זה  עם מיקום מלאי יחידי, זה כולל סחורות המאוחסנות במיקום זה, או כל אחד מתתי המיקומים עבור מלאי זה.\n"
"בהקשר עם מחסן יחיד, זה כולל סחורות המאוחסנים באתר המלאי של מחסן זה, או כל אחד מתתי המיקומים של המחסן.\n"
"אחרת, זה כולל סחורות המאוחסנים בכל מקום במלאי עם סוג \"פנימי\"."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Forecasted"
msgstr "חזוי"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__forecasted_issue
msgid "Forecasted Issue"
msgstr "תחזית בעיות"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Free to Use"
msgstr "זמינים לשימוש"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Free to Use / On Hand"
msgstr "זמינים לשימוש / זמינים במלאי"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Free to use / On Hand"
msgstr "זמינים לשימוש / זמינים במלאי"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "From"
msgstr "מ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Fully Productive"
msgstr "פרודוקטיבי לחלוטין"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason7
msgid "Fully Productive Time"
msgstr "זמן פרודוקטיבי לחלוטין"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Future Activities"
msgstr "פעילויות עתידיות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "General Information"
msgstr "מידע כללי"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Generate"
msgstr "הפק"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Generate Serial Numbers"
msgstr "ייצר מספרים סידורים"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Generate a new BoM from this Manufacturing Order"
msgstr "צור עץ מוצר חדש להוראת ייצור זו"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__generated_mrp_lot_label_to_print
msgid "Generated Lot/SN Label to Print"
msgstr "ייצר מספרי אצווה\\סידורים להדפסה"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
msgid "Get statistics about the work orders duration related to this routing."
msgstr "קבל נתונים סטטיסטיים לגבי משך הוראות העבודה הקשורות למסלול זה."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__sequence
msgid ""
"Gives the sequence order when displaying a list of routing Work Centers."
msgstr "נותן את סדר הרצף בעת הצגת רשימה של ניתוב תחנות עבודה."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__sequence
msgid "Gives the sequence order when displaying a list of work centers."
msgstr "נותן את סדר הרצף בעת הצגת רשימה של תחנות עבודה."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__sequence
msgid "Gives the sequence order when displaying."
msgstr "נותן את סדר הרצף בעת הצגת."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__google_slide
msgid "Google Slide"
msgstr "Google Slide"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Google Slide Link"
msgstr "קישור Google Slide "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Group By"
msgstr "קבץ לפי"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Group By..."
msgstr "קבץ לפי..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Group by..."
msgstr "קבץ לפי..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Hardware"
msgstr "חומרה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_produced
msgid "Has Been Produced"
msgstr "הופק"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking__has_kits
msgid "Has Kits"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_production__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__has_routing_lines
msgid "Has Routing Lines"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__has_worksheet
msgid "Has Worksheet"
msgstr "בעל גיליון עבודה"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__product_document__attached_on_mrp__hidden
msgid "Hidden"
msgstr "נסתר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__manual_consumption
msgid "Highlight Consumption"
msgstr "הדגש צריכה"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__costs_hour
msgid "Hourly processing cost."
msgstr "עלות עיבוד לפי שעה."

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Hours"
msgstr "שעות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__id
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__id
msgid "ID"
msgstr "מזהה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_exception_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_exception_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_id
msgid ""
"If a product variant is defined the BOM is available only for this product."
msgstr "אם הוגדר גרסת מוצר,עץ המוצר זמין רק עבור מוצר זה."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_needaction
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__propagate_cancel
msgid ""
"If checked, when the previous move of the move (which was generated by a "
"next procurement) is cancelled or split, the move generated by this move "
"will too"
msgstr ""
"אם מסומן, כאשר המהלך הקודם של המהלך (שנוצר על ידי רכש הבא) מבוטל או מפוצל, "
"המהלך שנוצר על ידי מהלך זה יכול אליו גם "

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"אם השדה הפעיל מוגדר כלא נכון, הוא יאפשר לך להסתיר את רשומת המשאב מבלי להסיר "
"אותה."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_mrp_reception_report_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the allocation "
"report labels of a MO when it is done."
msgstr ""
"אם תיבת סימון זו מסומנת, Odoo תדפיס אוטומטית את תוויות דוחות ההקצאה של הוראת"
" הייצור כאשר היא תסתיים."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_mrp_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically print the allocation "
"report of a MO when it is done and has assigned moves."
msgstr ""
"אם תיבת סימון זו מסומנת, Odoo תדפיס אוטומטית את תוויות דוחות ההקצאה של הוראת"
" הייצור כאשר היא תסתיים ומתי שיוקצו תנועות."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_done_mrp_lot
msgid ""
"If this checkbox is ticked, Odoo will automatically print the lot/SN label "
"of a MO when it is done."
msgstr ""
"אם תיבת סימון זו מסומנת, Odoo תדפיס אוטומטית את תווית האצווה/המספר הסידורי "
"של הוראת הייצור כאשר היא תסתיים."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_done_mrp_product_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the product labels"
" of a MO when it is done."
msgstr ""
"אם תיבת סימון זו מסומנת, Odoo תדפיס אוטומטית את תוויות המוצר של הוראת הייצור"
" כאשר היא תסתיים."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_done_production_order
msgid ""
"If this checkbox is ticked, Odoo will automatically print the production "
"order of a MO when it is done."
msgstr ""
"אם תיבה זו מסומנת, Odoo תדפיס באופן אוטומטי את פקודת הייצור של הוראת הייצור "
"כאשר היא תושלם."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Impacted Transfer(s):"
msgstr "העברה(ות) שהושפעו:"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "Import Template for Bills of Materials"
msgstr "ייבא תבנית עבור עצי מוצר"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"Impossible to plan the workorder. Please check the workcenter "
"availabilities."
msgstr "לא ניתן לתכנן את הוראת העבודה. אנא בדוק את זמינות תחנות העבודה."

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
msgid "Impossible to plan. Please check the workcenter availabilities."
msgstr "תכנון בלתי אפשרי. אנא בדוק את זמינות מרכזי העבודה."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__progress
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__progress
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "In Progress"
msgstr "בתהליך"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "In Transit"
msgstr "בהעברה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_view_search_catalog
msgid "In the BoM"
msgstr "בתוך עץ מוצר"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_view_search_catalog
msgid "In the MO"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_deadline
msgid ""
"Informative date allowing to define when the manufacturing order should be "
"processed at the latest to fulfill delivery on time."
msgstr ""
"תאריך אינפורמטיבי המאפשר לקבוע עד מתי יש לעבד את פקודת הייצור על מנת לעמוד "
"במועד האספקה."

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_moves
msgid "Inventory Moves"
msgstr "תנועות מלאי"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__move_line_ids
msgid ""
"Inventory moves for which you must scan a lot number at this work order"
msgstr "מהלכים מלאי שעבורם עליך לסרוק מספר אצווה בהזמנת עבודה זו"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_delayed
msgid "Is Delayed"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__is_kits
#: model:ir.model.fields,field_description:mrp.field_product_template__is_kits
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__product_is_kit
msgid "Is Kits"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_locked
msgid "Is Locked"
msgstr "נעול"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__manual
msgid "Is a Blocking Reason"
msgstr "סיבת חסימה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_user_working
msgid "Is the Current User Working"
msgstr "המשתמש הנוכחי עובד"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "It has already been unblocked."
msgstr "כבר בוטלה החסימה."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"It is not possible to unplan one single Work Order. You should unplan the "
"Manufacturing Order instead in order to unplan all the linked operations."
msgstr ""
"לא ניתן לבטל את התכנון של הזמנת עבודה אחת. עליך לבטל את תכנון הזמנת הייצור "
"במקום זאת כדי לבטל את התכנון של כל הפעולות המקושרות."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_planned
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_planned
msgid "Its Operations are Planned"
msgstr "פעולות עבודה תוכננו"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__json_popover
msgid "JSON data for the popover widget"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "John Doe"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "גרף לוח בקרה של קנבן"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__description_bom_line
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__description_bom_line
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__bom_id
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__phantom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Kit"
msgstr "ערכה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Laptop"
msgstr "מחשב נייד"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Laptop Model X"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Laptop model X"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Laptop with 16GB RAM"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__last_working_user_id
msgid "Last user that worked on this work order."
msgstr "משתמש אחרון שעבד על הוראת עבודה זו."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__late
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Late"
msgstr "מאחר"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Activities"
msgstr "פעילויות באיחור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Availability"
msgstr "זמינות מאוחרת"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late MO or Late delivery of components"
msgstr "איחור הוראת ייצור או קבלת רכיבים"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__components_availability
msgid ""
"Latest component availability status for this MO. If green, then the MO's "
"readiness status is ready, as per BOM configuration."
msgstr ""
"מוכנות הוראות הייצור: הסטטוס האחרון לזמינות רכיבים עבור הוראת ייצור זו. אם "
"ירוק, אז הסטטוס של מוכנות ההוראה מוכן, בהתאם להגדרת עץ המוצר (BOM)."

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_wood_ply_product_template
msgid "Layers that are stick together to assemble wood panels."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Lead Time"
msgstr "זמן אספקה "

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
msgid "Lead Times"
msgstr "זמני אספקה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__leave_id
msgid "Leave"
msgstr "עזוב"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_product_document__attached_on_mrp
msgid ""
"Leave hidden if document only accessible on product form.\n"
"Select Bill of Materials to visualise this document as a product attachment when this product is in a bill of material."
msgstr ""
"השאר מוסתר אם המסמך נגיש רק בטופס המוצר.\n"
"בחר עץ מוצר כדי להציג מסמך זה כתוספת מוצר כאשר מוצר זה נמצא בעץ מוצר."

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning_line
msgid "Line of issue consumption"
msgstr "שורת צריכת רכיבים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__location_id
msgid "Location"
msgstr "מיקום"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_id
msgid "Location where the product you want to unbuild is."
msgstr "המיקום שבו נמצא המוצר שברצונך לפרק."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_src_id
msgid "Location where the system will look for components."
msgstr "המיקום שבו המערכת תחפש רכיבים."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "מיקום בו המערכת תאחסן מוצרים גמורים."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_dest_id
msgid ""
"Location where you want to send the components resulting from the unbuild "
"order."
msgstr "מיקום אליו ברצונך לשלוח את הרכיבים הנוצרים מהוראת הפירוק."

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_lock_unlock
msgid "Lock/Unlock"
msgstr "נעל/הסר נעילה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_id
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Loss Reason"
msgstr "סיבת הפסד/פחת"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid "Lot %s does not exist."
msgstr "אצווה%s לא קיימת."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lots_quantity_separator
msgid "Lot quantity separator"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lots_separator
msgid "Lot separator"
msgstr "מפריד אצווה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Lot/SN Label"
msgstr "תווית אצווה\\ מספר סידורי"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__done_mrp_lot_label_to_print
msgid "Lot/SN Label to Print"
msgstr "אצווה\\מספר סידורי להדפסה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Lot/SN Labels"
msgstr "תוויות אצווה\\מספר סידורי"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_lot
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Lot/Serial"
msgstr "מספר סידורי/אצווה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__lot_producing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__lot_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__finished_lot_id
msgid "Lot/Serial Number"
msgstr "מס' אצווה/ סידורי"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Lot/Serial Numbers"
msgstr "מספרים של אצווה/ סידוריים"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_traceability
msgid "Lots/Serial Numbers"
msgstr "מספר סידוריים/אצוות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_backorder_id
msgid "MO Backorder"
msgstr "הזמנה חוזרת של הוראת ייצור"

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_cancelled
#: model:mail.message.subtype,name:mrp.mrp_mo_in_cancelled
msgid "MO Cancelled"
msgstr "הוראת ייצור מבוטלת"

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_confirmed
#: model:mail.message.subtype,name:mrp.mrp_mo_in_confirmed
msgid "MO Confirmed"
msgstr "הוראת ייצור מאושרת"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "MO Cost"
msgstr "עלות הוראת ייצור"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "MO Costs"
msgstr "עלויות הוראת ייצור"

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_done
#: model:mail.message.subtype,name:mrp.mrp_mo_in_done
msgid "MO Done"
msgstr "הוראת ייצור הסתיימה"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "MO Generated by %s"
msgstr "הוראת ייצור נוצרה ע\"י %s"

#. module: mrp
#: model:ir.actions.client,name:mrp.action_report_mo_overview
#: model:ir.actions.report,name:mrp.action_report_mrp_mo_overview
msgid "MO Overview"
msgstr "סקירה כללית של הוראת ייצור"

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "דו\"ח סקירה כללית של הוראת ייצור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "MO Pending"
msgstr "הוראת ייצור ממתינה"

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_progress
#: model:mail.message.subtype,name:mrp.mrp_mo_in_progress
msgid "MO Progress"
msgstr "התקדמות הוראת ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reservation_state
msgid "MO Readiness"
msgstr "סטטוס ייצור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "MO Ready"
msgstr "מוכן"

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_to_close
#: model:mail.message.subtype,name:mrp.mrp_mo_in_to_close
msgid "MO To Close"
msgstr "הוראת ייצור לסגירה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_document__attached_on_mrp
msgid "MRP : Visible at"
msgstr ""

#. module: mrp
#: model:ir.actions.client,name:mrp.mrp_reception_action
msgid "MRP Reception Report"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_routings
msgid "MRP Work Orders"
msgstr "הוראות עבודה MRP"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss_type
msgid "MRP Workorder productivity losses"
msgstr "MRP הפסדי פרודוקטיביות של הזמנות עבודה "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "MRP-001"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_routings
msgid "Manage Work Order Operations"
msgstr "נהל פעולות הוראת עבודה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__manual_consumption
msgid "Manual Consumption"
msgstr "צריכה ידנית"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid "Manual Duration"
msgstr "משך זמן ידני"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manuf. Lead Time"
msgstr "זמן ייצור ממוצע"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_steps
#: model:ir.model.fields.selection,name:mrp.selection__stock_rule__action__manufacture
#: model:stock.route,name:mrp.route_warehouse0_manufacture
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_stock_rule
msgid "Manufacture"
msgstr "ייצור"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__mrp_one_step
msgid "Manufacture (1 step)"
msgstr "ייצור (צעד 1)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_mto_pull_id
msgid "Manufacture MTO Rule"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_pull_id
msgid "Manufacture Rule"
msgstr "כלל ייצור"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "Manufacture Security Lead Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__normal
msgid "Manufacture this product"
msgstr "ייצר מוצר זה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_to_resupply
msgid "Manufacture to Resupply"
msgstr "ייצור כדי להחזיר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__mrp_product_qty
#: model:ir.model.fields,field_description:mrp.field_product_template__mrp_product_qty
msgid "Manufactured"
msgstr "מיוצר"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "Manufactured Products"
msgstr "מוצרים מיוצרים"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Manufactured in the last 365 days"
msgstr "מיוצר במהלך 365 הימים האחרונים."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.actions.client,name:mrp.action_mrp_display_fullscreen
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__code__mrp_operation
#: model:ir.ui.menu,name:mrp.menu_mrp_root
#: model_terms:ir.ui.view,arch_db:mrp.product_document_form
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Manufacturing"
msgstr "ייצור"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_buttons.xml:0
msgid "Manufacturing Forecast"
msgstr "תחזית ייצור"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__produce_delay
#: model:ir.model.fields,field_description:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__manufacturing_lead
msgid "Manufacturing Lead Time"
msgstr "זמן ייצור ממוצע"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manu_type_id
msgid "Manufacturing Operation Type"
msgstr "סוג פעולת ייצור"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__production_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Manufacturing Order"
msgstr "הוראת ייצור"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_product_mrp_production_workcenter
#: model:ir.actions.act_window,name:mrp.action_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_production_action
#: model:ir.actions.act_window,name:mrp.mrp_production_action_picking_deshboard
#: model:ir.ui.menu,name:mrp.menu_mrp_production_action
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_view_activity
#: model_terms:ir.ui.view,arch_db:mrp.view_production_calendar
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_production_pivot
msgid "Manufacturing Orders"
msgstr "הוראות ייצור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are in confirmed state."
msgstr "הוראות ייצור אשר נמצאים במצב מאושר."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__ready_to_produce
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manufacturing Readiness"
msgstr "מוכנות הייצור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Manufacturing Reference"
msgstr "מזהה ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__manufacturing_visibility_days
msgid "Manufacturing Visibility Days"
msgstr "ימי הייצור"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc."
msgstr ""
"פעולות ייצור מתבצעות בתחנות עבודה.  תחנת עבודה יכול לכלול \n"
"                עובדים ו/או מכונות, המשמשים לחישוב עלויות, תזמון, תכנון קיבולת ועוד."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc.\n"
"                They can be defined via the configuration menu."
msgstr ""
"פעולות ייצור מתבצעות בתחנות עבודה. תחנת עבודה יכול לכלול \n"
"עובדים ו/או מכונות, המשמשים לחישוב עלויות, תזמון, תכנון קיבולת ועוד .                            הם יכולים להיות מוגדרים בתפריט ההגדרות."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reservation_state
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_availability
msgid ""
"Manufacturing readiness for this MO, as per bill of material configuration:\n"
"            * Ready: The material is available to start the production.\n"
"            * Waiting: The material is not available to start the production.\n"
msgstr ""
"מוכנות הייצור להזמנת הייצור  הזו, בהתאם להגדרות עץ המוצר:\n"
"*מוכן: החומר זמין להתחלת הייצור.\n"
"*ממתין: החומר אינו זמין להתחלת הייצור.\n"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_picking_tree_mrp_operation
#: model:ir.actions.act_window,name:mrp.action_picking_tree_mrp_operation_graph
#: model:ir.ui.menu,name:mrp.mrp_operation_picking
msgid "Manufacturings"
msgstr "ייצור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Marc Demo"
msgstr ""

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_mark_done
msgid "Mark as Done"
msgstr "סמן כבוצע"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Mass Produce"
msgstr "ייצור המוני"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_mps
msgid "Master Production Schedule"
msgstr "תוכנית אב לייצור"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason0
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Material Availability"
msgstr "זמינות חומר"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_merge
msgid "Merge"
msgstr "מזג"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "כלל מלאי מינימלי"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Minutes"
msgstr "דקות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Miscellaneous"
msgstr "שונות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_byproduct_ids
msgid "Move Byproduct"
msgstr "העבר תוצר לוואי"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr "תנועות למעקב"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_consumption_warning_line_ids
msgid "Mrp Consumption Warning Line"
msgstr "שורת אזהרת צריכה בייצור"

#. module: mrp
#: model:ir.actions.client,name:mrp.action_mrp_display
msgid "Mrp Display"
msgstr "תצוגת ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_procurement_group__mrp_production_ids
msgid "Mrp Production"
msgstr "תכנון ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_count
msgid "Mrp Production Count"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__my_activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__my_activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "My MOs"
msgstr "הוראות הייצור שלי"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__never_product_template_attribute_value_ids
msgid "Never attribute values"
msgstr "אין לשייך ערכים בשום שלב"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/stock_rule.py:0
msgid "New"
msgstr "חדש"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "New BoM from %(mo_name)s"
msgstr "עץ מוצר חדש מ%(mo_name)s"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Next Activity"
msgstr "הפעילות הבאה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_calendar_event_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_calendar_event_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "הפעילות הבאה ביומן"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_summary
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_summary
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "No Backorder"
msgstr "ללא הזמנה חוזרת"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid "No bill of materials found. Let's create one!"
msgstr "לא נמצא עץ מוצר. בוא ניצור אחד!"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "No data available."
msgstr "אין נתונים זמינים."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workcenter_load_report_graph
msgid "No data yet!"
msgstr "אין מידע עדיין"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "No manufacturing order found. Let's create one."
msgstr "לא נמצאה הוראת ייצור. בואו ניצור אחת."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid "No product found. Let's create one!"
msgstr "לא נמצאו מוצרים. בואו ניצור אחד!"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_blocked
msgid "No productivity loss for this equipment"
msgstr "אין הפסד יצרנות עבור ציוד זה"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid "No unbuild order found"
msgstr "לא נמצאו הוראות פירוק"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid "No work orders to do!"
msgstr "אין הוראות עבודה לביצוע!"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid ""
"No workorder currently in progress. Click to mark work center as blocked."
msgstr "אין כרגע הוראת עבודה בתהליך. לחץ כדי לסמן את תחנת העבודה כחסומה."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__normal
msgid "Normal"
msgstr "נורמלי "

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__unavailable
msgid "Not Available"
msgstr "לא זמין"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Not Ready"
msgstr "לא מוכן"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Note that another version of this BOM is available."
msgstr "שימו לב שגרסה נוספת של עץ מוצר זה זמינה ."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid ""
"Note that archived work center(s): '%s' is/are still linked to active Bill "
"of Materials, which means that operations can still be planned on it/them. "
"To prevent this, deletion of the work center is recommended instead."
msgstr ""
"שים לב שתחנת\\ות עבודה בארכיון: '%s' עדיין מקושר/ים לעצי מוצרים פעילים, "
"כלומר עדיין ניתן לתכנן עליו/עליהם הוראות עבודה.כדי למנוע זאת, מומלץ למחוק את"
" תחנת העבודה במקום לארכב אותו."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/product.py:0
msgid ""
"Note that product(s): '%s' is/are still linked to active Bill of Materials, "
"which means that the product can still be used on it/them."
msgstr ""
"שים לב שהמוצר/ים: '%s' עדיין מקושר/ים לעצץ מוצר פעיל, כלומר ניתן עדיין "
"להשתמש במוצר/ים זה/אלו בהן."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_in_progress
msgid "Number of Manufacturing Orders In Progress"
msgstr "מספר הוראות ייצור בתהליך"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_late
msgid "Number of Manufacturing Orders Late"
msgstr "מספר הוראות ייצור באיחור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_to_close
msgid "Number of Manufacturing Orders To Close"
msgstr "מספר הוראות ייצור לסגירה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_waiting
msgid "Number of Manufacturing Orders Waiting"
msgstr "מספר הוראות ייצור ממתינות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_todo
msgid "Number of Manufacturing Orders to Process"
msgstr "מספר הוראות ייצור לביצוע"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lot_qty
msgid "Number of SN"
msgstr "כמות סיריאלים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unbuild_count
msgid "Number of Unbuilds"
msgstr "מספר של פירוקים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_child_count
msgid "Number of generated MO"
msgstr "מספר הוראות ייצור שנוצרו"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "מספר הודעות הדורשות פעולה"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__capacity
msgid "Number of pieces that can be produced in parallel for this product."
msgstr "מספר היחידות שניתן לייצר במקביל עבור מוצר זה."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_source_count
msgid "Number of source MO"
msgstr "מספר מקור של הוראת ייצור "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "OEE"
msgstr "OEE"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee_target
msgid "OEE Target"
msgstr "יעד OEE"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid ""
"Odoo opens a PDF preview by default. If you want to print instantly,\n"
"                                install the IoT App on a computer that is on the same local network as the\n"
"                                barcode operator and configure the routing of the reports."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee
msgid "Oee"
msgstr "OEE"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "On Hand"
msgstr "במלאי"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"Only manufacturing orders in either a draft or confirmed state can be %s."
msgstr "רק הוראות ייצור במצב טיוטה או מאושרת יכולות להיות %s."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Only manufacturing orders with a Bill of Materials can be %s."
msgstr "רק הזמנות ייצור עם עץ מוצר יכולות להיות%s."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Open"
msgstr "פתח"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view_mo_form
msgid "Open Work Order"
msgstr "הוראת עבודה פתוחה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Operation"
msgstr "פעולה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__allow_operation_dependencies
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__allow_operation_dependencies
msgid "Operation Dependencies"
msgstr "תלות בין פעולות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__operation_id
msgid "Operation To Consume"
msgstr "פעולה נדרשת"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__picking_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_type_id
msgid "Operation Type"
msgstr "סוג פעולה  "

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid ""
"Operation define that need to be done to realize a Work Order.\n"
"                Each operation is done at a specific Work Center and has a specific duration."
msgstr ""
"הפעולה מגדירה את מה שצריך לבצע על מנת לממש פקודת עבודה.\n"
"                    כל פעולה מתבצעת בתחנת עבודה מסוימת ולה משך זמן מוגדר."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/product.py:0
msgid "Operation not supported"
msgstr "הפעולה אינה נתמכת"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_operations_block/mrp_mo_overview_operations_block.xml:0
#: model:ir.actions.act_window,name:mrp.mrp_routing_action
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__allowed_operation_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_manufacturing
#: model:ir.ui.menu,name:mrp.menu_mrp_routing_action
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_operations
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_calendar
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Operations"
msgstr "פעולות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Done"
msgstr "פעולות שבוצעו"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Planned"
msgstr "פעולות מתוכננות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Operations Search Filters"
msgstr "מסנני חיפוש פעולות"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__needed_by_operation_ids
msgid "Operations that cannot start before this operation is completed."
msgstr "פעולות שלא ניתן להתחיל בהן לפני שסיום פעולה זו הושלם."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__blocked_by_operation_ids
msgid "Operations that need to be completed before this operation can start."
msgstr "פעולות שיש להשלים לפני שניתן להתחיל בפעולה זו."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__orderpoint_id
msgid "Orderpoint"
msgstr "נקודת הזמנה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__order_ids
msgid "Orders"
msgstr "הזמנות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_production
msgid "Original Production Quantity"
msgstr "כמות ייצור מקורית"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_outdated_bom
msgid "Outdated BoM"
msgstr "עץ מוצר מיושן"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee_target
msgid "Overall Effective Efficiency Target in percentage"
msgstr "יעד יעילות כוללת אפקטיבית באחוזים"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_oee
#: model:ir.ui.menu,name:mrp.menu_mrp_workcenter_productivity_report
msgid "Overall Equipment Effectiveness"
msgstr "יעילות ציוד כוללת (OEE)"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee
msgid "Overall Equipment Effectiveness, based on the last month"
msgstr "יעילות ציוד כוללת, על בסיס החודש האחרון"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_oee
msgid "Overall Equipment Effectiveness: no working or blocked time"
msgstr "יעילות ציוד כוללת: אין זמן עבודה או חסומה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__pdf
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__done_mrp_lot_label_to_print__pdf
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__generated_mrp_lot_label_to_print__pdf
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__mrp_product_label_to_print__pdf
msgid "PDF"
msgstr "PDF"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Package barcode"
msgstr "ברקוד חבילה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_id
msgid "Parent BoM"
msgstr "עץ מוצר אב"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__parent_product_tmpl_id
msgid "Parent Product Template"
msgstr "תבנית מוצר אב"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_consumption_warning_id
msgid "Parent Wizard"
msgstr "אשף אב"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields,help:mrp.field_mrp_workorder__worksheet_google_slide
msgid ""
"Paste the url of your Google Slide. Make sure the access to the document is "
"public."
msgstr "הדבק את כתובת ה Google Slide שלך. וודא שהגישה למסמך היא ציבורית."

#. module: mrp
#: model:ir.actions.server,name:mrp.action_pause_workorders
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Pause"
msgstr "השהה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Pending"
msgstr "ממתין "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__performance
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__performance
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Performance"
msgstr "שיעורי רווחיות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Performance Losses"
msgstr "הפסדי ביצועים"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__performance
msgid "Performance over the last month"
msgstr "ביצועים בחודש האחרון"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick Components"
msgstr "לקט רכיבים"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick components and then manufacture"
msgstr "לקט רכיבים וייצר"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm
msgid "Pick components then manufacture (2 steps)"
msgstr "לקט רכיבים וייצר (2 שלבים)"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick components, manufacture and then store products (3 steps)"
msgstr "לקט רכיבים,ייצר ואז אחסן מוצרים (3 שלבים)"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm_sam
msgid "Pick components, manufacture, then store products (3 steps)"
msgstr "לקט רכיבים,ייצר ואז אחסן מוצרים (3 שלבים)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_mto_pull_id
msgid "Picking Before Manufacturing MTO Rule"
msgstr "לקט לפני הייצור לפי כלל MTO"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_type_id
msgid "Picking Before Manufacturing Operation Type"
msgstr "ליקוט לפני סוג פעולת ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_route_id
msgid "Picking Before Manufacturing Route"
msgstr "ליקוט לפני מסלולי הייצור "

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking_type
msgid "Picking Type"
msgstr "סוג ליקוט"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_ids
msgid "Picking associated to this manufacturing order"
msgstr "ליקוט הקשור להוראת ייצור זו  "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_loc_id
msgid "Picking before Manufacturing Location"
msgstr "ליקוט לפני מיקום ייצור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Pieces"
msgstr "חלקים"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Plan"
msgstr "תוכנית"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Plan Orders"
msgstr "תכנון הזמנות "

#. module: mrp
#: model:ir.actions.server,name:mrp.action_plan_with_components_availability
msgid "Plan based on Components Availability"
msgstr "תכנון מבוסס על זמינות רכיבים"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Plan manufacturing or purchase orders based on forecasts"
msgstr "תכנן הוראות ייצור או הזמנות רכש על בסיס תחזיות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planned"
msgstr "מתוכנן"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Planned at the same time as other workorder(s) at %s"
msgstr "מתוכנן במקביל להזמנה(ות) עבודה אחרות ב%s"

#. module: mrp
#: model:ir.ui.menu,name:mrp.mrp_planning_menu_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Planning"
msgstr "תכנון"

#. module: mrp
#: model:product.template,name:mrp.product_product_plastic_laminate_product_template
msgid "Plastic Laminate"
msgstr "למינציה מפלסטיק"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_move.py:0
msgid "Please enter a positive quantity."
msgstr "הזן כמות חיובית."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Please set the first Serial Number or a default sequence"
msgstr "אנא הגדר את המספר הסידורי הראשון או רצף ברירת מחדל"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid "Please specify the first serial number you would like to use."
msgstr "אנא ציין את המספר הסידורי הראשון שתרצה להשתמש."

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid "Please specify the serial number you would like to use."
msgstr "אנא ציין את המספר הסידורי שתרצה להשתמש ."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Please unblock the work center to start the work order."
msgstr "אנא בטל את חסימת תחנת העבודה כדי להתחיל לעבוד על ההוראת העבודה."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Please unblock the work center to validate the work order"
msgstr "אנא בטל את חסימת תחנת העבודה על מנת לאשר הוראת עבודה"

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_ply_product_template
msgid "Ply Layer"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_ply_veneer_product_template
msgid "Ply Veneer"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__json_popover
msgid "Popover Data JSON"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__possible_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__possible_bom_product_template_attribute_value_ids
msgid "Possible Product Template Attribute Value"
msgstr "ערך תכונה אפשרי של תבנית מוצר"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Post-Production"
msgstr "אחרי ייצור"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pre-Production"
msgstr "לפני ייצור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Prepare MO"
msgstr "הכן הוראת ייצור"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Print"
msgstr "הדפס"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
msgid "Print All Variants"
msgstr "הדפס וריאנטים של מוצר"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_print_labels
msgid "Print Labels"
msgstr "הדפס תוויות"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.xml:0
msgid "Print Work Order"
msgstr "הדפס הוראת עבודה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Print labels as:"
msgstr "הדפס תוויות כ:"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Print when \"Create new Lot/SN\""
msgstr "הדפס כאשר מצוין \" צור מספר אצווה\\ סידורי חדש\""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Print when done"
msgstr "הדפס כאשר מוכן"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__priority
msgid "Priority"
msgstr "קְדִימוּת"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason5
msgid "Process Defect"
msgstr "פגם בתהליך"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Process manufacturing orders from the barcode application"
msgstr "בצע הוראות ייצור דרך אפלקציית הברקוד"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Process operations at specific work centers"
msgstr "תהליך פעולות בתחנות עבודה ספציפיים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__produce_line_ids
msgid "Processed Disassembly Lines"
msgstr "שורות פירוק מעובדות"

#. module: mrp
#: model:ir.model,name:mrp.model_procurement_group
#: model:ir.model.fields,field_description:mrp.field_mrp_production__procurement_group_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Procurement Group"
msgstr "קבוצת רכש"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_procurement_compute_mrp
msgid "Procurement: run scheduler"
msgstr "רכש: הפעל מתזמן"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Produce"
msgstr "ייצר"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Produce All"
msgstr "בצע ייצור"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_batch_produce
msgid "Produce a batch of production order"
msgstr "ייצר אצווה מהוראת ייצור "

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_byproducts
msgid "Produce residual products"
msgstr "ייצר מוצרי לוואי"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Produce residual products (A + B -> C + D)"
msgstr "ייצר מוצרי לוואי (א + ב -> ג + ד)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "Produced"
msgstr "מיוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__operation_id
msgid "Produced in Operation"
msgstr "ייצור בפעולה"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model,name:mrp.model_product_template
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Product"
msgstr "מוצר"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Product Attachments"
msgstr "קבצים מצורפים למוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__capacity_ids
msgid "Product Capacities"
msgstr "קיבולות מוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__product_catalog_product_is_in_bom
msgid "Product Catalog Product Is In Bom"
msgstr "קטלוג מוצרים מוצר נמצא בעץ מוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__product_catalog_product_is_in_mo
msgid "Product Catalog Product Is In Mo"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Product Cost"
msgstr "עלות המוצר"

#. module: mrp
#: model:ir.model,name:mrp.model_product_document
msgid "Product Document"
msgstr "מסמך מוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_virtual_available
msgid "Product Forecasted Quantity"
msgstr "תחזית כמות מוצר "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__mrp_product_label_to_print
msgid "Product Label to Print"
msgstr "תווית מוצר להדפסה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Product Labels"
msgstr "תוויות מוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_plm
msgid "Product Lifecycle Management (PLM)"
msgstr "ניהול מחזור חיי מוצר (PLM)"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "תנועות המוצר (תנועת שורת מלאי)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_qty_available
msgid "Product On Hand Quantity"
msgstr "מוצרים זמינים במלאי"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Product Quantity"
msgstr "כמות מוצרים"

#. module: mrp
#: model:ir.model,name:mrp.model_product_replenish
msgid "Product Replenish"
msgstr "רענון מלאי מוצר"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "Product Replenish Mixin"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__product_template
msgid "Product Template"
msgstr "תבנית מוצר "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_uom_id
msgid "Product Unit of Measure"
msgstr "יחידת מידה של מוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__product_uom_id
msgid "Product UoM"
msgstr "יחידת מידה של מוצר"

#. module: mrp
#: model:ir.model,name:mrp.model_product_product
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_id
msgid "Product Variant"
msgstr "וריאנט מוצר"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_product_variant_action
#: model:ir.ui.menu,name:mrp.product_variant_mrp
msgid "Product Variants"
msgstr "וריאנטים של מוצר"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_capacity_unique_product
msgid "Product capacity should be unique for each workcenter."
msgstr "קיבולת מוצר צריכה להיות ייחודית לכל תחנת עבודה ."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Product to build..."
msgstr "מוצר לבנייה..."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__production_id
#: model:ir.model.fields,field_description:mrp.field_picking_label_type__production_ids
#: model:ir.model.fields,field_description:mrp.field_stock_picking__production_ids
msgid "Production"
msgstr "ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_capacity
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_capacity
msgid "Production Capacity"
msgstr "קיבולת ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_date
msgid "Production Date"
msgstr "תאריך ייצור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Production Information"
msgstr "מידע ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_location_id
msgid "Production Location"
msgstr "מיקום הייצור"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_production_order
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__production_id
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Production Order"
msgstr "הוראת ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__raw_material_production_id
msgid "Production Order for components"
msgstr "הוראות ייצור לרכיבים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__production_id
msgid "Production Order for finished products"
msgstr "הוראת ייצור למוצרים מוגמרים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_state
msgid "Production State"
msgstr "שלב ייצור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Production Workcenter"
msgstr "תחנת עבודה לייצור"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
msgid "Production of Draft MO"
msgstr "הפקה של הוראת ייצור מסוג טיוטה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Production started late"
msgstr "ייצור שהתחיל באיחור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__production_ids
msgid "Productions To Split"
msgstr "ייצור לפיצול"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__productive
msgid "Productive"
msgstr "פרודוקטיבי"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__productive_time
msgid "Productive Time"
msgstr "זמן פרודוקטיבי"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__productive_time
msgid "Productive hours over the last month"
msgstr "שעות פרודוקטיביות במהלך החודש האחרון"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Productivity"
msgstr "יצרנות"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_blocked
msgid "Productivity Losses"
msgstr "הפסדי יצרנות"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_template_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom
#: model:ir.ui.menu,name:mrp.menu_mrp_product_form
msgid "Products"
msgstr "מוצרים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__progress
msgid "Progress Done (%)"
msgstr "התקדמות בוצעה (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__propagate_cancel
msgid "Propagate cancel and split"
msgstr "הפץ ביטול ופיצול"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__quality
msgid "Quality"
msgstr "איכות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Quality Losses"
msgstr "הפסדי איכות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "גליונות איכות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quant_ids
msgid "Quant"
msgstr "מנה"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_produced
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quantity
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Quantity"
msgstr "כמות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_produced
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view_mo_form
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Quantity Produced"
msgstr "כמות מיוצרת"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_producing
msgid "Quantity Producing"
msgstr "כמות מיוצרת"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Quantity Remaining"
msgstr "כמות שנותרה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_remaining
msgid "Quantity To Be Produced"
msgstr "כמות לייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__should_consume_qty
msgid "Quantity To Consume"
msgstr "כמות נדרשת"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__quantity
msgid "Quantity To Produce"
msgstr "כמות לייצור"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_capacity
#: model:ir.model.fields,help:mrp.field_mrp_production_split__production_capacity
msgid "Quantity that can be produced with the current stock of components"
msgstr "כמות שניתן לייצר עם המלאי הנוכחי של רכיבים"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_quant
msgid "Quants"
msgstr "אנליסטים כמותיים"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "RAM"
msgstr "RAM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__rating_ids
msgid "Ratings"
msgstr "דירוגים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_raw_ids
msgid "Raw Moves"
msgstr "תנועות גולמיות"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__assigned
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__ready
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Ready"
msgstr "מוכן"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Ready to Produce"
msgstr "מוכן לייצור"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Real Cost"
msgstr "עלות אמיתית"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Real Costs"
msgstr "עלויות אמיתיות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__duration
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration
msgid "Real Duration"
msgstr "משך הזמן בפועל"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Receipt"
msgstr "קבלה"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Receipts"
msgstr "קבלות"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Reception time estimation."
msgstr "הערכת זמן קליטה."

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason4
msgid "Reduced Speed"
msgstr "מהירות מופחתת "

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason6
msgid "Reduced Yield"
msgstr "תשואה מופחתת"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__code
#: model:ir.model.fields,field_description:mrp.field_mrp_production__name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__name
msgid "Reference"
msgstr "מזהה"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_name_uniq
msgid "Reference must be unique per Company!"
msgstr "מזהה חייב להיות ייחודי לכל חברה!"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__origin
msgid ""
"Reference of the document that generated this production order request."
msgstr "מזהה למסמך שייצר דרישה להוראת ייצור זו."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Reference:"
msgstr "מזהה:"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_remaining_days_unformatted_field.js:0
msgid "Remaining Days"
msgstr "ימים שנשארו"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_workorder_popover.xml:0
msgid "Replan"
msgstr "תכנן מחדש"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.xml:0
msgid "Replenish"
msgstr "חדש מלאי"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Replenish on Order (MTO)"
msgstr "חדש מלאי בהזמנה (הכן להזמנה)"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Replenishments"
msgstr "חידושי מלאי"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_reporting
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Reporting"
msgstr "דו\"חות"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Reserved"
msgstr "שמור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_id
msgid "Resource"
msgstr "עובד"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__user_id
msgid "Responsible"
msgstr "אחראי"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Resupply lead time."
msgstr "זמן הובלה לאספקה מחדש"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Route"
msgstr "מסלול"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Routing"
msgstr "ניתוב"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__routing_line_ids
msgid "Routing Lines"
msgstr "שורות ניתוב"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
msgid "Routing Work Centers"
msgstr "ניתוב תחנות עבודה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_has_sms_error
msgid "SMS Delivery error"
msgstr "שגיאה בשליחת SMS"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__date
msgid "Schedule Date"
msgstr "זמן מתוכנן"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Schedule manufacturing orders earlier to avoid delays"
msgstr "תזמן הוראות ייצור מוקדם יותר כדי למנוע עיכובים"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Scheduled Date"
msgstr "תאריך מתוזמן"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Scheduled End"
msgstr "סיום מתוכנן"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"Scheduled before the previous work order, planned from %(start)s to %(end)s"
msgstr "מתוזמן לפני הוראת העבודה הקודמת, מתוכנן מ-%(start)s עד%(end)s"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_workorder_popover.xml:0
msgid "Scheduling Information"
msgstr "מידע תכנון"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_scrap
#: model:ir.model,name:mrp.model_stock_scrap
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_scrap
msgid "Scrap"
msgstr "פסול"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_count
msgid "Scrap Move"
msgstr "תנועת פסילה"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Scrap Products"
msgstr "מוצרים פסולים"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_ids
msgid "Scraps"
msgstr "פסולים"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_screw_product_template
msgid "Screw"
msgstr "בורג"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Search"
msgstr "חיפוש"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Search Bill Of Material"
msgstr "חפש עץ מוצר"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Search Production"
msgstr "חפש ייצור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Search Work Orders"
msgstr "חפש הוראות עבודה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Search for mrp workcenter"
msgstr "חפש תחנת עבודה mrp"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Security Lead Time"
msgstr "זמן ביטחון לאספקה"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,help:mrp.field_res_config_settings__manufacturing_lead
msgid "Security days for each manufacturing operation."
msgstr "ימי ביטחון עבור כל פעולת ייצור."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
msgid "Select Operations to Copy"
msgstr "בחר פעולה להעתקה"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Selection not supported."
msgstr "הבחירה לא נתמכת."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__sequence
msgid "Sequence"
msgstr "רצף"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Set Quantities & Validate"
msgstr "הגדר כמויות ואשר"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Set Quantity"
msgstr "הגדר כמות"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__manual
msgid "Set duration manually"
msgstr "הגדר משך זמן ידנית"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Set the order that work orders should be processed in. Activate the feature "
"within each BoM's Miscellaneous tab"
msgstr ""
"קבע את הסדר של הוראות עבודה בעצי מוצר. הפעל הגדרה זו בכל עץ מוצר תחת חלון "
"\"שונות\""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_configuration
#: model:ir.ui.menu,name:mrp.menu_mrp_config
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Settings"
msgstr "הגדרות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_start
msgid "Setup Time"
msgstr "זמן הגדרה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__time_start
msgid "Setup Time (minutes)"
msgstr "זמן הכנה (דקות)"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason2
msgid "Setup and Adjustments"
msgstr "הכנה והתאמות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_allocation
msgid "Show Allocation"
msgstr "הצג הקצאה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__show_bom
msgid "Show BoM column"
msgstr "הצג עמודת עץ מוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_replenish__show_bom
#: model:ir.model.fields,field_description:mrp.field_stock_replenish_mixin__show_bom
msgid "Show Bom"
msgstr "הצג עץ מוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_final_lots
msgid "Show Final Lots"
msgstr "הצג אצוות סופיות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lock
msgid "Show Lock/unlock buttons"
msgstr "הצג כפתורי נעילה\\ שחרור מנעילה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__show_json_popover
msgid "Show Popover?"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_produce
msgid "Show Produce"
msgstr "הצג ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_produce_all
msgid "Show Produce All"
msgstr "הצג בצע ייצור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Show all records which has next action date is before today"
msgstr "הצג את כל הרשומות שתאריך הפעולה הבא שלהן הוא עד היום"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__show_backorder_lines
msgid "Show backorder lines"
msgstr "הצג שורות הזמנות חוזרות"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__leave_id
msgid "Slot into workcenter calendar once planned"
msgstr "הכנס ללוח הזמנים של תחנת העבודה ברגע שמתוכנן"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_head_product_template
msgid "Solid wood is a durable natural material."
msgstr "עץ מלא הוא חומר טבעי עמיד."

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_product_template
msgid "Solid wood table."
msgstr "שולחן מעץ מלא."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid ""
"Some of your byproducts are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct byproducts."
msgstr ""
"חלק מהתוצרי לוואי שלך מנוטרים, עליך לציין פקודת ייצור כדי להוציא את תוצרי "
"הלוואי הנכונים."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid ""
"Some of your components are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct components."
msgstr ""
"חלק מהרכיבים שלך נמצאים במעקב, אתה צריך לציין הוראת ייצור כדי לאחזר את "
"הרכיבים הנכונים."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"Some work orders are already done, so you cannot unplan this manufacturing order.\n"
"\n"
"It’d be a shame to waste all that progress, right?"
msgstr ""
"כמה פקודות עבודה כבר הושלמו, ולכן אינך יכול לבטל את תכנון הוראת הייצור הזו.\n"
"\n"
"יהיה חבל לבזבז את כל ההתקדמות הזאת, נכון?"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"Some work orders have already started, so you cannot unplan this manufacturing order.\n"
"\n"
"It’d be a shame to waste all that progress, right?"
msgstr ""
"חלק מההוראות עבודה כבר בתהליך, לכן אינך יכול לבטל את תכנון הוראת הייצור הזו.\n"
"\n"
"יהיה חבל לבזבז את כל ההתקדמות הזו, נכון?"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__origin
msgid "Source"
msgstr "מקור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_id
msgid "Source Location"
msgstr "מיקום מוצא"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Specific Capacities"
msgstr "תכולות ספציפיות"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__capacity_ids
msgid ""
"Specific number of pieces that can be produced in parallel per product."
msgstr "מספר ספציפי של יחידות שניתן לייצר במקביל לכל מוצר."

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_split
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
msgid "Split"
msgstr "פצל"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__counter
msgid "Split #"
msgstr "פצל #"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_detailed_vals_ids
msgid "Split Details"
msgstr "פרטי פיצול"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__mrp_production_split_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Split Production"
msgstr "פצל ייצור"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split_line
msgid "Split Production Detail"
msgstr "פרטי פיצול ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_split_multi_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Split Productions"
msgstr "פיצול ייצור"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_split
msgid "Split production"
msgstr "פצל ייצור"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_split_multi
msgid "Split productions"
msgstr "פצל ייצור"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_screw_product_template
msgid "Stainless steel screw"
msgstr "בורג נירוסטה"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_bolt_product_template
msgid "Stainless steel screw full (dia - 5mm, Length - 10mm)"
msgstr "בורג נירוסטה מלא (קוטר - 5 מ\"מ, אורך - 10 מ\"מ)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Starred"
msgstr "מסומן בכוכב"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_start_workorders
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_start
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Start"
msgstr "התחל"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_start
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Start Date"
msgstr "תאריך תחילה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__state
msgid "State"
msgstr "סטטוס"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__state
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Status"
msgstr "סטטוס"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_state
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_type_id
msgid "Stock After Manufacturing Operation Type"
msgstr "מלאי לאחר סוג פעילות ייצור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_rule_id
msgid "Stock After Manufacturing Rule"
msgstr "מלאי לאחר כלל ייצור "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_availability
msgid "Stock Availability"
msgstr "מלאי זמין"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move
msgid "Stock Move"
msgstr "תנועת מלאי"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_dest_ids
msgid "Stock Movements of Produced Goods"
msgstr "תנועות מלאי של מוצרים מיוצרים"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_unbuild_moves
msgid "Stock Moves"
msgstr "תנועות מלאי"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "דו\"ח קבלת מלאי"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "דוח חידוש מלאי"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_rule
msgid "Stock Rule"
msgstr "כלל מלאי "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_loc_id
msgid "Stock after Manufacturing Location"
msgstr "מלאי לאחר מיקום הייצור"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "דוח כללי מלאי"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Store Finished Product"
msgstr "מחסן תוצ\"ג"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_bom_id
msgid "Sub BoM"
msgstr "תת עץ מוצר"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_subcontracting
msgid "Subcontracting"
msgstr "מיקור חוץ"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_product_template
msgid "Table"
msgstr "טבלה"

#. module: mrp
#: model:product.template,name:mrp.product_product_table_kit_product_template
msgid "Table Kit"
msgstr "ערכת שולחן"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_leg_product_template
msgid "Table Leg"
msgstr "רגל שולחן"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_head_product_template
msgid "Table Top"
msgstr "משטח שולחן עליון"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_table_kit_product_template
msgid "Table kit"
msgstr "ערכת שולחן"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tag_ids
msgid "Tag"
msgstr "תגית"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__name
msgid "Tag Name"
msgstr "שם תגית"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr "שדה טכני המשמש להחלטה אם יש להציג את הכפתור \"הקצאה\"."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__has_routing_lines
msgid "Technical field for workcenter views"
msgstr "שדה טכני לתצוגת תחנת עבודה"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_produce_all
msgid "Technical field to check if produce all button can be shown"
msgstr "שדה טכני לבדוק אם ניתן להציג את כפתור \"ייצר הכל\"."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_produce
msgid "Technical field to check if produce button can be shown"
msgstr "שדה טכני לבדיקה אם ניתן להציג את כפתור הייצור."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reserve_visible
msgid "Technical field to check when we can reserve quantities"
msgstr "שדה טכני לבדיקה האם ניתן לשמור כמויות"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr "שדה טכני כדי לבדוק מתי אנחנו יכולים לבטל שמירה"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__text
msgid "Text"
msgstr "טקסט"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__is_outdated_bom
msgid "The BoM has been updated since creation of the MO"
msgstr "עץ המוצר עודכן מאז יצירת הוראת הייצור ."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The Product Unit of Measure you chose has a different category than in the "
"product form."
msgstr "יחידת המידה שבחרת היא בקטגוריה שונה מזו שמופיעה במוצר עצמו."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "The Workorder (%s) cannot be started twice!"
msgstr " לא ניתן להתחיל פעמיים הוראת עבודה (%s) !"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The attribute value %(attribute)s set on product %(product)s does not match "
"the BoM product %(bom_product)s."
msgstr ""
"ערך המאפיין %(attribute)sשהוגדר על המוצר %(product)sאינו תואם למוצר בעץ מוצר"
" %(bom_product)s."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "The capacity must be strictly positive."
msgstr "הקיבולת חייבת להיות חיובית לחלוטין."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "The component %s should not be the same as the product to produce."
msgstr "הרכיב %s לא צריך להיות זהה למוצר לייצור."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The current configuration is incorrect because it would create a cycle "
"between these products: %s."
msgstr "התצורה הנוכחית שגויה מכיוון שהיא תיצור מחזוריות בין המוצרים הבאים:%s."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__day_2
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "The day after tomorrow"
msgstr "מחרתיים"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_orderpoint.py:0
msgid "The following replenishment order has been generated"
msgstr "הוראת החידוש מלאי הבאה נוצרה"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_produced
msgid "The number of products already handled by this work order"
msgstr "מספר המוצרים שכבר טופלו בהוראת עבודה זו"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__operation_id
msgid ""
"The operation where the components are consumed, or the finished products "
"created."
msgstr "הפעולה בה השתמשו ברכיבים או שנוצרו בה המוצרים המוגמרים."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__cost_share
msgid ""
"The percentage of the final production cost for this by-product line "
"(divided between the quantity produced).The total of all by-products' cost "
"share must be less than or equal to 100."
msgstr ""
"אחוז מעלות הייצור הסופית עבור שורת תוצר הלוואי הזו (מחולק בין הכמות "
"המיוצרת). סך כל חלקי עלות התוצרי לוואי חייב להיות קטן או שווה ל-100."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__cost_share
msgid ""
"The percentage of the final production cost for this by-product. The total "
"of all by-products' cost share must be smaller or equal to 100."
msgstr ""
"אחוז מעלות הייצור הסופית עבור תוצר הלוואי הזה. סך כל חלקי עלות התוצרי לוואי "
"חייב להיות קטן או שווה ל-100."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"The planned end date of the work order cannot be prior to the planned start "
"date, please correct this to save the work order."
msgstr ""
"תאריך הסיום המתוכנן של הוראת העבודה לא יכול להיות לפני תאריך ההתחלה המתוכנן,"
" אנא תקן זאת כדי לשמור את הוראת העבודה."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The product has already been used at least once, editing its structure may "
"lead to undesirable behaviours. You should rather archive the product and "
"create a new one with a new bill of materials."
msgstr ""
"המוצר כבר שימש לפחות פעם אחת, עריכת המבנה שלו עשויה להוביל להתנהגויות לא "
"רצויות. עדיף שתשמור את המוצר בארכיון ותיצור מוצר חדש עם עץ מוצר חדש."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_reported_from_previous_wo
msgid ""
"The quantity already produced awaiting allocation in the backorders chain."
msgstr "הכמות שכבר יוצרה וממתינה להקצאה בשרשרת ההזמנות החוזרות."

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_qty_positive
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_qty_positive
msgid "The quantity to produce must be positive!"
msgstr "הכמות לייצור חייב להיות חיובית!"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_unbuild_qty_positive
msgid "The quantity to unbuild must be positive!"
msgstr "הכמות שיש לפרק חייבת להיות חיובית!"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"The serial number %(number)s used for byproduct %(product_name)s has already"
" been produced"
msgstr ""
"מספר הסידורי %(number)sשנעשה בו שימוש עבור תוצר הלוואי %(product_name)sכבר "
"יוצר."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"The serial number %(number)s used for component %(component)s has already "
"been consumed"
msgstr ""
"המספר הסידורי %(number)s שנעשה בו שימוש עבור הרכיב %(component)sכבר נצרך."

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_tag_tag_name_unique
msgid "The tag name must be unique."
msgstr "שם התווית חייב להיות ייחודי."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "The total cost share for a BoM's by-products cannot exceed 100."
msgstr "סך חלקי העלות של תוצרי הלוואי בעץ מוצר לא יכול לעלות על 100%."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"The total cost share for a manufacturing order's by-products cannot exceed "
"100."
msgstr ""
"ההשתתפות הכוללת בעלות של תוצרי לוואי בהזמנת ייצור לא יכולה לעלות על 100."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
msgid "The total should be equal to the quantity to produce."
msgstr "הסכום צריך להיות שווה ל כמות לייצור. "

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "The work order should have already been processed."
msgstr "הוראת עבודה זו הייתה צריכה להיות מעובדת כבר."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"There are no components to consume. Are you still sure you want to continue?"
msgstr "אין רכיבים לצרוך. האם אתה בטוח שברצונך להמשיך?"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
msgid "There is no defined calendar on workcenter %s."
msgstr "אין לוח שנה שמוגדר לתחנת העבודה %s."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid "There's no product move yet"
msgstr "אין עדיין תנועת מוצר"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "This Week"
msgstr "שבוע נוכחי"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr "שדה זה משמש כדי להגדיר באיזה אזור זמן המשאבים יעבדו,"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""
"שדה זה משמש לחישוב משך הזמן הצפוי של הוראת עבודה בתחנת עבודה זו. לדוגמה, אם "
"הוראת העבודה אורכת שעה אחת ופקטור היעילות הוא 100%, משך הזמן הצפוי יהיה שעה "
"אחת. עם זאת, אם פקטור היעילות הוא 200%, משך הזמן הצפוי יהיה 30 דקות."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
msgid "This is a BoM of type Kit!"
msgstr "זהו עץ מוצר מסוג ערכה!"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid ""
"This is the cost based on the BoM of the product. It is computed by summing "
"the costs of the components and operations needed to build the product."
msgstr ""
"זוהי העלות המבוססת על ה- עץ המוצר. זה מחושב על ידי סיכום העלויות של הרכיבים "
"והפעולות הדרושים כדי לבנות את המוצר."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "This is the cost defined on the product."
msgstr "זוהי העלות המוגדרת במוצר."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                You can filter on the product to see all the past movements for the product."
msgstr ""
"תפריט זה מספק מעקב מלא אחר פעולות המלאי של מוצר מסוים.\n"
"           באפשרותך לסנן לפי המוצר כדי לראות את כל תנועות המלאי שבוצעו עבורו בעבר."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This production has been merge in %s"
msgstr "ייצור זה עבר ייחוד ב- %s"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This production order has been created from Replenishment Report."
msgstr "הזמנת ייצור זו נוצרה מדוח עיתוד מלאי."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This serial number for product %s has already been produced"
msgstr "המספר הסידורי של המוצר %s כבר יוצר."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_qty
msgid ""
"This should be the smallest quantity that this product can be produced in. "
"If the BOM contains operations, make sure the work center capacity is "
"accurate."
msgstr ""
"זו צריכה להיות הכמות הקטנה ביותר שניתן לייצר ממוצר זה. אם העץ מוצר כולל "
"פעולות, ודא שהקיבולת של תחנת העבודה מתאימה."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__time_ids
msgid "Time"
msgstr "זמן"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_efficiency
msgid "Time Efficiency"
msgstr "יעילות זמן"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_ids
msgid "Time Logs"
msgstr "רישומי זמן"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Time Tracking"
msgstr "מעקב זמן"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Time Tracking: %(user)s"
msgstr "מעקב זמן: %(user)s"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__time_stop
msgid "Time in minutes for the cleaning."
msgstr "זמן ניקיון בדקות"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__time_start
msgid "Time in minutes for the setup."
msgstr "זמן סט-אפ בדקות"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid ""
"Time in minutes:- In manual mode, time used- In automatic mode, supposed "
"first time when there aren't any work orders yet"
msgstr ""
"זמן בדקות: -במצב ידני, הזמן שנעשה בו שימוש במצב אוטומטי, הזמן המשוער הראשוני"
" כאשר עדיין אין הוראות עבודה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tz
msgid "Timezone"
msgstr "אזור זמן"

#. module: mrp
#: model:digest.tip,name:mrp.digest_tip_mrp_0
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid "Tip: Use tablets in the shop to control manufacturing"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "To"
msgstr "ל"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__to_backorder
msgid "To Backorder"
msgstr "להזמנה חוזרת "

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__to_close
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Close"
msgstr "לסגור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_expected_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "To Consume"
msgstr "נדרש"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Do"
msgstr " לביצוע"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "To Launch"
msgstr "להתחיל לעבוד"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "To Manufacture"
msgstr "לייצור"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "To Order"
msgstr "להזמין"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "To Produce"
msgstr "לייצר"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__today
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today"
msgstr "היום"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today Activities"
msgstr "פעילויות היום"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__day_1
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Tomorrow"
msgstr "מחר"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_wood_wear_product_template
msgid "Top layer of a wood panel."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Total Cost of Components"
msgstr "עלות רכיבים כוללת"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Total Cost of Operations"
msgstr "עלות תפעול כוללת"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Total Cost of Production"
msgstr "עלות ייצור כוללת"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Total Duration"
msgstr "משך זמן כולל"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_late_count
msgid "Total Late Orders"
msgstr "סה\"כ הוראות באיחור"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_pending_count
msgid "Total Pending Orders"
msgstr "סה\"כ הוראות ממתינות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total Qty"
msgstr "כמות כוללת"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_qty
msgid "Total Quantity"
msgstr "כמות כוללת"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_progress_count
msgid "Total Running Orders"
msgstr "סה\"כ הוראות רצות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Total To Consume"
msgstr "סה\"כ לשימוש"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total expected duration"
msgstr "משך הזמן הכולל הצפוי"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__duration_expected
msgid "Total expected duration (in minutes)"
msgstr "משך הזמן הכולל הצפוי (בדקות)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total real duration"
msgstr "משך זמן אמיתי הכולל"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__duration
msgid "Total real duration (in minutes)"
msgstr "משך זמן אמיתי הכולל (בדקות)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Traceability"
msgstr "מעקב"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_traceability_report
msgid "Traceability Report"
msgstr "דוח מעקב"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_tracking
msgid "Tracking"
msgstr "מעקב"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking
msgid "Transfer"
msgstr "העברה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Transfers"
msgstr "העברות"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "סוג פעולה"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_exception_decoration
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Unable to split with more than the quantity to produce."
msgstr "לא ניתן לפצל עם יותר מהכמות שנדרשה לייצור."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.js:0
msgid "Unblock"
msgstr "בטל חסימה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__unbuild_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild"
msgstr "פרק"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild Order"
msgstr "הוראות פירוק"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_unbuild
#: model:ir.ui.menu,name:mrp.menu_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Unbuild Orders"
msgstr "הוראות פירוק"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Unbuild: %s"
msgstr "פרק: %s"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unbuild_ids
msgid "Unbuilds"
msgstr "לפרק"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Unbuilt"
msgstr "לא מוכן"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.js:0
msgid "Unfold"
msgstr "פרוס"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Unit"
msgstr "יחידה"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Unit Cost"
msgstr "עלות יחידה"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Unit Costs"
msgstr "עלויות יחידה "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unit_factor
msgid "Unit Factor"
msgstr "מקדם יחידה"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_uom_name
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Unit of Measure"
msgstr "יחידת מידה"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_id
msgid ""
"Unit of Measure (Unit of Measure) is the unit of measurement for the "
"inventory control"
msgstr "יחידת מידה (יחידת מידה) היא יחידת המדידה של בקרת המלאי"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Units"
msgstr "יחידה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_unlocked_by_default
msgid "Unlock Manufacturing Orders"
msgstr "בטל נעילה של הוראות ייצור"

#. module: mrp
#: model:res.groups,name:mrp.group_unlocked_by_default
msgid "Unlocked by default"
msgstr "לא נעול כברירת מחדל"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unplan"
msgstr "בטל תכנון"

#. module: mrp
#: model:ir.actions.server,name:mrp.mrp_production_action_unreserve_tree
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unreserve"
msgstr "בטל שמירה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "UoM"
msgstr "יחידת מידה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Update BoM"
msgstr "עדכן עץ מוצר"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Upload your PDF file."
msgstr "העלה את קובץ ה- PDF שלך."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__1
msgid "Urgent"
msgstr "דחוף"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid ""
"Use Manufacturing Orders (MO) to build finished products while consuming "
"components: i.e. 1 Table = 4 Table Legs + 1 Table Top"
msgstr ""
"השתמש בהוראות ייצור (MO) כדי לבנות מוצרים מוגמרים תוך צריכת רכיבים: למשל, "
"שולחן אחד = 4 רגלי שולחן + משטח שולחן אחד"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_workorder_dependencies
msgid "Use Operation Dependencies"
msgstr "השתמש בתלות בין משימות"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_reception_report
msgid "Use Reception Report with Manufacturing Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Used In"
msgstr "משמש ב"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__user_id
#: model:res.groups,name:mrp.group_mrp_user
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "User"
msgstr "משתמש"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Using a MPS report to schedule your reordering and manufacturing operations "
"is useful if you have long lead time and if you produce based on sales "
"forecasts."
msgstr ""
"באמצעות דוח MPS עוזר בקביעת תזמן מחודש וגם בקביעת פעולות הייצור הוא שימושי "
"אם יש לך זמן הספקה ארוך ואם אתה מייצר על בסיס תחזיות מכירות."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__valid_details
msgid "Valid"
msgstr "תקף"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr "שורות תכונת מוצר תקפה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Validate"
msgstr "אשר"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_consumption_warning.py:0
msgid ""
"Values cannot be set and validated because a Lot/Serial Number needs to be "
"specified for a tracked product that is having its consumed amount "
"increased:%(products)s"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
msgid "Variant"
msgstr "וריאנט"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Vendor ABC"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "View WorkOrder"
msgstr "צפה בהוראת עבודה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"View and allocate production quantities to customer orders or other "
"manufacturing orders"
msgstr "הצג והקצה כמויות ייצור להזמנות לקוח או להזמנות ייצור אחרות."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse_orderpoint__manufacturing_visibility_days
msgid "Visibility Days applied on the manufacturing routes."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Waiting"
msgstr "ממתין"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__waiting
msgid "Waiting Another Operation"
msgstr "ממתין לפעולה אחרת"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Waiting Availability"
msgstr "ממתין לזמינות"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__pending
msgid "Waiting for another WO"
msgstr "ממתין להוראת עבודה אחרת"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__waiting
msgid "Waiting for components"
msgstr "ממתין לרכיבים"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Waiting the previous work order, planned from %(start)s to %(end)s"
msgstr "ממתין להוראת העבודה הקודמת, שמתוכננת מ%(start)s ל%(end)s"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: model:ir.model,name:mrp.model_stock_warehouse
#: model:ir.model.fields,field_description:mrp.field_mrp_production__warehouse_id
msgid "Warehouse"
msgstr "מחסן"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warn_insufficient_qty_unbuild
msgid "Warn Insufficient Unbuild Quantity"
msgstr "הזהר כאשר אין כמות מספקת לפירוק"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/stock_scrap.py:0
msgid "Warning"
msgstr "אזהרה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Warnings"
msgstr "אזהרות"

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_wear_product_template
msgid "Wear Layer"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__website_message_ids
msgid "Website Messages"
msgstr "הודעות מאתר האינטרנט"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__website_message_ids
msgid "Website communication history"
msgstr "היסטורית התקשרויות מאתר האינטרנט"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__picking_type_id
msgid ""
"When a procurement has a ‘produce’ route with a operation type set, it will "
"try to create a Manufacturing Order for that product using a BoM of the same"
" operation type. That allows to define stock rules which trigger different "
"manufacturing orders with different BoMs."
msgstr ""
"כאשר קיים רכש נתיב 'ייצור' מאותו קבוצת סוג פעולה, הוא ינסה ליצור הזמנת ייצור"
" עבור מוצר זה באמצעות עץ מוצר מאותו משפחה. זה מאפשר להגדיר כללי מלאי אשר "
"מפעיל הוראות ייצור שונות עם עצי מוצר שונים."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__manual_consumption
#: model:ir.model.fields,help:mrp.field_stock_move__manual_consumption
msgid ""
"When activated, then the registration of consumption for that component is recorded manually exclusively.\n"
"If not activated, and any of the components consumption is edited manually on the manufacturing order, Odoo assumes manual consumption also."
msgstr ""
"כאשר מופעל, הרישום של הצריכה עבור אותו רכיב נרשם באופן ידני באופן בלעדי.\n"
"אם לא מופעל, וכל אחד מצריכת הרכיבים נערכת ידנית בהזמנת הייצור, Odoo מניח גם צריכה ידנית."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__asap
msgid "When components for 1st operation are available"
msgstr "כאשר רכיבים עבור הפעולה הראשונה זמינים"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_to_resupply
msgid ""
"When products are manufactured, they can be manufactured in this warehouse."
msgstr "כאשר המוצרים מיוצרים, הם יכולים להיות מיוצרים במחסן זה."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid ""
"When products are needed in <b>%s</b>, <br/> a manufacturing order is "
"created to fulfill the need."
msgstr ""
"כאשר מוצרים נדרשים ב <b>%s</b>, <br/> נוצרת הזמנת ייצור לצורך מילוי הצורך."

#. module: mrp
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid ""
"With the Odoo work center control panel, your worker can start work orders "
"in the shop and follow instructions of the worksheet. Quality tests are "
"perfectly integrated into the process. Workers can trigger feedback loops, "
"maintenance alerts, scrap products, etc."
msgstr ""
"עם לוח הבקרה של מרכז העבודה של Odoo, העובד שלך יכול להתחיל הוראות עבודה "
"בחנות ולעקוב אחר ההוראות של גיליון העבודה. בדיקות איכות משולבות בצורה מושלמת"
" בתהליך. עובדים יכולים להפעיל לולאות משוב, התראות תחזוקה, מוצרים פסולים "
"וכו'."

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr ""
"אשף במקרה של צריכה תחת אזהרה/חומרה, כאשר נעשה שימוש ביותר רכיבים מהמצופה על "
"פי עץ המוצר בהזמנת ייצור (בהקשר לעץ מוצר)."

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split_multi
msgid "Wizard to Split Multiple Productions"
msgstr "אשף לפיצול ייצור מרובה"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split
msgid "Wizard to Split a Production"
msgstr "אשף לפיצול ייצור"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder
msgid "Wizard to mark as done or create back order"
msgstr "אשף שמסמן כבוצע או יוצר הזמנה חוזרת "

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_panel_product_template
msgid "Wood Panel"
msgstr "לוח עץ"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Work Center"
msgstr "תחנת עבודה"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_capacity
msgid "Work Center Capacity"
msgstr "קיבולת תחנת עבודה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workcenter_load
msgid "Work Center Load"
msgstr "עומס תחנת עבודה"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Work Center Loads"
msgstr "עומסי תחנות עבודה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Work Center Name"
msgstr "שם תחנת העבודה"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "שימוש בתחנת עבודה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
msgid "Work Center load"
msgstr "עומס בתחנת עבודה"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_action
#: model:ir.ui.menu,name:mrp.menu_view_resource_search_mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Centers"
msgstr "תחנות עבודה"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_kanban_action
msgid "Work Centers Overview"
msgstr "סקירה כללית של תחנות עבודה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Work Instruction"
msgstr "הוראות עבודה"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_workorder
#: model:ir.model,name:mrp.model_mrp_workorder
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workorder_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__name
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__workorder_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__workorder_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Work Order"
msgstr "הוראת עבודה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_workorder_dependencies
msgid "Work Order Dependencies"
msgstr "תלות בין הוראות עבודה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Work Order Operations allow you to create and manage the manufacturing "
"operations that should be followed within your work centers in order to "
"produce a product. They are attached to bills of materials that will define "
"the required components."
msgstr ""
"פעולות הוראת עבודה מאפשרות לך ליצור ולנהל את פעולות הייצור שיש לבצע במרכזי "
"העבודה שלך על מנת לייצר מוצר. פעולות אלו קשורות לעצי המוצר אשר מגדירים את "
"הרכיבים הנדרשים."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__workorder_id
msgid "Work Order To Consume"
msgstr "הוראת עבודה לצריכה"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_routing_time
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production_specific
#: model:ir.actions.act_window,name:mrp.action_work_orders
#: model:ir.actions.act_window,name:mrp.mrp_workorder_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_workorder_report
#: model:ir.actions.act_window,name:mrp.mrp_workorder_todo
#: model:ir.model.fields,field_description:mrp.field_mrp_production__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_work_order_report
#: model:ir.ui.menu,name:mrp.menu_mrp_workorder_todo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Orders"
msgstr "הוראות עבודה"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workorder_workcenter_report
msgid "Work Orders Performance"
msgstr "ביצועי הוראות עבודה"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_workcenter
msgid "Work Orders Planning"
msgstr "תכנון הוראות עבודה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Work Sheet"
msgstr "גיליון"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Work center"
msgstr "תחנת עבודה"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"                    Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"הוראות עבודה הן פעולות שיש לבצע כחלק מהזמנת ייצור.\n"
"                      הפעולות מוגדרות בעץ מוצר או מתווספות ישירות בהזמנת הייצור."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"הוראות עבודה הן פעולות שיש לבצע כחלק מהזמנת ייצור.\n"
"                        הפעולות מוגדרות בעץ מוצר או מתווספות ישירות בהזמנת הייצור."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Work orders in progress. Click to block work center."
msgstr "הוראות עבודה בתהליך. לחץ כדי לחסום את תחנת העבודה."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Workcenter"
msgstr "תחנת עבודה"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "Workcenter %s cannot be an alternative of itself."
msgstr "תחנת עבודה %s אינה יכולה להיות חלופה לעצמה."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Workcenter Productivity"
msgstr "יצרנות בתחנת העבודה "

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "יומן יצרנות של תחנת העבודה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Workcenter Productivity Loss"
msgstr "הפסד יצרנות של תחנת עבודה"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss
msgid "Workcenter Productivity Losses"
msgstr "הפסדי יצרנות של תחנת עבודה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__working_state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_state
msgid "Workcenter Status"
msgstr "סטטוס תחנת עבודה "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Workcenter blocked, click to unblock."
msgstr "תחנת עבודה חסומה, לחץ כדי לבטל את החסימה."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_calendar_id
msgid "Working Hours"
msgstr "שעות עבודה "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_user_ids
msgid "Working user on this work order."
msgstr "משתמש עובד בהוראת עבודה זו."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_type
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet
msgid "Worksheet"
msgstr "גיליון עבודה"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_type
msgid "Worksheet Type"
msgstr "סוג גיליון"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_google_slide
msgid "Worksheet URL"
msgstr "כתובת אתר גיליון"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid ""
"Write one line per finished product to produce, with serial numbers as "
"follows:\n"
msgstr "כתוב שורה אחת לכל מוצר גמור לייצור, עם מספרים סידוריים כדלקמן:\n"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__yesterday
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Yesterday"
msgstr "אתמול"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_lot.py:0
msgid ""
"You are not allowed to create or edit a lot or serial number for the "
"components with the operation type \"Manufacturing\". To change this, go on "
"the operation type and tick the box \"Create New Lots/Serial Numbers for "
"Components\"."
msgstr ""
"אינך רשאי ליצור או לערוך מספר אצווה  או מספר סידורי עבור רכיבים עם סוג פעולה"
" \"ייצור\". כדי לשנות זאת, עבור לסוג הפעולה וסמן את התיבה \"צור אצוות/מספרים"
" סידוריים חדשים עבור רכיבים\"."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"You can not create a kit-type bill of materials for products that have at "
"least one reordering rule."
msgstr ""
"לא ניתן ליצור עץ מוצר מסוג ערכה (Kit) עבור מוצרים שיש להם לפחות כלל הזמנה "
"חוזרת אחד."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"You can not delete a Bill of Material with running manufacturing orders.\n"
"Please close or cancel it first."
msgstr ""
"לא ניתן למחוק את עץ המוצר עם הוראות ייצור שרצות.\n"
"אנא סגור או בטל תחילה."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"You can only merge manufacturing orders of identical products with same BoM."
msgstr "ניתן למזג הוראות ייצור רק של מוצרים זהים עם עצי מוצרים זהים."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"You can only merge manufacturing orders with no additional components or by-"
"products."
msgstr "ניתן למזג הוראות ייצור רק אם אין רכיבים נוספים או תוצרי לוואי."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You can only merge manufacturing with the same operation type"
msgstr "ניתן למזג הזמנות ייצור שיש להן את אותו סוג פעולה."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You can only merge manufacturing with the same state."
msgstr "ניתן למזג הזמנות ייצור שנמצאות באותו השלב."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"You cannot change the workcenter of a work order that is in progress or "
"done."
msgstr "אינך יכול לשנות את תחנת העבודה של הוראת עבודה שמתבצעת או שבוצעה."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "You cannot create a new Bill of Material from here."
msgstr "לא תוכל ליצור עץ מוצר חדש מכאן."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot create cyclic dependency."
msgstr "אתה לא יכול ליצור תלות מחזורית."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You cannot delete an unbuild order if the state is 'Done'."
msgstr "אתה לא יכול למחוק הוראת פירוק אם המצב הוא 'בוצע'."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You cannot have %s  as the finished product and in the Byproducts"
msgstr "לא יכול להיות לך %s  כמו במוצר המוגמר ובמוצרי הלוואי"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot link this work order to another manufacturing order."
msgstr "אינך יכול לקשר הוראת עבודה זו להוראת ייצור אחרת."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You cannot move a manufacturing order once it is cancelled or done."
msgstr "אינך יכול להעביר הוראת ייצור ברגע שהיא מבוטלת או מבוצעת."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot produce the same serial number twice."
msgstr "אתה לא יכול לייצר את אותו מספר הסידורי פעמיים."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot start a work order that is already done or cancelled"
msgstr "לא ניתן להתחיל הוראת ייצור שהסתיימה או בוטלה "

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You cannot unbuild a undone manufacturing order."
msgstr "אתה לא יכול לפרק הוראת ייצור שלא בוצעה."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"You cannot use the 'Apply on Variant' functionality and simultaneously "
"create a BoM for a specific variant."
msgstr ""
"אינך יכול להשתמש בפונקציונליות 'החל על וריאנט' ובו זמנית ליצור עץ מוצר "
"לוריאנט ספציפי. "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid ""
"You consumed a different quantity than expected for the following products.\n"
"                        <b invisible=\"consumption == 'strict'\">\n"
"                            Please confirm it has been done on purpose.\n"
"                        </b>\n"
"                        <b invisible=\"consumption != 'strict'\">\n"
"                            Please review your component consumption or ask a manager to validate\n"
"                            <span invisible=\"mrp_production_count != 1\">this manufacturing order</span>\n"
"                            <span invisible=\"mrp_production_count == 1\">these manufacturing orders</span>.\n"
"                        </b>"
msgstr ""
"צרכת כמות שונה מהמצופה עבור המוצרים הבאים.\n"
"<b invisible=\"consumption == 'strict'\">\n"
"אנא אשר שזה בוצע בכוונה.\n"
"</b>\n"
"<b invisible=\"consumption != 'strict'\">\n"
"יש לעיין בצריכת הרכיבים שלך או לבקש ממנהל לאשר\n"
"<span invisible=\"mrp_production_count != 1\">את הזמנת הייצור הזו</span>\n"
"<span invisible=\"mrp_production_count == 1\">את הזמנות הייצור הללו.</span>\n"
"</b>"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You need at least two production orders to merge them."
msgstr "צריך לפחות שתי הזמנות ייצור על מנת לבצע פעולת מיזוג."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"You need to define at least one productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""
"עליך להגדיר לפחות הפסד יצרנות אחד בקטגוריה 'ביצועים'. צור אחד מתוך יישום "
"הייצור, תפריט: תצורה / הפסדי יצרנות."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"You need to define at least one productivity loss in the category "
"'Productivity'. Create one from the Manufacturing app, menu: Configuration /"
" Productivity Losses."
msgstr ""
"עליך להגדיר לפחות הפסד יצרנות אחד בקטגוריה 'יצרנות'. צור אחד מתוך יישום "
"הייצור, תפריט: תצורה / הפסדי יצרנות."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid ""
"You need to define at least one unactive productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""
"עליך להגדיר לפחות הפסד יצרנות אחד לא פעיל בקטגוריה 'ביצועים'. צור אחד מתוך "
"יישום הייצור, תפריט: תצורה / הפסדי יצרנות."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You need to provide a lot for the finished product."
msgstr "אתה צריך לספק מספר אצווה עבור מוצר מוגמר."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"You need to supply Lot/Serial Number for products and 'consume' them: "
"%(missing_products)s"
msgstr ""
"עליך לספק מספר אצווה/מספר סידורי עבור המוצרים ולצרוך "
"אותם:%(missing_products)s"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_backorder
msgid "You produced less than initial demand"
msgstr "ייצרת פחות מהדרישה הראשונית"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You should provide a lot number for the final product."
msgstr "אתה צריך לספק מספר אצווה עבור המוצר המוגמר."

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_quant.py:0
msgid ""
"You should update the components quantity instead of directly updating the "
"quantity of the kit product."
msgstr "עליך לעדכן את כמות הרכיבים במקום לעדכן ישירות את הכמות של מוצר בערכה."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__done_mrp_lot_label_to_print__zpl
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__generated_mrp_lot_label_to_print__zpl
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__mrp_product_label_to_print__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "cancelled"
msgstr "בוטל"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "days"
msgstr "ימים"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "days before"
msgstr "ימים לפני"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "expected duration"
msgstr "משך זמן צפוי"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "from location"
msgstr "מהמיקום "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "it is added as a component in a manufacturing order"
msgstr "הוא מתווסף כרכיב בהזמנת ייצור"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"it is moved via a transfer, such as a receipt or a delivery order for "
"instance."
msgstr "זה מועבר באמצעות העברה, כגון קבלת סחורה או הזמנת משלוח, לדוגמה."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "last"
msgstr " "

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "manufacturing order"
msgstr "הוראת ייצור"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "merged"
msgstr "ממוזג"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "minutes"
msgstr "דקות"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "of"
msgstr "של"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "ordered instead of"
msgstr "הוזמן במקום"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "per workcenter"
msgstr "פר תחנת עבודה"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "quantity has been updated."
msgstr "כמות עודכנה."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "real duration"
msgstr "משך זמן בפועל"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "split"
msgstr "פצל"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "work orders"
msgstr "הוראות העבודה האחרונות"
