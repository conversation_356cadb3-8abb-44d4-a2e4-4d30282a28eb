<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="report_stock_inventory_print">
        <t t-call="web.html_container">
            <t t-set="head_start">
                <base t-att-href="base_url"/>
            </t>
            <t t-set="head_end">
                <t t-call-assets="stock.assets_stock_print_report" t-js="False"/>
            </t>
            <t t-call='stock.report_stock_body_print'/>
        </t>
    </template>

    <template id="report_stock_body_print">
        <div class="container o_stock_reports_page o_stock_reports_no_print">
            <span class="text-start">
                <h3>
                    Traceability Report
                    <span t-if="reference" style="margin-left:28%">
                        Reference: <t t-esc="reference"/>
                    </span>
                </h3>
            </span>
            <hr/>
            <div class="o_stock_reports_table table-responsive">
                <table class="table" style="border-spacing:10px">
                    <thead>
                        <tr class="o_report_header">
                            <th class="o_report_line_header">Reference</th>
                            <th class="o_report_line_header">Product</th>
                            <th class="o_report_line_header">Date</th>
                            <th class="o_report_line_header">Lot/Serial #</th>
                            <th class="o_report_line_header">From</th>
                            <th class="o_report_line_header">To</th>
                            <th class="o_report_line_header">Quantity</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-set="space_td" t-value="'margin-left: 0'"/>
                        <t t-foreach="lines" t-as="a">
                            <t t-set="space_td" t-value="'margin-left: '+ str(a['level']) + 'px;'"/>
                            <t t-set="trclass" t-value="'o_stock_reports_default_style'"/>
                            <t t-if="a.get('model') == 'stock.move.line'"><t t-set="trclass" t-value="'o_stock_reports_level0'"/></t>
                            <t t-set="column" t-value="0" />
                            <tr t-att-data-id="a['id']" t-att-data-model="a['model']" t-att-class="trclass">
                                <t t-set="tdclass" t-value="''" />
                                <t t-if="a.get('unfoldable')"><t t-set="tdclass" t-value="'o_stock_reports_unfoldable'" /></t>
                                <t t-set="column" t-value="0" />
                                <t t-foreach="a['columns']" t-as="c">
                                    <t t-set="column" t-value="column + 1" />
                                    <td>
                                        <t t-if="column == 1">
                                            <span t-att-style="space_td"></span>
                                        </t>
                                        <span>
                                            <t t-esc="c" />
                                        </span>
                                    </td>
                                </t>
                            </tr>
                        </t>
                    </tbody>
                </table>
                <br/>
            </div>
        </div>
    </template>
</odoo>
