<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_stock_warehouse_orderpoint_kanban" model="ir.ui.view">
        <field name="name">stock.warehouse.orderpoint.kanban</field>
        <field name="model">stock.warehouse.orderpoint</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <templates>
                    <t t-name="card">
                        <div class="d-flex">
                            <field name="name" class="fw-bold fs-5 mb-1"/>
                            <span class="badge rounded-pill ms-auto mt-1"><strong>Min qty:</strong><field name="product_min_qty"/></span>
                        </div>
                        <footer class="pt-0">
                            <field name="product_id"/>
                            <span class="badge rounded-pill ms-auto"><strong>Max qty:</strong><field name="product_max_qty"/></span>
                        </footer>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="view_warehouse_orderpoint_tree_editable" model="ir.ui.view">
        <field name="name">stock.warehouse.orderpoint.list.editable</field>
        <field name="model">stock.warehouse.orderpoint</field>
        <field name="arch" type="xml">
            <list string="Reordering Rules" editable="bottom" js_class="stock_orderpoint_list" sample="1" multi_edit="1" export_xlsx="0">
                <field name="active" column_invisible="True"/>
                <field name="company_id" column_invisible="True"/>
                <field name="product_category_id" column_invisible="True"/>
                <field name="product_tmpl_id" column_invisible="True"/>
                <field name="unwanted_replenish" column_invisible="True"/>
                <field name="product_id" readonly="product_id" force_save="1" context="{'default_is_storable': True}"/>
                <field name="location_id" options="{'no_create': True}" groups="stock.group_stock_multi_locations"/>
                <field name="warehouse_id" options="{'no_create': True}" groups="stock.group_stock_multi_warehouses" optional="hide"/>
                <field name="qty_on_hand" force_save="1"/>
                <field name="qty_forecast" force_save="1"/>
                <button name="action_product_forecast_report" type="object" icon="fa-area-chart" title="Forecast Report" invisible="not id or unwanted_replenish"/>
                <button name="action_product_forecast_report" type="object" icon="fa-warning text-warning" title="Due to receipts scheduled in the future, you might end up with excessive stock . Check the Forecasted Report  before reordering" invisible="not id or not unwanted_replenish"/>
                <field name="visibility_days" optional="hidden"/>
                <field name="route_id" options="{'no_create': True, 'no_open': True}" optional="hidden"/>
                <button name="action_stock_replenishment_info" type="object" icon="fa-info-circle" title="Replenishment Information" invisible="not id"/>
                <field name="trigger" optional="hide"/>
                <field name="group_id" optional="hide" groups="stock.group_adv_location"/>
                <field name="product_min_qty" string="Min" optional="show"/>
                <field name="product_max_qty" string="Max" optional="show"/>
                <field name="qty_multiple" optional="hide"/>
                <field name="qty_to_order" readonly="trigger == 'auto'"/>
                <field name="qty_to_order_manual" column_invisible="True"/>
                <button name="action_remove_manual_qty_to_order" type="object" icon="fa-undo" title="Remove manually entered value and replace by the quantity to order based on the forecasted quantities" invisible="not qty_to_order_manual"/>
                <button name="action_remove_manual_qty_to_order" type="object" icon="fa-undo" title="-" class="disabled opacity-0" invisible="qty_to_order_manual"/>
                <field name="product_uom_name" string="UoM" groups="uom.group_uom"/>
                <field name="company_id" optional="hide" readonly="1" groups="base.group_multi_company"/>
                <button name="action_replenish" string="Order" type="object" class="o_replenish_buttons" icon="fa-truck"
                    invisible="qty_to_order &lt;= 0.0"/>
                <button name="action_replenish_auto" string="Automate" type="object" class="o_replenish_buttons" icon="fa-refresh"
                    invisible="qty_to_order &lt;= 0.0 or trigger == 'auto'"/>
                <button name="%(action_orderpoint_snooze)d" string="Snooze" type="action" class="text-warning" icon="fa-bell-slash"
                    invisible="trigger != 'manual'" context="{'default_orderpoint_ids': [id]}"/>
            </list>
        </field>
    </record>

    <record model="ir.ui.view" id="stock_reorder_report_search">
        <field name="name">stock.warehouse.orderpoint.reorder.search</field>
        <field name="model">stock.warehouse.orderpoint</field>
        <field name="arch" type="xml">
            <search string="Replenishment Report Search">
                <field name="product_id"/>
                <field name="trigger"/>
                <field name="product_category_id"/>
                <field name="group_id" groups="stock.group_adv_location"/>
                <field name="warehouse_id" groups="stock.group_stock_multi_warehouses"/>
                <field name="location_id" groups="stock.group_stock_multi_locations"/>
                <field name="name" string="Reordering Rule"/>
                <filter string="Trigger Manual" name="filter_creation_trigger" domain="[('trigger', '=', 'manual')]"/>
                <separator/>
                <filter string="To Reorder" name="filter_to_reorder" domain="[('qty_to_order', '&gt;', 0.0)]"/>
                <separator/>
                <filter string="Not Snoozed" name="filter_not_snoozed" domain="['|', ('snoozed_until', '=', False), ('snoozed_until', '&lt;=', datetime.date.today().strftime('%Y-%m-%d'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Warehouse" name="groupby_warehouse" domain="[]"  context="{'group_by': 'warehouse_id'}" groups="stock.group_stock_multi_warehouses"/>
                    <filter string="Location" name="groupby_location" domain="[]" context="{'group_by': 'location_id'}" groups="stock.group_stock_multi_locations"/>
                    <filter string="Product" name="groupby_product" domain="[]" context="{'group_by': 'product_id'}"/>
                    <filter string="Category" name="groupby_category" domain="[]" context="{'group_by': 'product_category_id'}"/>
                </group>
                <searchpanel>
                    <field name="location_id" string="Locations" groups="stock.group_stock_multi_locations" enable_counters="1"/>
                    <field name="trigger" string="Trigger" enable_counters="1"/>
                    <field name="product_category_id" icon="fa-filter" string="Category" select="multi" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>


    <record model="ir.ui.view" id="warehouse_orderpoint_search">
        <field name="name">stock.warehouse.orderpoint.search</field>
        <field name="model">stock.warehouse.orderpoint</field>
        <field name="arch" type="xml">
            <search string="Reordering Rules Search">
                <field name="product_id"/>
                <field name="name" string="Reordering Rule"/>
                <field name="trigger"/>
                <field name="group_id" groups="stock.group_adv_location"/>
                <field name="warehouse_id" groups="stock.group_stock_multi_warehouses"/>
                <field name="location_id" groups="stock.group_stock_multi_locations"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Warehouse" name="warehouse" domain="[]"  context="{'group_by': 'warehouse_id'}" groups="stock.group_stock_multi_warehouses"/>
                    <filter string="Location" name="location" domain="[]" context="{'group_by': 'location_id'}" groups="stock.group_stock_multi_locations"/>
                    <filter string="Product" name="product" domain="[]" context="{'group_by': 'product_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="view_warehouse_orderpoint_form" model="ir.ui.view">
        <field name="name">stock.warehouse.orderpoint.form</field>
        <field name="model">stock.warehouse.orderpoint</field>
        <field name="arch" type="xml">
            <form string="Reordering Rules">
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="company_id" invisible="1"/>
                            <field name="route_id" invisible="1"/>
                            <field name="product_id"/>
                            <label for="product_min_qty"/>
                            <div class="o_row">
                                <field name="product_min_qty"/>
                                <field name="product_uom_name"/>
                                <button name="stock.action_stock_replenishment_info" string="Forecast Description" type="action" icon="fa-area-chart" invisible="not id"/>
                            </div>
                            <label for="product_max_qty"/>
                            <div class="o_row">
                                <field name="product_max_qty"/>
                                <field name="product_uom_name"/>
                            </div>
                            <field name="qty_multiple" string="Quantity Multiple"/>
                        </group>
                        <group>
                            <field name="allowed_location_ids" invisible="1"/>
                            <field name="warehouse_id" options="{'no_open': True, 'no_create': True}" groups="stock.group_stock_multi_locations"/>
                            <field name="location_id" options="{'no_create': True}" groups="stock.group_stock_multi_locations" domain="[('id', 'in', allowed_location_ids)]"/>
                            <label for="group_id" groups="base.group_no_one"/>
                            <div groups="base.group_no_one">
                                <field name="group_id" groups="stock.group_adv_location"/>
                            </div>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            <field name="visibility_days"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_orderpoint_replenish" model="ir.actions.act_window">
        <field name="name">Replenishment</field>
        <field name="res_model">stock.warehouse.orderpoint</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="view_id" ref="view_warehouse_orderpoint_tree_editable"/>
        <field name="search_view_id" ref="stock_reorder_report_search"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_replenishment">
            You are good, no replenishment to perform!
          </p><p>
            You'll find here smart replenishment propositions based on inventory forecasts.
            Choose the quantity to buy or manufacture and launch orders in a click.
            To save time in the future, set the rules as "automated".
          </p>
        </field>
    </record>

    <record id="action_orderpoint" model="ir.actions.act_window">
        <field name="name">Reordering Rules</field>
        <field name="res_model">stock.warehouse.orderpoint</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="view_id" ref="view_warehouse_orderpoint_tree_editable"/>
        <field name="search_view_id" ref="warehouse_orderpoint_search"/>
        <field name="context">{'search_default_trigger': 'auto'}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            No reordering rule found
          </p><p>
            Define a minimum stock rule so that Odoo automatically creates requests for quotations or confirmed manufacturing orders to resupply your stock.
          </p>
        </field>
    </record>

    <record model="ir.actions.server" id="action_replenishment">
        <field name="name">Replenishment</field>
        <field name="model_id" ref="model_stock_warehouse_orderpoint"/>
        <field name="state">code</field>
        <field name="path">replenishment</field>
        <field name="code">
            action = model.with_context(
                search_default_filter_not_snoozed=True,
                default_trigger='manual',
                searchpanel_default_trigger='manual'
            ).action_open_orderpoints()
        </field>
    </record>


    <menuitem
        id="menu_reordering_rules_replenish"
        action="action_replenishment"
        name="Replenishment" parent="menu_stock_procurement" sequence="5"
        groups="stock.group_stock_manager"/>
</odoo>
