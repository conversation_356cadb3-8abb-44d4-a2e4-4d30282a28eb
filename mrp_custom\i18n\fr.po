# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_custom
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-21 09:41+0000\n"
"PO-Revision-Date: 2025-08-21 09:41+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: mrp_custom
#: model:ir.model.fields,field_description:mrp_custom.field_mrp_consumption_warning_line__available_lot_ids
msgid "Available Lot"
msgstr "Lot disponible"

#. module: mrp_custom
#: model:ir.model.fields,field_description:mrp_custom.field_mrp_consumption_warning_line__end_lot
msgid "End of Lot"
msgstr "Fin de Lot"

#. module: mrp_custom
#: model:ir.model,name:mrp_custom.model_mrp_consumption_warning_line
msgid "Line of issue consumption"
msgstr "Ligne de problème de consommation"

#. module: mrp_custom
#: model:ir.model.fields,field_description:mrp_custom.field_mrp_consumption_warning_line__lot_id
msgid "Lot"
msgstr "Lot"

#. module: mrp_custom
#: model:ir.model,name:mrp_custom.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr ""
"Assistant en cas de consommation en alerte/stricte et si plus de composants "
"ont été utilisés pour un OF (par rapport à la nomenclature)"

#. module: mrp_custom
#: model:ir.model.fields,field_description:mrp_custom.field_mrp_bom__product_label_id
msgid "Product label"
msgstr "Produit étiquette"

#. module: mrp_custom
#: model:ir.model.fields,help:mrp_custom.field_mrp_bom__product_label_id
msgid "Product used for label number control."
msgstr "Produit utilisé pour le contrôle du numéro d'étiquette."

#. module: mrp_custom
#. odoo-python
#: code:addons/mrp_custom/models/mrp_production.py:0
msgid ""
"The batch of the labeled product must be consumed before entering the code."
msgstr "Le lot du produit étiquette doit être consommé avant la saisie du code."

#. module: mrp_custom
#: model:ir.model.fields,field_description:mrp_custom.field_mrp_input_control__production_id
msgid "Manufacturing Orders"
msgstr "Ordres de fabrication"

#. module: mrp_custom
#: model:ir.model.fields,field_description:mrp_custom.field_mrp_input_control__last_digits
msgid "Last 4 digits"
msgstr "4 derniers chiffres"

#. module: mrp_custom
#. odoo-python
#: code:addons/mrp_custom/wizard/mrp_input_control.py:0
msgid "You must enter exactly 4 characters."
msgstr "Vous devez entrer exactement 4 caractères."

#. module: mrp_custom
#. odoo-python
#: code:addons/mrp_custom/wizard/mrp_input_control.py:0
msgid "The entered tag number is invalid. Please check and try again."
msgstr "Le numéro d'étiquette saisi est invalide. Veuillez vérifier et réessayer."

#. module: mrp_custom
#. odoo-python
#: code:addons/mrp_custom/models/mrp_production.py:0
msgid "Input control"
msgstr "Contrôle de saisie"

#. module: mrp_custom
#. odoo-python
#: code:addons/mrp_custom/wizard/mrp_input_control.py:0
msgid "No label product is defined on the BoM."
msgstr "Aucun produit d'étiquette n'est défini sur la nomenclature."

#. module: mrp_custom
#: model_terms:ir.ui.view,arch_db:mrp_custom.view_mrp_input_control_wizard_form
msgid "Validate"
msgstr "Valider"

#. module: mrp_custom
#: model_terms:ir.ui.view,arch_db:mrp_custom.view_mrp_input_control_wizard_form
msgid "Cancel"
msgstr "Annuler"

#. module: mrp_custom
#: model_terms:ir.ui.view,arch_db:mrp_custom.view_mrp_input_control_wizard_form
msgid "Enter the last 4 digits visible on the label."
msgstr "Entrez les 4 derniers chiffres visibles sur l'étiquette."

#. module: mrp_custom
#: model:ir.model.fields,field_description:mrp_custom.field_mrp_bom__is_packaging_nomenclature
msgid "Packaging Nomenclature"
msgstr "Nomenclature de conditionnement"
