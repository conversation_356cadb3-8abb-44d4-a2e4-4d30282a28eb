<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="track_move_template">
        <div>
            <t t-call="stock.message_head"/>
            <t t-call="stock.message_body"/>
        </div>
    </template>

    <template id="exception_on_picking">
        <div> Exception(s) occurred on the picking
            <a href="#" data-oe-model="stock.picking" t-att-data-oe-id="origin_picking.id"><t t-esc="origin_picking.name"/></a>.
            Manual actions may be needed.
            <div class="mt16">
                <p>Exception(s):</p>
                <ul t-foreach="moves_information" t-as="exception">
                    <t t-set="move" t-value="exception[0]"/>
                    <t t-set="new_qty" t-value="exception[1][0]"/>
                    <t t-set="old_qty" t-value="exception[1][1]"/>
                    <li><t t-esc="new_qty"/> <t t-esc="move.product_uom.name"/>
                    of <t t-esc="move.product_id.display_name"/> processed instead of <t t-esc="old_qty"/> <t t-esc="move.product_uom.name"/></li>
                </ul>
            </div>
            <div class="mt16" t-if="impacted_pickings">
                <p>Next transfer(s) impacted:</p>
                <ul t-foreach="impacted_pickings" t-as="picking">
                    <li><a href="#" data-oe-model="stock.picking" t-att-data-oe-id="picking.id"><t t-esc="picking.name"/></a></li>
                </ul>
            </div>
        </div>
    </template>
</odoo>
