# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:48+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Language-Team: Am<PERSON><PERSON> (https://app.transifex.com/odoo/teams/41243/am/)\n"
"Language: am\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__state
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_state
msgid ""
" * Draft: The MO is not confirmed yet.\n"
" * Confirmed: The MO is confirmed, the stock rules and the reordering of the components are trigerred.\n"
" * In Progress: The production has started (on the MO or on the WO).\n"
" * To Close: The production is done, the MO has to be closed.\n"
" * Done: The MO is closed, the stock moves are posted. \n"
" * Cancelled: The MO has been cancelled, can't be confirmed anymore."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid " <br/><br/> The components will be taken from <b>%s</b>."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__all_available
msgid " When all components are available"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_count
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_count
msgid "# Bill of Material"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__used_in_bom_count
msgid "# BoM Where Used"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_ready_count
msgid "# Read Work Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_count
msgid "# Work Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_template__used_in_bom_count
msgid "# of BoM Where is Used"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "%(qty)s %(measure)s unbuilt in %(order)s"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
msgid "%i work orders"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "%s (new) %s"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "%s Child MO's"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "%s cannot be deleted. Try to cancel them before."
msgstr ""

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_bom_structure
msgid "'Bom Overview - %s' % object.display_name"
msgstr ""

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_finished_product
msgid "'Finished products - %s' % object.name"
msgstr ""

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_mrp_mo_overview
msgid "'MO Overview - %s' % object.display_name"
msgstr ""

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_production_order
msgid "'Production Order - %s' % object.name"
msgstr ""

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_workorder
msgid "'Work Order - %s' % object.name"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_leg_product_template
msgid "18″ x 2½″ Square Leg"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid ": Insufficient Quantity To Unbuild"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pause\" title=\"Pause\"/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-play\" role=\"img\" aria-label=\"Run\" title=\"Run\"/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"Stop\" title=\"Stop\"/>"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Upload files to your product\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"fw-bold\">To Produce</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Backorders</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Child MO</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Load</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Lost</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "<span class=\"o_stat_text\">Manufactured</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">OEE</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "<span class=\"o_stat_text\">Operations<br/>Performance</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Overview</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Performance</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "<span class=\"o_stat_text\">Product Moves</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span class=\"o_stat_text\">Scraps</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Source MO</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Traceability</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Unbuilds</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "<span class=\"text-muted\">Modifying the quantity to produce will also modify the quantities of components to consume for this manufacturing order.</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid ""
"<span>\n"
"            Components\n"
"        </span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Actions</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "<span>Generate</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>New</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>Orders</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>PLAN ORDERS</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_report_delivery_no_kit_section
msgid "<span>Products not associated with a kit</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Reporting</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>WORK ORDERS</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span>minutes</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<strong class=\"mr8 oe_inline\">to</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Actual Duration (minutes)</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Deadline:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Description:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Duration (minutes)</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Effectiveness Category: </strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "<strong>Finished Product:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Is a Blocking Reason? </strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "<strong>Manufacturing Order:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Operation</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Product:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity Producing:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity to Produce:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Reason: </strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Responsible:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Source:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Start Date: </strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Stop Date: </strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "<strong>Unit Cost</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>WorkCenter</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Workcenter: </strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"A BoM of type Kit is not produced with a manufacturing order.<br/>\n"
"                                Instead, it is used to decompose a BoM into its components when:"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "A Manufacturing Order is already done or cancelled."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "A product with a kit-type bill of materials can not have a reordering rule."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__access_token
msgid "Access Token"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_rule__action
msgid "Action"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction
msgid "Action Needed"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__active
#: model:ir.model.fields,field_description:mrp.field_mrp_document__active
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__active
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__active
msgid "Active"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_ids
msgid "Activities"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_state
msgid "Activity State"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Add a description..."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Add a line"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Add by-products to bills of materials. This can be used to get several finished products as well. Without this option you only do: A + B = C. With the option: A + B = C + D."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Add quality checks to your work orders"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_tag
msgid "Add tag for the workcenter"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_manager
msgid "Administrator"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "All"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/controller/main.py:0
msgid "All files uploaded"
msgstr ""

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_line_bom_qty_zero
msgid ""
"All product quantities must be greater or equal to 0.\n"
"Lines with 0 quantities can be used as optional lines. \n"
"You should install the mrp_byproduct module if you want to manage extra products on BoMs!"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Allocation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_reception_report
msgid "Allocation Report for Manufacturing Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__allow_workorder_dependencies
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__allow_workorder_dependencies
msgid "Allow Work Order Dependencies"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__use_auto_consume_components_lots
#: model:ir.model.fields,help:mrp.field_stock_picking_type__use_auto_consume_components_lots
msgid "Allow automatic consumption of tracked components that are reserved"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Allow manufacturing users to modify quantities to consume, without the need for prior approval"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,help:mrp.field_stock_picking_type__use_create_components_lots
msgid "Allow to create new lot/serial numbers for the components"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__flexible
msgid "Allowed"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reserve_visible
msgid "Allowed to Reserve Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unreserve_visible
msgid "Allowed to Unreserve Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__warning
msgid "Allowed with warning"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid "Alternative Workcenters"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid "Alternative workcenters that can be substituted to this one in order to dispatch production"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid "An unbuild order is used to break down a finished product into its components."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Apply"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "Apply on Variants"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Archive Operation"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Archived"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_assign_serial_numbers_production
msgid "Assign Serial Numbers"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "Attached To"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__local_url
msgid "Attachment URL"
msgstr ""

#. module: mrp
#. odoo-python
#. odoo-javascript
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.js:0
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "Attachments"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__attachments_count
msgid "Attachments Count"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_variant_attributes
msgid "Attribute Values"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Availabilities"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Availabilities on products."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__availability
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Availability"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Availability Losses"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__available
msgid "Available"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "Avatar"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__produce_delay
msgid "Average lead time in days to manufacture this product. In the case of multi-level BOM, the manufacturing lead times of the components will be added. In case the product is subcontracted, this can be used to determine the date at which components should be sent to the subcontractor."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__variant_bom_ids
msgid "BOM Product Variants"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "BOM Product Variants needed to apply this line."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_line_ids
msgid "BOM lines of the referred bom"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder_line
msgid "Backorder Confirmation Line"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_backorder_line_ids
msgid "Backorder Confirmation Lines"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Backorder MO"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Backorder MO's"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__backorder_sequence
msgid "Backorder Sequence"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__backorder_sequence
msgid "Backorder sequence, if equals to 0 means there is not related backorder"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Barcode"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode_batch
msgid "Based on"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom
#: model:ir.model.fields,field_description:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Bill of Material"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
msgid "Bill of Material line"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_bom_id
msgid "Bill of Material used on the Production Order"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_open_bom
#: model:ir.actions.act_window,name:mrp.template_open_bom
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_ids
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Bill of Materials"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_bom_form_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom_form_action
msgid "Bills of Materials"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__mo_bom_id
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_bom_id
msgid "Bills of Materials, also called recipes, are used to autocomplete components and work order instructions."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid ""
"Bills of materials allow you to define the list of required raw\n"
"                materials used to make a finished product; through a manufacturing\n"
"                order or a pack of products."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter_wo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block Workcenter"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__blocked
msgid "Blocked"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__blocked_by_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__blocked_by_workorder_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Blocked By"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked hours over the last month"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__name
msgid "Blocking Reason"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__needed_by_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__needed_by_workorder_ids
msgid "Blocks"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_id
msgid "BoM"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_line_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_line_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "BoM Components"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Cost"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__bom_line_id
msgid "BoM Line"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__bom_line_ids
msgid "BoM Lines"
msgstr ""

#. module: mrp
#: model:ir.actions.client,name:mrp.action_report_mrp_bom
#: model:ir.actions.report,name:mrp.action_report_bom_structure
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Overview"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__type
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "BoM Type"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "BoM line product %s should not be the same as BoM product."
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_bolt_product_template
msgid "Bolt"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_byproducts
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "By-Products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_id
msgid "By-product"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "By-product %s should not be the same as BoM product."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__byproduct_id
msgid "By-product line that generated the move in a manufacturing order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__byproduct_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__byproduct_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "By-products"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
msgid "By-products cost shares must be positive."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_byproduct
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_byproduct_form_view
msgid "Byproduct"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
msgid "Byproducts"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Can't find any production location."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Cancel"
msgstr "መሰረዝ"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__cancel
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__cancel
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Cancelled"
msgstr "ተሰርዟል"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Cannot delete a manufacturing order in done state."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__default_capacity
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__capacity
msgid "Capacity"
msgstr ""

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_capacity_positive_capacity
msgid "Capacity should be a positive number."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_reported_from_previous_wo
msgid "Carried Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__loss_type
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Category"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Change Product Qty"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_change_production_qty
msgid "Change Production Qty"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_change_production_qty
msgid "Change Quantity To Produce"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Check availability"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__checksum
msgid "Checksum/SHA1"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_stop
msgid "Cleanup Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__time_stop
msgid "Cleanup Time (minutes)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__code
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view_kanban
msgid "Code"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__color
msgid "Color"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__color
msgid "Color Index"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_res_company
msgid "Companies"
msgstr "ድርጅት"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_document__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__company_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Company"
msgstr "ድርጅት"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Component"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability
msgid "Component Status"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
msgid "Component of Draft MO"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_raw_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Components"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability_state
msgid "Components Availability State"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_src_id
msgid "Components Location"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__priority
msgid "Components will be reserved first for the MO with the highest priorities."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Compute"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__auto
msgid "Compute based on tracked time"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Compute the days required to resupply all components from BoM, by either buying or manufacturing the components and/or subassemblies."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_computed_on
msgid "Computed on last"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_configuration
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Configuration"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Confirm"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__confirm_cancel
msgid "Confirm Cancel"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Confirmed"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__use_auto_consume_components_lots
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__use_auto_consume_components_lots
msgid "Consume Reserved Lots/Serial Numbers automatically"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_consumed_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Consumed"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__consume_line_ids
msgid "Consumed Disassembly Lines"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__consume_unbuild_id
msgid "Consumed Disassembly Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__operation_id
msgid "Consumed in Operation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_production__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__consumption
msgid "Consumption"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_consumption_warning
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Consumption Warning"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_production__product_uom_category_id
msgid "Conversion between Units of Measure can only occur if they belong to the same category. The conversion will be made based on the ratios."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Copy Existing Operations"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_copy_to_bom_tree_view
msgid "Copy selected operations"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__cost_share
#: model:ir.model.fields,field_description:mrp.field_stock_move__cost_share
msgid "Cost Share (%)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__costs_hour
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__costs_hour
msgid "Cost per hour"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Costing Information"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
msgid "Costs"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/views/fields/google_slides_viewer.js:0
msgid "Could not display the selected spreadsheet"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_backorder_count
msgid "Count of linked backorder"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Create Backorder"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__use_create_components_lots
msgid "Create New Lots/Serial Numbers for Components"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create a Backorder"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create a backorder if you expect to process the remaining products later. Do not create a backorder if you will not process the remaining products."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_report
msgid "Create a new manufacturing order"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid "Create a new operation"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid "Create a new work center"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_workcenter_report
msgid "Create a new work orders performance"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__days_to_prepare_mo
msgid ""
"Create and confirm Manufacturing Orders these many days in advance, to have enough time to replenish components or manufacture semi-finished products.\n"
"Note that this does not affect the MO scheduled date, which still respects the just-in-time mechanism."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create backorder"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__allow_operation_dependencies
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__allow_operation_dependencies
msgid "Create operation level dependencies that will influence both planning and the status of work orders upon MO confirmation. If this feature is ticked, and nothing is specified, Odoo will assume that all operations can be started simultaneously."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__created_production_id
msgid "Created Production Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_document__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_uid
msgid "Created by"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_document__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_date
msgid "Created on"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Creates a new serial/lot number"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "Creation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__currency_id
msgid "Currency"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_producing
msgid "Currently Produced Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_description_variants
msgid "Custom Description"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__db_datas
msgid "Database Data"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Date"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Date by Month"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_finished
msgid "Date you expect to finish production or actual date you finished production."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_start
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_date
msgid "Date you plan to start production or date you actually started production."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Date: Last 365 Days"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_pdf_line
msgid "Days"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "Days to Supply Components"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__days_to_prepare_mo
msgid "Days to prepare Manufacturing Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_deadline
msgid "Deadline"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Default Duration"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__use_manufacturing_lead
msgid "Default Manufacturing Lead Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__default_capacity
msgid "Default number of pieces (in product UoM) that can be produced in parallel (at the same time) at this work center. For example: the capacity is 5 and you need to produce 10 units, then the operation time listed on the BOM will be multiplied by two. However, note that both time before and after production will only be counted once."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid ""
"Define the components and finished products you wish to use in\n"
"                bill of materials and manufacturing orders."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__consumption
msgid ""
"Defines if you can consume more or less components than the quantity defined on the BoM:\n"
"  * Allowed: allowed for all manufacturing users.\n"
"  * Allowed with warning: allowed for all manufacturing users with summary of consumption differences when closing the manufacturing order.\n"
"  Note that in the case of component Manual Consumption, where consumption is registered manually exclusively, consumption warnings will still be issued when appropriate also.\n"
"  * Blocked: only a manager can close a manufacturing order when the BoM consumption is not respected."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delay_alert_date
msgid "Delay Alert Date"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Delete"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delivery_count
msgid "Delivery Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__description
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__description
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_note
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description"
msgstr "ማብራርያ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description of the work center..."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_dest_id
msgid "Destination Location"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unbuild_id
msgid "Disassembly Order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Discard"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_document__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__display_name
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__display_name
msgid "Display Name"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_serial_mass_produce
msgid "Display the serial mass produce wizard action"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lot_ids
msgid "Display the serial number shortcut on the moves"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.xml:0
msgid "Display:"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Displays the consumed Lot/Serial Numbers."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "Do you confirm you want to unbuild"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Document"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__is_done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Done"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Download"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__draft
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__draft
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Draft"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_drawer_drawer_product_template
msgid "Drawer Black"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_drawer_case_product_template
msgid "Drawer Case Black"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_drawer_drawer_product_template
msgid "Drawer on casters for great usability."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
msgid "Duplicate Serial Numbers (%s)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__duration
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Duration"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Duration (minutes)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode
msgid "Duration Computation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_percent
msgid "Duration Deviation (%)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_unit
msgid "Duration Per Unit"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Edit"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_type
msgid "Effectiveness"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_type
msgid "Effectiveness Category"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_finished
msgid "End"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_end
msgid "End Date"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__tracking
#: model:ir.model.fields,help:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,help:mrp.field_mrp_workorder__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason1
msgid "Equipment Failure"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Estimated %s"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s) occurred on the manufacturing order(s):"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s):"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
msgid "Existing Serial Numbers (%s)"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Exp %s"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__expected
msgid "Expected"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Expected %s"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__duration_expected
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_expected
msgid "Expected Duration"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Expected Duration (minutes)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__expected_qty
msgid "Expected Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__datas
msgid "File Content (base64)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__raw
msgid "File Content (raw)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__file_size
msgid "File Size"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Files attached to the product."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Filters"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__done
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Finished"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__order_finished_lot_id
msgid "Finished Lot/Serial Number"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_finished_ids
msgid "Finished Moves"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__finished_move_line_ids
msgid "Finished Product"
msgstr ""

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_finished_product
msgid "Finished Product Label (PDF)"
msgstr ""

#. module: mrp
#: model:ir.actions.report,name:mrp.label_manufacture_template
msgid "Finished Product Label (ZPL)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_finished_ids
msgid "Finished Products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_dest_id
msgid "Finished Products Location"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__consumption
msgid "Flexible Consumption"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.js:0
msgid "Fold"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_follower_ids
msgid "Followers"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Force"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Forecast Report"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Forecasted"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__forecasted_issue
msgid "Forecasted Issue"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Free to Use"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Free to Use / On Hand"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Free to use / On Hand"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "From"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Fully Productive"
msgstr ""

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason7
msgid "Fully Productive Time"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Future Activities"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "General Information"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Generate Serial Numbers"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
msgid "Get statistics about the work orders duration related to this routing."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__sequence
msgid "Gives the sequence order when displaying a list of routing Work Centers."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__sequence
msgid "Gives the sequence order when displaying a list of work centers."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__sequence
msgid "Gives the sequence order when displaying."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__google_slide
msgid "Google Slide"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Google Slide Link"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/views/fields/google_slides_viewer.js:0
msgid "Google Slides Viewer"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Group By"
msgstr "በመደብ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Group By..."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Group by..."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_produced
msgid "Has Been Produced"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking__has_kits
msgid "Has Kits"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_production__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_message
msgid "Has Message"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__has_routing_lines
msgid "Has Routing Lines"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__2
msgid "High"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "History"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__costs_hour
msgid "Hourly processing cost."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Hours"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_document__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__id
msgid "ID"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_id
msgid "If a product variant is defined the BOM is available only for this product."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__propagate_cancel
msgid "If checked, when the previous move of the move (which was generated by a next procurement) is cancelled or split, the move generated by this move will too"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__active
msgid "If the active field is set to False, it will allow you to hide the resource record without removing it."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_height
msgid "Image Height"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_src
msgid "Image Src"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_width
msgid "Image Width"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Image is a link"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Impacted Transfer(s):"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "Import Template for Bills of Materials"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Impossible to plan the workorder. Please check the workcenter availabilities."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__progress
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__progress
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "In Progress"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "In Transit"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__index_content
msgid "Indexed Content"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_deadline
msgid "Informative date allowing to define when the manufacturing order should be processed at the latest to fulfill delivery on time."
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_moves
msgid "Inventory Moves"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__move_line_ids
msgid "Inventory moves for which you must scan a lot number at this work order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__is_kits
#: model:ir.model.fields,field_description:mrp.field_product_template__is_kits
msgid "Is Kits"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_locked
msgid "Is Locked"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__manual
msgid "Is a Blocking Reason"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "Is a Blocking Reason?"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__public
msgid "Is public document"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_user_working
msgid "Is the Current User Working"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "It has already been unblocked."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "It is not possible to unplan one single Work Order. You should unplan the Manufacturing Order instead in order to unplan all the linked operations."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_planned
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_planned
msgid "Its Operations are Planned"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__json_popover
msgid "JSON data for the popover widget"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__key
msgid "Key"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__description_bom_line
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__description_bom_line
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__phantom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Kit"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_document__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_uid
msgid "Last Updated by"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_document__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_date
msgid "Last Updated on"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__last_working_user_id
msgid "Last user that worked on this work order."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__late
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Late"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Activities"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late MO or Late delivery of components"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__components_availability
msgid "Latest component availability status for this MO. If green, then the MO's readiness status is ready, as per BOM configuration."
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_wood_ply_product_template
msgid "Layers that are stick together to assemble wood panels."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Lead Time"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
msgid "Lead Times"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__leave_id
msgid "Leave"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning_line
msgid "Line of issue consumption"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__location_id
msgid "Location"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_id
msgid "Location where the product you want to unbuild is."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_src_id
msgid "Location where the system will look for components."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_dest_id
msgid "Location where you want to send the components resulting from the unbuild order."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Lock"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Lock the manufacturing order to prevent changes to what has been consumed or produced."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_id
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Loss Reason"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_lot
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Lot/Serial"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__lot_producing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__lot_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__finished_lot_id
msgid "Lot/Serial Number"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Lot/Serial Numbers"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_traceability
msgid "Lots/Serial Numbers"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__1
msgid "Low"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_backorder_id
msgid "MO Backorder"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "MO Cost"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "MO Costs"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "MO Generated by %s"
msgstr ""

#. module: mrp
#: model:ir.actions.client,name:mrp.action_report_mo_overview
#: model:ir.actions.report,name:mrp.action_report_mrp_mo_overview
msgid "MO Overview"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reservation_state
msgid "MO Readiness"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "MO Reference"
msgstr ""

#. module: mrp
#: model:ir.actions.client,name:mrp.mrp_reception_action
msgid "MRP Reception Report"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_routings
msgid "MRP Work Orders"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss_type
msgid "MRP Workorder productivity losses"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Make sure enough quantities of these components are reserved to do the production:\n"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_routings
msgid "Manage Work Order Operations"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__manual_consumption
#: model:ir.model.fields,field_description:mrp.field_stock_move__manual_consumption
msgid "Manual Consumption"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__manual_consumption_readonly
msgid "Manual Consumption Readonly"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid "Manual Duration"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manuf. Lead Time"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_steps
#: model:ir.model.fields.selection,name:mrp.selection__stock_rule__action__manufacture
#: model:stock.route,name:mrp.route_warehouse0_manufacture
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_stock_rule
msgid "Manufacture"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__mrp_one_step
msgid "Manufacture (1 step)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_mto_pull_id
msgid "Manufacture MTO Rule"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_pull_id
msgid "Manufacture Rule"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "Manufacture Security Lead Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__normal
msgid "Manufacture this product"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_to_resupply
msgid "Manufacture to Resupply"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__mrp_product_qty
#: model:ir.model.fields,field_description:mrp.field_product_template__mrp_product_qty
msgid "Manufactured"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "Manufactured Products"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Manufactured in the last 365 days"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__code__mrp_operation
#: model:ir.ui.menu,name:mrp.menu_mrp_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Manufacturing"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_buttons.xml:0
msgid "Manufacturing Forecast"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__produce_delay
#: model:ir.model.fields,field_description:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__manufacturing_lead
msgid "Manufacturing Lead Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manu_type_id
msgid "Manufacturing Operation Type"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__production_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Manufacturing Order"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_product_mrp_production_workcenter
#: model:ir.actions.act_window,name:mrp.action_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_production_action
#: model:ir.actions.act_window,name:mrp.mrp_production_action_picking_deshboard
#: model:ir.actions.act_window,name:mrp.mrp_production_report
#: model:ir.ui.menu,name:mrp.menu_mrp_production_action
#: model:ir.ui.menu,name:mrp.menu_mrp_production_report
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_production_calendar
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_production_pivot
msgid "Manufacturing Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are in confirmed state."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__ready_to_produce
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manufacturing Readiness"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Manufacturing Reference"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__manufacturing_visibility_days
msgid "Manufacturing Visibility Days"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc.\n"
"                They can be defined via the configuration menu."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reservation_state
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_availability
msgid ""
"Manufacturing readiness for this MO, as per bill of material configuration:\n"
"            * Ready: The material is available to start the production.\n"
"            * Waiting: The material is not available to start the production.\n"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_picking_tree_mrp_operation
#: model:ir.ui.menu,name:mrp.mrp_operation_picking
msgid "Manufacturings"
msgstr ""

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_mark_done
msgid "Mark as Done"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Mass Produce"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_mps
msgid "Master Production Schedule"
msgstr ""

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason0
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Material Availability"
msgstr ""

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_merge
msgid "Merge"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_ids
msgid "Messages"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__mimetype
msgid "Mime Type"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr ""

#. module: mrp
#. odoo-python
#. odoo-javascript
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Minutes"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Miscellaneous"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_byproduct_ids
msgid "Move Byproduct"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_consumption_warning_line_ids
msgid "Mrp Consumption Warning Line"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_procurement_group__mrp_production_ids
msgid "Mrp Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_count
msgid "Mrp Production Count"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__multiple_lot_components_names
msgid "Multiple Lot Components Names"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__my_activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__name
msgid "Name"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/stock_rule.py:0
msgid "New"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Next Activity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_calendar_event_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_summary
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "No Backorder"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid "No bill of materials found. Let's create one!"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "No data available."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_report
msgid "No data yet!"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "No manufacturing order found. Let's create one."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid "No product found. Let's create one!"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_blocked
msgid "No productivity loss for this equipment"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid "No unbuild order found"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid "No work orders to do!"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "No workorder currently in progress. Click to mark work center as blocked."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__normal
msgid "Normal"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Not Available"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "Note that archived work center(s): '%s' is/are still linked to active Bill of Materials, which means that operations can still be planned on it/them. To prevent this, deletion of the work center is recommended instead."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Note that components"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/product.py:0
msgid "Note that product(s): '%s' is/are still linked to active Bill of Materials, which means that the product can still be used on it/them."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_late
msgid "Number of Manufacturing Orders Late"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_waiting
msgid "Number of Manufacturing Orders Waiting"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_todo
msgid "Number of Manufacturing Orders to Process"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unbuild_count
msgid "Number of Unbuilds"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_child_count
msgid "Number of generated MO"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__capacity
msgid "Number of pieces that can be produced in parallel for this product."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_source_count
msgid "Number of source MO"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "OEE"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee_target
msgid "OEE Target"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee
msgid "Oee"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "On Hand"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Only manufacturing orders in either a draft or confirmed state can be %s."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Only manufacturing orders with a Bill of Materials can be %s."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Open Work Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Operation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__allow_operation_dependencies
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__allow_operation_dependencies
msgid "Operation Dependencies"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__operation_id
msgid "Operation To Consume"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__picking_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_type_id
msgid "Operation Type"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid ""
"Operation define that need to be done to realize a Work Order.\n"
"                Each operation is done at a specific Work Center and has a specific duration."
msgstr ""

#. module: mrp
#. odoo-python
#. odoo-javascript
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_operations_block/mrp_mo_overview_operations_block.xml:0
#: model:ir.actions.act_window,name:mrp.mrp_routing_action
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__allowed_operation_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_manufacturing
#: model:ir.ui.menu,name:mrp.menu_mrp_routing_action
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_operations
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_calendar
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Operations"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Done"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Planned"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Operations Search Filters"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__needed_by_operation_ids
msgid "Operations that cannot start before this operation is completed."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__blocked_by_operation_ids
msgid "Operations that need to be completed before this operation can start."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__orderpoint_id
msgid "Orderpoint"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__order_ids
msgid "Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_production
msgid "Original Production Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee_target
msgid "Overall Effective Efficiency Target in percentage"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_oee
#: model:ir.ui.menu,name:mrp.menu_mrp_workcenter_productivity_report
msgid "Overall Equipment Effectiveness"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee
msgid "Overall Equipment Effectiveness, based on the last month"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_oee
msgid "Overall Equipment Effectiveness: no working or blocked time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__pdf
msgid "PDF"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_id
msgid "Parent BoM"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__parent_product_tmpl_id
msgid "Parent Product Template"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_consumption_warning_id
msgid "Parent Wizard"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields,help:mrp.field_mrp_workorder__worksheet_google_slide
msgid "Paste the url of your Google Slide. Make sure the access to the document is public."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Pause"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Pending"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__performance
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__performance
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Performance"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Performance Losses"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__performance
msgid "Performance over the last month"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick Components"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick components and then manufacture"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm
msgid "Pick components and then manufacture (2 steps)"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm_sam
msgid "Pick components, manufacture and then store products (3 steps)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_mto_pull_id
msgid "Picking Before Manufacturing MTO Rule"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_type_id
msgid "Picking Before Manufacturing Operation Type"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_route_id
msgid "Picking Before Manufacturing Route"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking_type
msgid "Picking Type"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_ids
msgid "Picking associated to this manufacturing order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_loc_id
msgid "Picking before Manufacturing Location"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Plan"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Plan Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Plan manufacturing or purchase orders based on forecasts"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planned"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Planned at the same time as other workorder(s) at %s"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.mrp_planning_menu_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Planning"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planning Issues"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_plastic_laminate_product_template
msgid "Plastic Laminate"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_ply_product_template
msgid "Ply Layer"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_ply_veneer_product_template
msgid "Ply Veneer"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__json_popover
msgid "Popover Data JSON"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__possible_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__possible_bom_product_template_attribute_value_ids
msgid "Possible Product Template Attribute Value"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Post-Production"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pre-Production"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Print"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
msgid "Print All Variants"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__priority
#: model:ir.model.fields,field_description:mrp.field_mrp_production__priority
msgid "Priority"
msgstr ""

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason5
msgid "Process Defect"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Process operations at specific work centers"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__produce_line_ids
msgid "Processed Disassembly Lines"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_procurement_group
#: model:ir.model.fields,field_description:mrp.field_mrp_production__procurement_group_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Procurement Group"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Produce"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Produce All"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_byproducts
msgid "Produce residual products"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Produce residual products (A + B -> C + D)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_steps
msgid ""
"Produce: Move the components to the production location        directly and start the manufacturing process.\n"
"Pick / Produce: Unload        the components from the Stock to Input location first, and then        transfer it to the Production location."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "Produced"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__produced_qty
msgid "Produced Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__serial_numbers
msgid "Produced Serial Numbers"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__operation_id
msgid "Produced in Operation"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model:ir.model,name:mrp.model_product_template
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Product"
msgstr "እቃ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Product Attachments"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__capacity_ids
msgid "Product Capacities"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Product Cost"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Product Costs"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_virtual_available
msgid "Product Forecasted Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_plm
msgid "Product Lifecycle Management (PLM)"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_qty_available
msgid "Product On Hand Quantity"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Product Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tmpl_id
msgid "Product Template"
msgstr "የእቃው ማሳያ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_uom_id
msgid "Product Unit of Measure"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__product_uom_id
msgid "Product UoM"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_product_product
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_id
msgid "Product Variant"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_product_variant_action
#: model:ir.ui.menu,name:mrp.product_variant_mrp
msgid "Product Variants"
msgstr ""

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_capacity_unique_product
msgid "Product capacity should be unique for each workcenter."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Product to build..."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__production_id
msgid "Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_capacity
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_capacity
msgid "Production Capacity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_date
msgid "Production Date"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_document
msgid "Production Document"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Production Information"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_location_id
msgid "Production Location"
msgstr ""

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_production_order
#: model:ir.model,name:mrp.model_mrp_production
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__production_id
msgid "Production Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__raw_material_production_id
msgid "Production Order for components"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__production_id
msgid "Production Order for finished products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_state
msgid "Production State"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Production Workcenter"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
msgid "Production of Draft MO"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Production started late"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__production_ids
msgid "Productions To Split"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__productive
msgid "Productive"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__productive_time
msgid "Productive Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__productive_time
msgid "Productive hours over the last month"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Productivity"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_blocked
msgid "Productivity Losses"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_template_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom
#: model:ir.ui.menu,name:mrp.menu_mrp_product_form
msgid "Products"
msgstr "እቃዎች"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__progress
msgid "Progress Done (%)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__propagate_cancel
msgid "Propagate cancel and split"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__quality
msgid "Quality"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Quality Losses"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quant_ids
msgid "Quant"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_produced
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quantity
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Quantity"
msgstr "ብዛት"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_produced
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Quantity Produced"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_producing
msgid "Quantity Producing"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_remaining
msgid "Quantity To Be Produced"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__should_consume_qty
msgid "Quantity To Consume"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__quantity
msgid "Quantity To Produce"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_capacity
#: model:ir.model.fields,help:mrp.field_mrp_production_split__production_capacity
msgid "Quantity that can be produced with the current stock of components"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__rating_ids
msgid "Ratings"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_raw_ids
msgid "Raw Moves"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__assigned
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__ready
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Ready"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Ready to Produce"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__duration
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration
msgid "Real Duration"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Receipt"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Receipts"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Reception time estimation."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "Recursion error!  A product with a Bill of Material should not have itself in its BoM or child BoMs!"
msgstr ""

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason4
msgid "Reduced Speed"
msgstr ""

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason6
msgid "Reduced Yield"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__code
#: model:ir.model.fields,field_description:mrp.field_mrp_production__name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__name
msgid "Reference"
msgstr ""

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_name_uniq
msgid "Reference must be unique per Company!"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__origin
msgid "Reference of the document that generated this production order request."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Reference:"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__ir_attachment_id
msgid "Related attachment"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_workorder_popover.xml:0
msgid "Replan"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.xml:0
msgid "Replenish"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Replenish on Order (MTO)"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Replenishments"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_reporting
msgid "Reporting"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Reserved"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_id
msgid "Resource"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_field
msgid "Resource Field"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_id
msgid "Resource ID"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_model
msgid "Resource Model"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_name
msgid "Resource Name"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__user_id
msgid "Responsible"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Resupply lead time."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Route"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__routing_line_ids
msgid "Routing Lines"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
msgid "Routing Work Centers"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_procurement_compute_mrp
msgid "Run Scheduler"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__date
msgid "Schedule Date"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Schedule manufacturing orders earlier to avoid delays"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Scheduled Date"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Scheduled before the previous work order, planned from %(start)s to %(end)s"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_workorder_popover.xml:0
msgid "Scheduling Information"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
#: model:ir.model,name:mrp.model_stock_scrap
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_scrap
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Scrap"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_count
msgid "Scrap Move"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_ids
msgid "Scraps"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_screw_product_template
msgid "Screw"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Search"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Search Bill Of Material"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Search Production"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Search Work Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Search for mrp workcenter"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Security Lead Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,help:mrp.field_res_config_settings__manufacturing_lead
msgid "Security days for each manufacturing operation."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
msgid "Select Operations to Copy"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__sequence
msgid "Sequence"
msgstr "ቅደም ተከተል"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Sequence picking before manufacturing"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Sequence production"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Sequence stock after manufacturing"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Serial Mass Produce"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Set Quantities & Validate"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Set Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__manual
msgid "Set duration manually"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Set the order that work orders should be processed in. Activate the feature within each BoM's Miscellaneous tab"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_configuration
#: model:ir.ui.menu,name:mrp.menu_mrp_config
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Settings"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_start
msgid "Setup Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__time_start
msgid "Setup Time (minutes)"
msgstr ""

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason2
msgid "Setup and Adjustments"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_allocation
msgid "Show Allocation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__show_apply
msgid "Show Apply"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__show_backorders
msgid "Show Backorders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__show_bom
msgid "Show BoM column"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Show Details"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_final_lots
msgid "Show Final Lots"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lock
msgid "Show Lock/unlock buttons"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__show_json_popover
msgid "Show Popover?"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_produce
msgid "Show Produce"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_produce_all
msgid "Show Produce All"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__show_backorder_lines
msgid "Show backorder lines"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/views/fields/google_slides_viewer.xml:0
msgid "Slides viewer"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__leave_id
msgid "Slot into workcenter calendar once planned"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_head_product_template
msgid "Solid wood is a durable natural material."
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_product_template
msgid "Solid wood table."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "Some of your byproducts are tracked, you have to specify a manufacturing order in order to retrieve the correct byproducts."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "Some of your components are tracked, you have to specify a manufacturing order in order to retrieve the correct components."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Some product moves have already been confirmed, this manufacturing order can't be completely cancelled. Are you still sure you want to process?"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Some work orders are already done, you cannot unplan this manufacturing order."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Some work orders have already started, you cannot unplan this manufacturing order."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Some workorders require another workorder to be completed first"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__origin
msgid "Source"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_id
msgid "Source Location"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Specific Capacities"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__capacity_ids
msgid "Specific number of pieces that can be produced in parallel per product."
msgstr ""

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_split
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
msgid "Split"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__counter
msgid "Split #"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_detailed_vals_ids
msgid "Split Details"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__mrp_production_split_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Split Production"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split_line
msgid "Split Production Detail"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_split_multi_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Split Productions"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_split
msgid "Split production"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_split_multi
msgid "Split productions"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_screw_product_template
msgid "Stainless steel screw"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_bolt_product_template
msgid "Stainless steel screw full (dia - 5mm, Length - 10mm)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Starred"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_start
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Start"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_start
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Start Date"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__state
msgid "State"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__state
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Status"
msgstr "ሁኔታው"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_type_id
msgid "Stock After Manufacturing Operation Type"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_rule_id
msgid "Stock After Manufacturing Rule"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_assign_serial
msgid "Stock Assign Serial Numbers"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_availability
msgid "Stock Availability"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_dest_ids
msgid "Stock Movements of Produced Goods"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_unbuild_moves
msgid "Stock Moves"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_rule
msgid "Stock Rule"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_loc_id
msgid "Stock after Manufacturing Location"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Store Finished Product"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__store_fname
msgid "Stored Filename"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_bom_id
msgid "Sub BoM"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Subcontract the production of some products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_subcontracting
msgid "Subcontracting"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_product_template
msgid "Table"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_table_kit_product_template
msgid "Table Kit"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_leg_product_template
msgid "Table Leg"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_head_product_template
msgid "Table Top"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_table_kit_product_template
msgid "Table kit"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tag_ids
msgid "Tag"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__name
msgid "Tag Name"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_allocation
msgid "Technical Field used to decide whether the button \"Allocation\" should be displayed."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__has_routing_lines
msgid "Technical field for workcenter views"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_produce_all
msgid "Technical field to check if produce all button can be shown"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_produce
msgid "Technical field to check if produce button can be shown"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reserve_visible
msgid "Technical field to check when we can reserve quantities"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__text
msgid "Text"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "The Product Unit of Measure you chose has a different category than in the product form."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "The Workorder (%s) cannot be started twice!"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "The attribute value %(attribute)s set on product %(product)s does not match the BoM product %(bom_product)s."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "The capacity must be strictly positive."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "The component %s should not be the same as the product to produce."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_orderpoint.py:0
msgid "The following replenishment order has been generated"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_produced
msgid "The number of products already handled by this work order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__operation_id
msgid "The operation where the components are consumed, or the finished products created."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__cost_share
msgid "The percentage of the final production cost for this by-product line (divided between the quantity produced).The total of all by-products' cost share must be less than or equal to 100."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__cost_share
msgid "The percentage of the final production cost for this by-product. The total of all by-products' cost share must be smaller or equal to 100."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "The planned end date of the work order cannot be prior to the planned start date, please correct this to save the work order."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "The product has already been used at least once, editing its structure may lead to undesirable behaviours. You should rather archive the product and create a new one with a new bill of materials."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_reported_from_previous_wo
msgid "The quantity already produced awaiting allocation in the backorders chain."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_qty_positive
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_qty_positive
msgid "The quantity to produce must be positive!"
msgstr ""

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_unbuild_qty_positive
msgid "The quantity to unbuild must be positive!"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "The serial number %(number)s used for byproduct %(product_name)s has already been produced"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "The serial number %(number)s used for component %(component)s has already been consumed"
msgstr ""

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_tag_tag_name_unique
msgid "The tag name must be unique."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "The total cost share for a BoM's by-products cannot exceed 100."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "The total cost share for a manufacturing order's by-products cannot exceed 100."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "The work order should have already been processed."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__theme_template_id
msgid "Theme Template"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
msgid "There are more Serial Numbers than the Quantity to Produce"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "There are no components to consume. Are you still sure you want to continue?"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "There is no defined calendar on workcenter %s."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid "There's no product move yet"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__tz
msgid "This field is used in order to define in which timezone the resources will work."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_efficiency
msgid "This field is used to calculate the expected duration of a work order at this work center. For example, if a work order takes one hour and the efficiency factor is 100%, then the expected duration will be one hour. If the efficiency factor is 200%, however the expected duration will be 30 minutes."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
msgid "This is a BoM of type Kit!"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "This is the cost based on the BoM of the product. It is computed by summing the costs of the components and operations needed to build the product."
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "This is the cost defined on the product."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                You can filter on the product to see all the past movements for the product."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This production has been merge in %s"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "This production order has been created from Replenishment Report."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This serial number for product %s has already been produced"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_qty
msgid "This should be the smallest quantity that this product can be produced in. If the BOM contains operations, make sure the work center capacity is accurate."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__time_ids
msgid "Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_efficiency
msgid "Time Efficiency"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_ids
msgid "Time Logs"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Time Tracking"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Time Tracking: %(user)s"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__time_stop
msgid "Time in minutes for the cleaning."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__time_start
msgid "Time in minutes for the setup."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid "Time in minutes:- In manual mode, time used- In automatic mode, supposed first time when there aren't any work orders yet"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tz
msgid "Timezone"
msgstr ""

#. module: mrp
#: model:digest.tip,name:mrp.digest_tip_mrp_0
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid "Tip: Use tablets in the shop to control manufacturing"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "To"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__to_backorder
msgid "To Backorder"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__to_close
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Close"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_expected_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "To Consume"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Do"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "To Launch"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "To Order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "To Process"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "To Produce"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today Activities"
msgstr ""

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_wood_wear_product_template
msgid "Top layer of a wood panel."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Total Duration"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_late_count
msgid "Total Late Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_pending_count
msgid "Total Pending Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total Qty"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_qty
msgid "Total Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_progress_count
msgid "Total Running Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Total To Consume"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Total duration"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total expected duration"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__duration_expected
msgid "Total expected duration (in minutes)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total real duration"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__duration
msgid "Total real duration (in minutes)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Traceability"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_traceability_report
msgid "Traceability Report"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_tracking
msgid "Tracking"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Transfers"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__type
msgid "Type"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__code
msgid "Type of Operation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Unable to split with more than the quantity to produce."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Unblock"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__unbuild_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild Order"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_unbuild
#: model:ir.ui.menu,name:mrp.menu_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Unbuild Orders"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Unbuild: %s"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unbuild_ids
msgid "Unbuilds"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Unbuilt"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.js:0
msgid "Unfold"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Unit Cost"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unit_factor
msgid "Unit Factor"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_uom_name
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Unit of Measure"
msgstr "መለክያ"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_id
msgid "Unit of Measure (Unit of Measure) is the unit of measurement for the inventory control"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unlock"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_unlocked_by_default
msgid "Unlock Manufacturing Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unlock to adjust component demand during production, or consumed quantities once the manufacturing order is done."
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_unlocked_by_default
msgid "Unlocked by default"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unplan"
msgstr ""

#. module: mrp
#: model:ir.actions.server,name:mrp.mrp_production_action_unreserve_tree
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Unreserve"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "UoM"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/views/mrp_documents_kanban/mrp_documents_kanban_controller.xml:0
msgid "Upload"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Upload your PDF file."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__1
msgid "Urgent"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__url
msgid "Url"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "Use Manufacturing Orders (MO) to build finished products while consuming components: i.e. 1 Table = 4 Table Legs + 1 Table Top"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_workorder_dependencies
msgid "Use Operation Dependencies"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_reception_report
msgid "Use Reception Report with Manufacturing Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Used In"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__user_id
#: model:res.groups,name:mrp.group_mrp_user
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "User"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Using a MPS report to schedule your reordering and manufacturing operations is useful if you have long lead time and if you produce based on sales forecasts."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__valid_details
msgid "Valid"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Validate"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
msgid "Variant"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__3
msgid "Very High"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "View WorkOrder"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "View and allocate manufactured quantities"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse_orderpoint__manufacturing_visibility_days
msgid "Visibility Days applied on the manufacturing routes."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Waiting"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__waiting
msgid "Waiting Another Operation"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Waiting Availability"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__pending
msgid "Waiting for another WO"
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__waiting
msgid "Waiting for components"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Waiting the previous work order, planned from %(start)s to %(end)s"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse
#: model:ir.model.fields,field_description:mrp.field_mrp_production__warehouse_id
msgid "Warehouse"
msgstr ""

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
msgid "Warehouse:"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warn_insufficient_qty_unbuild
msgid "Warn Insufficient Unbuild Quantity"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/stock_scrap.py:0
msgid "Warning"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Warnings"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_wear_product_template
msgid "Wear Layer"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__website_id
msgid "Website"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__picking_type_id
msgid "When a procurement has a ‘produce’ route with a operation type set, it will try to create a Manufacturing Order for that product using a BoM of the same operation type. That allows to define stock rules which trigger different manufacturing orders with different BoMs."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__manual_consumption
#: model:ir.model.fields,help:mrp.field_stock_move__manual_consumption
msgid ""
"When activated, then the registration of consumption for that component is recorded manually exclusively.\n"
"If not activated, and any of the components consumption is edited manually on the manufacturing order, Odoo assumes manual consumption also."
msgstr ""

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__asap
msgid "When components for 1st operation are available"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_to_resupply
msgid "When products are manufactured, they can be manufactured in this warehouse."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "When products are needed in <b>%s</b>, <br/> a manufacturing order is created to fulfill the need."
msgstr ""

#. module: mrp
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid "With the Odoo work center control panel, your worker can start work orders in the shop and follow instructions of the worksheet. Quality tests are perfectly integrated into the process. Workers can trigger feedback loops, maintenance alerts, scrap products, etc."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning
msgid "Wizard in case of consumption in warning/strict and more component has been used for a MO (related to the bom)"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split_multi
msgid "Wizard to Split Multiple Productions"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split
msgid "Wizard to Split a Production"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder
msgid "Wizard to mark as done or create back order"
msgstr ""

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_panel_product_template
msgid "Wood Panel"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Work Center"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_capacity
msgid "Work Center Capacity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workcenter_load
msgid "Work Center Load"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Work Center Loads"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Work Center Name"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
msgid "Work Center load"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_action
#: model:ir.ui.menu,name:mrp.menu_view_resource_search_mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Centers"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_kanban_action
msgid "Work Centers Overview"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Work Instruction"
msgstr ""

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_workorder
#: model:ir.model,name:mrp.model_mrp_workorder
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workorder_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__name
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__workorder_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__workorder_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Work Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_workorder_dependencies
msgid "Work Order Dependencies"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Order Operations allow you to create and manage the manufacturing operations that should be followed within your work centers in order to produce a product. They are attached to bills of materials that will define the required components."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__workorder_id
msgid "Work Order To Consume"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_routing_time
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production_specific
#: model:ir.actions.act_window,name:mrp.action_work_orders
#: model:ir.actions.act_window,name:mrp.mrp_workorder_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_workorder_report
#: model:ir.actions.act_window,name:mrp.mrp_workorder_todo
#: model:ir.model.fields,field_description:mrp.field_mrp_production__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_workorder
#: model:ir.ui.menu,name:mrp.menu_mrp_work_order_report
#: model:ir.ui.menu,name:mrp.menu_mrp_workorder_todo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Orders"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workorder_workcenter_report
msgid "Work Orders Performance"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_workcenter
msgid "Work Orders Planning"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Work Sheet"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Work center"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"                    Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Work orders in progress. Click to block work center."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Workcenter"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "Workcenter %s cannot be an alternative of itself."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Workcenter Productivity"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Workcenter Productivity Loss"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss
msgid "Workcenter Productivity Losses"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__working_state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_state
msgid "Workcenter Status"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Workcenter blocked, click to unblock."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_calendar_id
msgid "Working Hours"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_user_ids
msgid "Working user on this work order."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_type
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet
msgid "Worksheet"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_type
msgid "Worksheet Type"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_google_slide
msgid "Worksheet URL"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_lot.py:0
msgid "You are not allowed to create or edit a lot or serial number for the components with the operation type \"Manufacturing\". To change this, go on the operation type and tick the box \"Create New Lots/Serial Numbers for Components\"."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__type
msgid "You can either upload a file from your computer or copy/paste an internet link to your file."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "You can not create a kit-type bill of materials for products that have at least one reordering rule."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"You can not delete a Bill of Material with running manufacturing orders.\n"
"Please close or cancel it first."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You can only merge manufacturing orders of identical products with same BoM."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You can only merge manufacturing orders with no additional components or by-products."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You can only merge manufacturing with the same operation type"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You can only merge manufacturing with the same state."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot change the workcenter of a work order that is in progress or done."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "You cannot create a new Bill of Material from here."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot create cyclic dependency."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You cannot delete an unbuild order if the state is 'Done'."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You cannot have %s  as the finished product and in the Byproducts"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot link this work order to another manufacturing order."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You cannot move a manufacturing order once it is cancelled or done."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot produce the same serial number twice."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You cannot unbuild a undone manufacturing order."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "You cannot use the 'Apply on Variant' functionality and simultaneously create a BoM for a specific variant."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid ""
"You consumed a different quantity than expected for the following products.\n"
"                        <b attrs=\"{'invisible': [('consumption', '=', 'strict')]}\">\n"
"                            Please confirm it has been done on purpose.\n"
"                        </b>\n"
"                        <b attrs=\"{'invisible': [('consumption', '!=', 'strict')]}\">\n"
"                            Please review your component consumption or ask a manager to validate\n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '!=', 1)]}\">this manufacturing order</span>\n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '=', 1)]}\">these manufacturing orders</span>.\n"
"                        </b>"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/change_production_qty.py:0
msgid "You have already processed %(quantity)s. Please input a quantity higher than %(minimum)s "
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid ""
"You have entered less serial numbers than the quantity to produce.<br/>\n"
"                        Create a backorder if you expect to process the remaining quantities later.<br/>\n"
"                        Do not create a backorder if you will not process the remaining products."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You must indicate a non-zero amount consumed for at least one of your components"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You need at least two production orders to merge them."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You need to define at least one productivity loss in the category 'Performance'. Create one from the Manufacturing app, menu: Configuration / Productivity Losses."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You need to define at least one productivity loss in the category 'Productivity'. Create one from the Manufacturing app, menu: Configuration / Productivity Losses."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "You need to define at least one unactive productivity loss in the category 'Performance'. Create one from the Manufacturing app, menu: Configuration / Productivity Losses."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You need to provide a lot for the finished product."
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_consumption_warning.py:0
msgid "You need to supply Lot/Serial Number"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You need to supply Lot/Serial Number for products:"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_backorder
msgid "You produced less than initial demand"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You should provide a lot number for the final product."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "cancelled"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "copy paste a list and/or use Generate"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "days"
msgstr "ቀኖች"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "expected duration"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "from location"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid ""
"have multiple lot reservations.<br/>\n"
"                        Do you want to confirm anyway ?"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "it is added as a component in a manufacturing order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "it is moved via a transfer, such as a receipt or a delivery order for instance."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "last"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "manufacturing order"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "merged"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "minutes"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "of"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "on"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "ordered instead of"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "per workcenter"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "quantity has been updated."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "real duration"
msgstr ""

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "split"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "work orders"
msgstr ""
