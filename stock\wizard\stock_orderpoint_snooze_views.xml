<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_stock_orderpoint_snooze" model="ir.ui.view">
        <field name="name">Stock Orderpoint Snooze</field>
        <field name="model">stock.orderpoint.snooze</field>
        <field name="arch" type="xml">
            <form string="Snooze">
                <group>
                    <field name="orderpoint_ids" invisible="1"/>
                    <field name="predefined_date" widget="radio"/>
                    <field name="snoozed_until" readonly="predefined_date != 'custom'" force_save="1"/>
                </group>
                <footer>
                    <button string="Snooze" name="action_snooze" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Discard" name="cancel_button" class="btn-secondary" special="cancel" data-hotkey="x"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_orderpoint_snooze" model="ir.actions.act_window">
        <field name="name">Snooze</field>
        <field name="res_model">stock.orderpoint.snooze</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
