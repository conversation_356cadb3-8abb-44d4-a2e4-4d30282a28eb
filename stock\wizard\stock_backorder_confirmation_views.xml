<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_backorder_confirmation" model="ir.ui.view">
        <field name="name">stock_backorder_confirmation</field>
        <field name="model">stock.backorder.confirmation</field>
        <field name="arch" type="xml">
            <form string="Backorder creation">
                <group>
                    <group>
                        <div colspan="2"><p name="explanation-text">
                            You have processed less products than the initial demand.
                        </p></div>
                    </group>
                    <group>
                        <div colspan="2" class="text-muted">
                            Create a backorder if you expect to process the remaining
                            products later. Do not create a backorder if you will not
                            process the remaining products.
                        </div>
                    </group>
                </group>

                <!-- Added to ensure a correct default_get behavior

                The wizard is always opened with default_pick_ids values in context,
                which are used to generate the backorder_confirmation_line_ids.

                To ensure default_pick_ids is correctly converted from the context
                by default_get, the field has to be present in the view.
                -->
                <field name="pick_ids" invisible="1"/>

                <field name="show_transfers" invisible="1"/>
                <field name="backorder_confirmation_line_ids" nolabel="1" invisible="not show_transfers">
                    <list create="0" delete="0" editable="top">
                        <field name="picking_id" force_save="1" readonly="1"/>
                        <field name="to_backorder" widget="boolean_toggle"/>
                    </list>
                </field>

                <footer>
                    <button name="process" string="Create Backorder" type="object" class="oe_highlight" data-hotkey="q"/>
                    <button name="process_cancel_backorder" string="No Backorder" type="object" class="btn-danger text-uppercase" invisible="show_transfers" data-hotkey="w"/>
                    <button string="Discard" class="btn-secondary" special="cancel" data-hotkey="x" />
                </footer>
            </form>
        </field>
    </record>
</odoo>
