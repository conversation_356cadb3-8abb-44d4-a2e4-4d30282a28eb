<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <template id="stock.report_return_slip">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-call="web.external_layout">
                <div class="page">
                    <div class="oe_structure"/>
                    <div class="row mt8">
                        <div class="col-8">
                            <h2>
                                <span>RETURN OF <span t-out="o.name">Default Return Name</span></span>
                            </h2>
                            <p>
                                Please put this document inside your return parcel.<br/>
                                Your parcel must be sent to this address:
                            </p>
                            <span t-field="o.location_id.warehouse_id.partner_id"
                                t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": True}'>Demo Address and Name</span>
                        </div>
                        <div class="col-4 text-center mt-4">
                            <div>
                                <span t-field="o.name" t-options="{'widget': 'barcode', 'img_style': 'width:100%;'}">Default Barcode Name</span>
                                <span t-out="o.name">Default Name</span>
                            </div>
                            <div style="margin-top:200px;">
                                <span t-out="'OBTRETU'" t-options="{'widget': 'barcode', 'img_style': 'width:100%;'}">Default OBTRETU Barcode</span>
                                <span>OBTRETU</span>
                            </div>
                        </div>
                    </div>
                    <div class="oe_structure"/>
                </div>
            </t>
            </t>
        </t>
    </template>

    <record id="return_label_report" model="ir.actions.report">
        <field name="name">Return slip</field>
        <field name="model">stock.picking</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">stock.report_return_slip</field>
        <field name="report_file">return_slip</field>
        <field name="binding_model_id" ref="model_stock_picking"/>
        <field name="binding_type">report</field>
    </record>
</odoo>
