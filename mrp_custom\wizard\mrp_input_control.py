from odoo import _, api, fields, models
from odoo.exceptions import UserError


class MrpInputControl(models.TransientModel):
    _name = "mrp.input.control"

    production_id = fields.Many2one("mrp.production", string="Manufacturing Orders")
    last_digits = fields.Char("Last 4 digits", size=4, required=True)

    # -------------------------------------------------------------------------
    # CONSTRAINTS METHODS
    # -------------------------------------------------------------------------

    @api.constrains("last_digits")
    def _check_last_digits(self):
        for rec in self:
            if rec.last_digits and len(rec.last_digits) != 4:
                raise UserError(_("You must enter exactly 4 characters."))

    # -------------------------------------------------------------------------
    # ACTION METHODS
    # -------------------------------------------------------------------------

    def action_validate(self):
        self.ensure_one()
        production = self.production_id
        bom = production.bom_id
        if not bom or not bom.product_label_id:
            raise UserError(_("No label product is defined on the BoM."))
        label_moves = production.move_raw_ids.filtered(
            lambda m: m.product_id == bom.product_label_id
        )
        lot_name = label_moves.mapped("move_line_ids.lot_id.name")
        if not lot_name:
            raise UserError(_("No lot found for the label product."))
        if not any(name and name[-4:] == self.last_digits for name in lot_name):
            raise UserError(
                _("The entered tag number is invalid. Please check and try again.")
            )
        if production.product_id.tracking != "none":
            production.action_generate_serial()
        return production.with_context(from_input_control=True).action_start()
